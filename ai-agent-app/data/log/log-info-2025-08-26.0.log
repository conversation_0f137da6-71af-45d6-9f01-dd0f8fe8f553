25-08-26.19:51:45.944 [main            ] INFO  AiModelApiTest         - Starting AiModelApiTest using Java 17.0.12 with PID 14496 (started by 30562 in D:\ACode\project\ai-agent\ai-agent-app)
25-08-26.19:51:45.947 [main            ] INFO  AiModelApiTest         - The following 1 profile is active: "dev"
25-08-26.19:51:47.098 [main            ] WARN  ClassPathMapperScanner - No MyBatis mapper was found in '[com.flyxy.ai]' package. Please check your configuration.
25-08-26.19:51:47.985 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-08-26.19:51:49.941 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store. Is empty: false
25-08-26.19:51:49.947 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store in schema: public
25-08-26.19:51:49.947 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-08-26.19:51:50.015 [main            ] INFO  AiModelApiTest         - Started AiModelApiTest in 4.785 seconds (process running for 6.647)
25-08-26.19:51:54.494 [main            ] INFO  AiModelApiTest         - 测试结果: {"metadata":{"empty":false,"id":"chatcmpl-ff192c62-ca15-937e-a204-70f6cd1771f0","model":"deepseek-v3","rateLimit":{},"usage":{"promptTokens":5,"completionTokens":63,"totalTokens":68,"nativeUsage":{"completion_tokens":63,"prompt_tokens":5,"total_tokens":68}}},"result":{"metadata":{"contentFilters":[],"empty":true,"finishReason":"STOP"},"output":{"media":[],"messageType":"ASSISTANT","metadata":{"role":"ASSISTANT","messageType":"ASSISTANT","refusal":"","finishReason":"STOP","annotations":[],"index":0,"id":"chatcmpl-ff192c62-ca15-937e-a204-70f6cd1771f0"},"text":"我是DeepSeek Chat，由深度求索公司打造的智能AI助手！✨ 我的使命是帮助你解答问题、提供信息、陪你聊天，甚至帮你处理各种文本和文件。无论是学习、工作，还是日常生活中的疑问，我都会尽力帮你解答！😊  \n\n有什么我可以帮你的吗？"{"$ref":"$.metadata.rateLimit.usage.nativeUsage.result.metadata.contentFilters.output.metadata.annotations"}}},"results":[{"$ref":"$.metadata.rateLimit.usage.nativeUsage.result"}]}
25-08-26.19:58:11.335 [main            ] INFO  AiModelApiTest         - Starting AiModelApiTest using Java 17.0.12 with PID 15604 (started by 30562 in D:\ACode\project\ai-agent\ai-agent-app)
25-08-26.19:58:11.338 [main            ] INFO  AiModelApiTest         - The following 1 profile is active: "dev"
25-08-26.19:58:12.456 [main            ] WARN  ClassPathMapperScanner - No MyBatis mapper was found in '[com.flyxy.ai]' package. Please check your configuration.
25-08-26.19:58:13.320 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-08-26.19:58:15.395 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store. Is empty: false
25-08-26.19:58:15.403 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store in schema: public
25-08-26.19:58:15.403 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-08-26.19:58:15.530 [main            ] INFO  AiModelApiTest         - Started AiModelApiTest in 4.883 seconds (process running for 6.517)
25-08-26.19:58:17.765 [ForkJoinPool.commonPool-worker-1] INFO  AiModelApiTest         - 测试结果(stream): {"media":[],"messageType":"ASSISTANT","metadata":{"role":"ASSISTANT","messageType":"ASSISTANT","finishReason":"","refusal":"","index":0,"annotations":[],"id":"chatcmpl-8826f5d8-3820-9833-8a6c-7c24a312f4b2"},"text":""{"$ref":"$.metadata.annotations"}}
25-08-26.19:58:17.766 [ForkJoinPool.commonPool-worker-1] INFO  AiModelApiTest         - 测试结果(stream): {"media":[],"messageType":"ASSISTANT","metadata":{"role":"ASSISTANT","messageType":"ASSISTANT","finishReason":"","refusal":"","index":0,"annotations":[],"id":"chatcmpl-8826f5d8-3820-9833-8a6c-7c24a312f4b2"},"text":"**"{"$ref":"$.metadata.annotations"}}
25-08-26.19:58:17.768 [ForkJoinPool.commonPool-worker-1] INFO  AiModelApiTest         - 测试结果(stream): {"media":[],"messageType":"ASSISTANT","metadata":{"role":"ASSISTANT","messageType":"ASSISTANT","finishReason":"","refusal":"","index":0,"annotations":[],"id":"chatcmpl-8826f5d8-3820-9833-8a6c-7c24a312f4b2"},"text":"Answer"{"$ref":"$.metadata.annotations"}}
25-08-26.19:58:17.768 [ForkJoinPool.commonPool-worker-1] INFO  AiModelApiTest         - 测试结果(stream): {"media":[],"messageType":"ASSISTANT","metadata":{"role":"ASSISTANT","messageType":"ASSISTANT","finishReason":"","refusal":"","index":0,"annotations":[],"id":"chatcmpl-8826f5d8-3820-9833-8a6c-7c24a312f4b2"},"text":":**  \n1"{"$ref":"$.metadata.annotations"}}
25-08-26.19:58:17.769 [ForkJoinPool.commonPool-worker-1] INFO  AiModelApiTest         - 测试结果(stream): {"media":[],"messageType":"ASSISTANT","metadata":{"role":"ASSISTANT","messageType":"ASSISTANT","finishReason":"","refusal":"","index":0,"annotations":[],"id":"chatcmpl-8826f5d8-3820-9833-8a6c-7c24a312f4b2"},"text":" + 1"{"$ref":"$.metadata.annotations"}}
25-08-26.19:58:17.826 [HttpClient-1-Worker-0] INFO  AiModelApiTest         - 测试结果(stream): {"media":[],"messageType":"ASSISTANT","metadata":{"role":"ASSISTANT","messageType":"ASSISTANT","finishReason":"","refusal":"","index":0,"annotations":[],"id":"chatcmpl-8826f5d8-3820-9833-8a6c-7c24a312f4b2"},"text":" = 2"{"$ref":"$.metadata.annotations"}}
25-08-26.19:58:17.901 [HttpClient-1-Worker-0] INFO  AiModelApiTest         - 测试结果(stream): {"media":[],"messageType":"ASSISTANT","metadata":{"role":"ASSISTANT","messageType":"ASSISTANT","finishReason":"","refusal":"","index":0,"annotations":[],"id":"chatcmpl-8826f5d8-3820-9833-8a6c-7c24a312f4b2"},"text":"  \n\nThis"{"$ref":"$.metadata.annotations"}}
25-08-26.19:58:17.958 [HttpClient-1-Worker-0] INFO  AiModelApiTest         - 测试结果(stream): {"media":[],"messageType":"ASSISTANT","metadata":{"role":"ASSISTANT","messageType":"ASSISTANT","finishReason":"","refusal":"","index":0,"annotations":[],"id":"chatcmpl-8826f5d8-3820-9833-8a6c-7c24a312f4b2"},"text":" is a basic"{"$ref":"$.metadata.annotations"}}
25-08-26.19:58:17.993 [HttpClient-1-Worker-0] INFO  AiModelApiTest         - 测试结果(stream): {"media":[],"messageType":"ASSISTANT","metadata":{"role":"ASSISTANT","messageType":"ASSISTANT","finishReason":"","refusal":"","index":0,"annotations":[],"id":"chatcmpl-8826f5d8-3820-9833-8a6c-7c24a312f4b2"},"text":" addition problem where"{"$ref":"$.metadata.annotations"}}
25-08-26.19:58:18.045 [HttpClient-1-Worker-0] INFO  AiModelApiTest         - 测试结果(stream): {"media":[],"messageType":"ASSISTANT","metadata":{"role":"ASSISTANT","messageType":"ASSISTANT","finishReason":"","refusal":"","index":0,"annotations":[],"id":"chatcmpl-8826f5d8-3820-9833-8a6c-7c24a312f4b2"},"text":" combining"{"$ref":"$.metadata.annotations"}}
25-08-26.19:58:18.117 [HttpClient-1-Worker-0] INFO  AiModelApiTest         - 测试结果(stream): {"media":[],"messageType":"ASSISTANT","metadata":{"role":"ASSISTANT","messageType":"ASSISTANT","finishReason":"","refusal":"","index":0,"annotations":[],"id":"chatcmpl-8826f5d8-3820-9833-8a6c-7c24a312f4b2"},"text":" one"{"$ref":"$.metadata.annotations"}}
25-08-26.19:58:18.171 [HttpClient-1-Worker-0] INFO  AiModelApiTest         - 测试结果(stream): {"media":[],"messageType":"ASSISTANT","metadata":{"role":"ASSISTANT","messageType":"ASSISTANT","finishReason":"","refusal":"","index":0,"annotations":[],"id":"chatcmpl-8826f5d8-3820-9833-8a6c-7c24a312f4b2"},"text":" unit with"{"$ref":"$.metadata.annotations"}}
25-08-26.19:58:18.207 [HttpClient-1-Worker-0] INFO  AiModelApiTest         - 测试结果(stream): {"media":[],"messageType":"ASSISTANT","metadata":{"role":"ASSISTANT","messageType":"ASSISTANT","finishReason":"","refusal":"","index":0,"annotations":[],"id":"chatcmpl-8826f5d8-3820-9833-8a6c-7c24a312f4b2"},"text":" another unit results"{"$ref":"$.metadata.annotations"}}
25-08-26.19:58:18.285 [HttpClient-1-Worker-0] INFO  AiModelApiTest         - 测试结果(stream): {"media":[],"messageType":"ASSISTANT","metadata":{"role":"ASSISTANT","messageType":"ASSISTANT","finishReason":"","refusal":"","index":0,"annotations":[],"id":"chatcmpl-8826f5d8-3820-9833-8a6c-7c24a312f4b2"},"text":" in two"{"$ref":"$.metadata.annotations"}}
25-08-26.19:58:18.321 [HttpClient-1-Worker-0] INFO  AiModelApiTest         - 测试结果(stream): {"media":[],"messageType":"ASSISTANT","metadata":{"role":"ASSISTANT","messageType":"ASSISTANT","finishReason":"","refusal":"","index":0,"annotations":[],"id":"chatcmpl-8826f5d8-3820-9833-8a6c-7c24a312f4b2"},"text":" units.  \n\n"{"$ref":"$.metadata.annotations"}}
25-08-26.19:58:18.401 [HttpClient-1-Worker-0] INFO  AiModelApiTest         - 测试结果(stream): {"media":[],"messageType":"ASSISTANT","metadata":{"role":"ASSISTANT","messageType":"ASSISTANT","finishReason":"","refusal":"","index":0,"annotations":[],"id":"chatcmpl-8826f5d8-3820-9833-8a6c-7c24a312f4b2"},"text":"Let me know"{"$ref":"$.metadata.annotations"}}
25-08-26.19:58:18.426 [HttpClient-1-Worker-0] INFO  AiModelApiTest         - 测试结果(stream): {"media":[],"messageType":"ASSISTANT","metadata":{"role":"ASSISTANT","messageType":"ASSISTANT","finishReason":"","refusal":"","index":0,"annotations":[],"id":"chatcmpl-8826f5d8-3820-9833-8a6c-7c24a312f4b2"},"text":" if you'd"{"$ref":"$.metadata.annotations"}}
25-08-26.19:58:18.501 [HttpClient-1-Worker-0] INFO  AiModelApiTest         - 测试结果(stream): {"media":[],"messageType":"ASSISTANT","metadata":{"role":"ASSISTANT","messageType":"ASSISTANT","finishReason":"","refusal":"","index":0,"annotations":[],"id":"chatcmpl-8826f5d8-3820-9833-8a6c-7c24a312f4b2"},"text":" like further"{"$ref":"$.metadata.annotations"}}
25-08-26.19:58:18.570 [HttpClient-1-Worker-0] INFO  AiModelApiTest         - 测试结果(stream): {"media":[],"messageType":"ASSISTANT","metadata":{"role":"ASSISTANT","messageType":"ASSISTANT","finishReason":"","refusal":"","index":0,"annotations":[],"id":"chatcmpl-8826f5d8-3820-9833-8a6c-7c24a312f4b2"},"text":" explanation"{"$ref":"$.metadata.annotations"}}
25-08-26.19:58:18.601 [HttpClient-1-Worker-0] INFO  AiModelApiTest         - 测试结果(stream): {"media":[],"messageType":"ASSISTANT","metadata":{"role":"ASSISTANT","messageType":"ASSISTANT","finishReason":"","refusal":"","index":0,"annotations":[],"id":"chatcmpl-8826f5d8-3820-9833-8a6c-7c24a312f4b2"},"text":"! 😊"{"$ref":"$.metadata.annotations"}}
25-08-26.19:58:18.602 [HttpClient-1-Worker-0] INFO  AiModelApiTest         - 测试结果(stream): {"media":[],"messageType":"ASSISTANT","metadata":{"role":"ASSISTANT","messageType":"ASSISTANT","finishReason":"STOP","refusal":"","index":0,"annotations":[],"id":"chatcmpl-8826f5d8-3820-9833-8a6c-7c24a312f4b2"},"text":""{"$ref":"$.metadata.annotations"}}
25-08-26.19:58:18.602 [HttpClient-1-Worker-0] INFO  AiModelApiTest         - 测试结果(stream): done!
25-08-26.20:08:05.368 [main            ] INFO  AiModelApiTest         - Starting AiModelApiTest using Java 17.0.12 with PID 31660 (started by 30562 in D:\ACode\project\ai-agent\ai-agent-app)
25-08-26.20:08:05.372 [main            ] INFO  AiModelApiTest         - The following 1 profile is active: "dev"
25-08-26.20:08:06.500 [main            ] WARN  ClassPathMapperScanner - No MyBatis mapper was found in '[com.flyxy.ai]' package. Please check your configuration.
25-08-26.20:08:07.323 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-08-26.20:08:09.123 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store. Is empty: false
25-08-26.20:08:09.130 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store in schema: public
25-08-26.20:08:09.130 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-08-26.20:08:09.205 [main            ] INFO  AiModelApiTest         - Started AiModelApiTest in 4.532 seconds (process running for 6.231)
25-08-26.20:08:59.161 [main            ] INFO  AiModelApiTest         - Starting AiModelApiTest using Java 17.0.12 with PID 22584 (started by 30562 in D:\ACode\project\ai-agent\ai-agent-app)
25-08-26.20:08:59.164 [main            ] INFO  AiModelApiTest         - The following 1 profile is active: "dev"
25-08-26.20:09:00.277 [main            ] WARN  ClassPathMapperScanner - No MyBatis mapper was found in '[com.flyxy.ai]' package. Please check your configuration.
25-08-26.20:09:01.064 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-08-26.20:09:02.949 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store. Is empty: false
25-08-26.20:09:02.955 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store in schema: public
25-08-26.20:09:02.955 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-08-26.20:09:03.027 [main            ] INFO  AiModelApiTest         - Started AiModelApiTest in 4.522 seconds (process running for 6.028)
25-08-26.20:09:08.540 [main            ] INFO  AiModelApiTest         - 测试结果: {"metadata":{"empty":false,"id":"chatcmpl-ee79e462-fb34-9ff6-856e-5334a36e274a","model":"deepseek-v3","rateLimit":{},"usage":{"promptTokens":9,"completionTokens":129,"totalTokens":138,"nativeUsage":{"completion_tokens":129,"prompt_tokens":9,"total_tokens":138}}},"result":{"metadata":{"contentFilters":[],"empty":true,"finishReason":"STOP"},"output":{"media":[],"messageType":"ASSISTANT","metadata":{"role":"ASSISTANT","messageType":"ASSISTANT","refusal":"","finishReason":"STOP","index":0,"annotations":[],"id":"chatcmpl-ee79e462-fb34-9ff6-856e-5334a36e274a"},"text":"目前我无法直接查看或分析图片中的内容，但如果你能提供图片的详细描述（如场景、物体、文字、颜色等关键信息），我可以根据这些信息帮你整理、总结或描绘图片中的内容。  \n\n例如，你可以告诉我：  \n- 图片中是否有文字？具体内容是什么？  \n- 主要物体或人物是什么？它们的姿态、位置如何？  \n- 场景是自然风光、城市建筑还是其他类型？  \n- 整体色调或风格（如明亮、暗沉、卡通、写实等）？  \n\n提供这些细节后，我可以帮你生成更准确的描述或分析！"{"$ref":"$.metadata.rateLimit.usage.nativeUsage.result.metadata.contentFilters.output.metadata.annotations"}}},"results":[{"$ref":"$.metadata.rateLimit.usage.nativeUsage.result"}]}
25-08-26.20:11:04.098 [main            ] INFO  AiModelApiTest         - Starting AiModelApiTest using Java 17.0.12 with PID 19292 (started by 30562 in D:\ACode\project\ai-agent\ai-agent-app)
25-08-26.20:11:04.101 [main            ] INFO  AiModelApiTest         - The following 1 profile is active: "dev"
25-08-26.20:11:05.263 [main            ] WARN  ClassPathMapperScanner - No MyBatis mapper was found in '[com.flyxy.ai]' package. Please check your configuration.
25-08-26.20:11:06.072 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-08-26.20:11:07.872 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store. Is empty: false
25-08-26.20:11:07.878 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store in schema: public
25-08-26.20:11:07.879 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-08-26.20:11:07.951 [main            ] INFO  AiModelApiTest         - Started AiModelApiTest in 4.54 seconds (process running for 6.144)
25-08-26.20:11:12.588 [main            ] INFO  AiModelApiTest         - 测试结果: {"metadata":{"empty":false,"id":"chatcmpl-f74d30ee-61b5-9b99-9881-bcbe135187bd","model":"qwen-plus","rateLimit":{},"usage":{"promptTokens":17,"completionTokens":54,"totalTokens":71,"nativeUsage":{"completion_tokens":54,"prompt_tokens":17,"prompt_tokens_details":{"cached_tokens":0},"total_tokens":71}}},"result":{"metadata":{"contentFilters":[],"empty":true,"finishReason":"STOP"},"output":{"media":[],"messageType":"ASSISTANT","metadata":{"role":"ASSISTANT","messageType":"ASSISTANT","refusal":"","finishReason":"STOP","annotations":[],"index":0,"id":"chatcmpl-f74d30ee-61b5-9b99-9881-bcbe135187bd"},"text":"您好！我目前无法直接查看或分析图片。如果您能描述图片的内容或提供相关信息，我将很乐意帮助您解答问题或提供相关的信息。如果您有图片中的具体场景、物体或任何细节的描述，请告诉我，我会尽力协助您！"{"$ref":"$.metadata.rateLimit.usage.nativeUsage.prompt_tokens_details.result.metadata.contentFilters.output.metadata.annotations"}}},"results":[{"$ref":"$.metadata.rateLimit.usage.nativeUsage.prompt_tokens_details.result"}]}
25-08-26.20:14:24.530 [main            ] INFO  AiModelApiTest         - Starting AiModelApiTest using Java 17.0.12 with PID 26880 (started by 30562 in D:\ACode\project\ai-agent\ai-agent-app)
25-08-26.20:14:24.533 [main            ] INFO  AiModelApiTest         - The following 1 profile is active: "dev"
25-08-26.20:14:25.635 [main            ] WARN  ClassPathMapperScanner - No MyBatis mapper was found in '[com.flyxy.ai]' package. Please check your configuration.
25-08-26.20:14:26.486 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-08-26.20:14:28.384 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store. Is empty: false
25-08-26.20:14:28.392 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store in schema: public
25-08-26.20:14:28.392 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-08-26.20:14:28.480 [main            ] INFO  AiModelApiTest         - Started AiModelApiTest in 4.654 seconds (process running for 6.238)
25-08-26.20:14:34.561 [main            ] INFO  AiModelApiTest         - 测试结果: {"metadata":{"empty":false,"id":"chatcmpl-0b92b90e-273f-964f-9835-3ac6e7868a8f","model":"qwen-vl-plus","rateLimit":{},"usage":{"promptTokens":339,"completionTokens":277,"totalTokens":616,"nativeUsage":{"completion_tokens":277,"prompt_tokens":339,"prompt_tokens_details":{"cached_tokens":0},"total_tokens":616}}},"result":{"metadata":{"contentFilters":[],"empty":true,"finishReason":"STOP"},"output":{"media":[],"messageType":"ASSISTANT","metadata":{"role":"ASSISTANT","messageType":"ASSISTANT","finishReason":"STOP","refusal":"","index":0,"annotations":[],"id":"chatcmpl-0b92b90e-273f-964f-9835-3ac6e7868a8f"},"text":"这是一张卡通风格的图片，描绘了一只可爱的狗狗。以下是对图片内容的详细描述：\n\n1. **颜色和外观**：\n   - 狗狗的身体主要是棕色的。\n   - 它的脸部有一些白色的斑点，嘴巴周围也有白色的区域。\n   - 眼睛是大大的白色圆圈，中间有黑色的瞳孔，显得非常突出和可爱。\n   - 眼睛上方有两条细长的眉毛，看起来有些无精打采或困倦。\n   - 鼻子是深棕色的，形状像一个椭圆形。\n\n2. **表情和姿态**：\n   - 狗狗的表情看起来有些疲惫或困倦，眼睛半闭，嘴巴微微张开，露出粉红色的舌头。\n   - 它的耳朵竖立着，耳朵的内侧也是棕色的。\n   - 尾巴向上翘起，末端稍微弯曲，显示出一种放松的状态。\n\n3. **细节**：\n   - 狗狗的四肢短而粗壮，脚掌是棕色的。\n   - 身体上还有一些小斑点，增加了它的可爱感。\n\n总体来说，这是一幅非常温馨和可爱的卡通狗狗插画，给人一种温暖和亲切的感觉。"{"$ref":"$.metadata.rateLimit.usage.nativeUsage.prompt_tokens_details.result.metadata.contentFilters.output.metadata.annotations"}}},"results":[{"$ref":"$.metadata.rateLimit.usage.nativeUsage.prompt_tokens_details.result"}]}
25-08-26.20:40:14.069 [main            ] INFO  AiModelApiTest         - Starting AiModelApiTest using Java 17.0.12 with PID 17452 (started by 30562 in D:\ACode\project\ai-agent\ai-agent-app)
25-08-26.20:40:14.071 [main            ] INFO  AiModelApiTest         - The following 1 profile is active: "dev"
25-08-26.20:40:15.194 [main            ] WARN  ClassPathMapperScanner - No MyBatis mapper was found in '[com.flyxy.ai]' package. Please check your configuration.
25-08-26.20:40:16.022 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-08-26.20:40:17.851 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store. Is empty: false
25-08-26.20:40:17.860 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store in schema: public
25-08-26.20:40:17.860 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-08-26.20:40:17.960 [main            ] INFO  AiModelApiTest         - Started AiModelApiTest in 4.569 seconds (process running for 6.266)
25-08-26.20:40:20.363 [main            ] INFO  TextSplitter           - Splitting up document into 410 chunks.
25-08-26.20:40:20.808 [main            ] WARN  SpringAiRetryAutoConfiguration - Retry error. Retry count: 1, Exception: HTTP 404 - {"error":{"message":"The model `text-embedding-ada-002` does not exist or you do not have access to it.","type":"invalid_request_error","param":null,"code":"model_not_found"},"request_id":"69a94634-7528-9763-b8ab-90a382cb70d0"}
org.springframework.ai.retry.NonTransientAiException: HTTP 404 - {"error":{"message":"The model `text-embedding-ada-002` does not exist or you do not have access to it.","type":"invalid_request_error","param":null,"code":"model_not_found"},"request_id":"69a94634-7528-9763-b8ab-90a382cb70d0"}
	at org.springframework.ai.retry.autoconfigure.SpringAiRetryAutoConfiguration$2.handleError(SpringAiRetryAutoConfiguration.java:109)
	at org.springframework.web.client.ResponseErrorHandler.handleError(ResponseErrorHandler.java:58)
	at org.springframework.web.client.StatusHandler.lambda$fromErrorHandler$1(StatusHandler.java:71)
	at org.springframework.web.client.StatusHandler.handle(StatusHandler.java:146)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.applyStatusHandlers(DefaultRestClient.java:826)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.lambda$readBody$4(DefaultRestClient.java:815)
	at org.springframework.web.client.DefaultRestClient.readWithMessageConverters(DefaultRestClient.java:215)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.readBody(DefaultRestClient.java:814)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.lambda$toEntityInternal$2(DefaultRestClient.java:770)
	at org.springframework.web.client.DefaultRestClient$DefaultRequestBodyUriSpec.exchangeInternal(DefaultRestClient.java:574)
	at org.springframework.web.client.DefaultRestClient$DefaultRequestBodyUriSpec.exchange(DefaultRestClient.java:535)
	at org.springframework.web.client.RestClient$RequestHeadersSpec.exchange(RestClient.java:677)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.executeAndExtract(DefaultRestClient.java:809)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.toEntityInternal(DefaultRestClient.java:769)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.toEntity(DefaultRestClient.java:765)
	at org.springframework.ai.openai.api.OpenAiApi.embeddings(OpenAiApi.java:294)
	at org.springframework.ai.openai.OpenAiEmbeddingModel.lambda$call$1(OpenAiEmbeddingModel.java:168)
	at org.springframework.retry.support.RetryTemplate.doExecute(RetryTemplate.java:357)
	at org.springframework.retry.support.RetryTemplate.execute(RetryTemplate.java:230)
	at org.springframework.ai.openai.OpenAiEmbeddingModel.lambda$call$3(OpenAiEmbeddingModel.java:168)
	at io.micrometer.observation.Observation.observe(Observation.java:564)
	at org.springframework.ai.openai.OpenAiEmbeddingModel.call(OpenAiEmbeddingModel.java:166)
	at org.springframework.ai.embedding.EmbeddingModel.embed(EmbeddingModel.java:67)
	at org.springframework.ai.embedding.EmbeddingModel.embed(EmbeddingModel.java:49)
	at org.springframework.ai.embedding.AbstractEmbeddingModel.dimensions(AbstractEmbeddingModel.java:70)
	at org.springframework.ai.embedding.AbstractEmbeddingModel.dimensions(AbstractEmbeddingModel.java:95)
	at org.springframework.ai.vectorstore.pgvector.PgVectorStore.embeddingDimensions(PgVectorStore.java:489)
	at org.springframework.ai.vectorstore.pgvector.PgVectorStore.createObservationContextBuilder(PgVectorStore.java:506)
	at org.springframework.ai.vectorstore.observation.AbstractObservationVectorStore.add(AbstractObservationVectorStore.java:79)
	at org.springframework.ai.vectorstore.VectorStore.accept(VectorStore.java:56)
	at com.flyxy.ai.test.AiModelApiTest.rag_upload_test(AiModelApiTest.java:128)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.springframework.test.context.junit4.statements.RunBeforeTestExecutionCallbacks.evaluate(RunBeforeTestExecutionCallbacks.java:76)
	at org.springframework.test.context.junit4.statements.RunAfterTestExecutionCallbacks.evaluate(RunAfterTestExecutionCallbacks.java:84)
	at org.springframework.test.context.junit4.statements.RunBeforeTestMethodCallbacks.evaluate(RunBeforeTestMethodCallbacks.java:75)
	at org.springframework.test.context.junit4.statements.RunAfterTestMethodCallbacks.evaluate(RunAfterTestMethodCallbacks.java:86)
	at org.springframework.test.context.junit4.statements.SpringRepeat.evaluate(SpringRepeat.java:84)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:252)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:97)
	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at org.springframework.test.context.junit4.statements.RunBeforeTestClassCallbacks.evaluate(RunBeforeTestClassCallbacks.java:61)
	at org.springframework.test.context.junit4.statements.RunAfterTestClassCallbacks.evaluate(RunAfterTestClassCallbacks.java:70)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.run(SpringJUnit4ClassRunner.java:191)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
	at com.intellij.junit4.JUnit4IdeaTestRunner.startRunnerWithArgs(JUnit4IdeaTestRunner.java:69)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater$1.execute(IdeaTestRunner.java:38)
	at com.intellij.rt.execution.junit.TestsRepeater.repeat(TestsRepeater.java:11)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:35)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:231)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:55)
25-08-26.20:40:20.810 [main            ] WARN  PgVectorStore          - Failed to obtain the embedding dimensions from the embedding model and fall backs to default:1536
org.springframework.ai.retry.NonTransientAiException: HTTP 404 - {"error":{"message":"The model `text-embedding-ada-002` does not exist or you do not have access to it.","type":"invalid_request_error","param":null,"code":"model_not_found"},"request_id":"69a94634-7528-9763-b8ab-90a382cb70d0"}
	at org.springframework.ai.retry.autoconfigure.SpringAiRetryAutoConfiguration$2.handleError(SpringAiRetryAutoConfiguration.java:109)
	at org.springframework.web.client.ResponseErrorHandler.handleError(ResponseErrorHandler.java:58)
	at org.springframework.web.client.StatusHandler.lambda$fromErrorHandler$1(StatusHandler.java:71)
	at org.springframework.web.client.StatusHandler.handle(StatusHandler.java:146)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.applyStatusHandlers(DefaultRestClient.java:826)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.lambda$readBody$4(DefaultRestClient.java:815)
	at org.springframework.web.client.DefaultRestClient.readWithMessageConverters(DefaultRestClient.java:215)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.readBody(DefaultRestClient.java:814)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.lambda$toEntityInternal$2(DefaultRestClient.java:770)
	at org.springframework.web.client.DefaultRestClient$DefaultRequestBodyUriSpec.exchangeInternal(DefaultRestClient.java:574)
	at org.springframework.web.client.DefaultRestClient$DefaultRequestBodyUriSpec.exchange(DefaultRestClient.java:535)
	at org.springframework.web.client.RestClient$RequestHeadersSpec.exchange(RestClient.java:677)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.executeAndExtract(DefaultRestClient.java:809)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.toEntityInternal(DefaultRestClient.java:769)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.toEntity(DefaultRestClient.java:765)
	at org.springframework.ai.openai.api.OpenAiApi.embeddings(OpenAiApi.java:294)
	at org.springframework.ai.openai.OpenAiEmbeddingModel.lambda$call$1(OpenAiEmbeddingModel.java:168)
	at org.springframework.retry.support.RetryTemplate.doExecute(RetryTemplate.java:357)
	at org.springframework.retry.support.RetryTemplate.execute(RetryTemplate.java:230)
	at org.springframework.ai.openai.OpenAiEmbeddingModel.lambda$call$3(OpenAiEmbeddingModel.java:168)
	at io.micrometer.observation.Observation.observe(Observation.java:564)
	at org.springframework.ai.openai.OpenAiEmbeddingModel.call(OpenAiEmbeddingModel.java:166)
	at org.springframework.ai.embedding.EmbeddingModel.embed(EmbeddingModel.java:67)
	at org.springframework.ai.embedding.EmbeddingModel.embed(EmbeddingModel.java:49)
	at org.springframework.ai.embedding.AbstractEmbeddingModel.dimensions(AbstractEmbeddingModel.java:70)
	at org.springframework.ai.embedding.AbstractEmbeddingModel.dimensions(AbstractEmbeddingModel.java:95)
	at org.springframework.ai.vectorstore.pgvector.PgVectorStore.embeddingDimensions(PgVectorStore.java:489)
	at org.springframework.ai.vectorstore.pgvector.PgVectorStore.createObservationContextBuilder(PgVectorStore.java:506)
	at org.springframework.ai.vectorstore.observation.AbstractObservationVectorStore.add(AbstractObservationVectorStore.java:79)
	at org.springframework.ai.vectorstore.VectorStore.accept(VectorStore.java:56)
	at com.flyxy.ai.test.AiModelApiTest.rag_upload_test(AiModelApiTest.java:128)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.springframework.test.context.junit4.statements.RunBeforeTestExecutionCallbacks.evaluate(RunBeforeTestExecutionCallbacks.java:76)
	at org.springframework.test.context.junit4.statements.RunAfterTestExecutionCallbacks.evaluate(RunAfterTestExecutionCallbacks.java:84)
	at org.springframework.test.context.junit4.statements.RunBeforeTestMethodCallbacks.evaluate(RunBeforeTestMethodCallbacks.java:75)
	at org.springframework.test.context.junit4.statements.RunAfterTestMethodCallbacks.evaluate(RunAfterTestMethodCallbacks.java:86)
	at org.springframework.test.context.junit4.statements.SpringRepeat.evaluate(SpringRepeat.java:84)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:252)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:97)
	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at org.springframework.test.context.junit4.statements.RunBeforeTestClassCallbacks.evaluate(RunBeforeTestClassCallbacks.java:61)
	at org.springframework.test.context.junit4.statements.RunAfterTestClassCallbacks.evaluate(RunAfterTestClassCallbacks.java:70)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.run(SpringJUnit4ClassRunner.java:191)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
	at com.intellij.junit4.JUnit4IdeaTestRunner.startRunnerWithArgs(JUnit4IdeaTestRunner.java:69)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater$1.execute(IdeaTestRunner.java:38)
	at com.intellij.rt.execution.junit.TestsRepeater.repeat(TestsRepeater.java:11)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:35)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:231)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:55)
25-08-26.20:42:39.884 [main            ] INFO  AiModelApiTest         - Starting AiModelApiTest using Java 17.0.12 with PID 29900 (started by 30562 in D:\ACode\project\ai-agent\ai-agent-app)
25-08-26.20:42:39.888 [main            ] INFO  AiModelApiTest         - The following 1 profile is active: "dev"
25-08-26.20:42:40.966 [main            ] WARN  ClassPathMapperScanner - No MyBatis mapper was found in '[com.flyxy.ai]' package. Please check your configuration.
25-08-26.20:42:41.741 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-08-26.20:42:43.605 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store. Is empty: false
25-08-26.20:42:43.613 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store in schema: public
25-08-26.20:42:43.613 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-08-26.20:42:43.696 [main            ] INFO  AiModelApiTest         - Started AiModelApiTest in 4.497 seconds (process running for 6.246)
25-08-26.20:42:45.908 [main            ] INFO  TextSplitter           - Splitting up document into 410 chunks.
25-08-26.20:49:11.251 [main            ] INFO  AiModelApiTest         - Starting AiModelApiTest using Java 17.0.12 with PID 27628 (started by 30562 in D:\ACode\project\ai-agent\ai-agent-app)
25-08-26.20:49:11.254 [main            ] INFO  AiModelApiTest         - The following 1 profile is active: "dev"
25-08-26.20:49:12.414 [main            ] WARN  ClassPathMapperScanner - No MyBatis mapper was found in '[com.flyxy.ai]' package. Please check your configuration.
25-08-26.20:49:13.254 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-08-26.20:49:15.236 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store. Is empty: false
25-08-26.20:49:15.242 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store in schema: public
25-08-26.20:49:15.242 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-08-26.20:49:15.312 [main            ] INFO  AiModelApiTest         - Started AiModelApiTest in 4.779 seconds (process running for 6.459)
25-08-26.20:49:15.337 [main            ] WARN  TestContextManager     - Caught exception while allowing TestExecutionListener [org.springframework.test.context.support.DependencyInjectionTestExecutionListener] to prepare test instance [com.flyxy.ai.test.AiModelApiTest@76278f70]
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'com.flyxy.ai.test.AiModelApiTest': Injection of resource dependencies failed
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:372)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1445)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireBeanProperties(AbstractAutowireCapableBeanFactory.java:399)
	at org.springframework.test.context.support.DependencyInjectionTestExecutionListener.injectDependencies(DependencyInjectionTestExecutionListener.java:156)
	at org.springframework.test.context.support.DependencyInjectionTestExecutionListener.prepareTestInstance(DependencyInjectionTestExecutionListener.java:111)
	at org.springframework.test.context.TestContextManager.prepareTestInstance(TestContextManager.java:260)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.createTest(SpringJUnit4ClassRunner.java:228)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner$1.runReflectiveCall(SpringJUnit4ClassRunner.java:289)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.methodBlock(SpringJUnit4ClassRunner.java:291)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:247)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:97)
	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at org.springframework.test.context.junit4.statements.RunBeforeTestClassCallbacks.evaluate(RunBeforeTestClassCallbacks.java:61)
	at org.springframework.test.context.junit4.statements.RunAfterTestClassCallbacks.evaluate(RunAfterTestClassCallbacks.java:70)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.run(SpringJUnit4ClassRunner.java:191)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
	at com.intellij.junit4.JUnit4IdeaTestRunner.startRunnerWithArgs(JUnit4IdeaTestRunner.java:69)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater$1.execute(IdeaTestRunner.java:38)
	at com.intellij.rt.execution.junit.TestsRepeater.repeat(TestsRepeater.java:11)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:35)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:231)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:55)
Caused by: org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'org.springframework.ai.transformer.splitter.TokenTextSplitter' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {@jakarta.annotation.Resource(shareable=true, lookup="", name="", description="", authenticationType=CONTAINER, type=java.lang.Object.class, mappedName="")}
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.raiseNoMatchingBeanFound(DefaultListableBeanFactory.java:2177)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1627)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1552)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:599)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:577)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:739)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:272)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:369)
	... 28 common frames omitted
25-08-26.20:53:56.825 [main            ] INFO  AiModelApiTest         - Starting AiModelApiTest using Java 17.0.12 with PID 2052 (started by 30562 in D:\ACode\project\ai-agent\ai-agent-app)
25-08-26.20:53:56.827 [main            ] INFO  AiModelApiTest         - The following 1 profile is active: "dev"
25-08-26.20:53:57.906 [main            ] WARN  ClassPathMapperScanner - No MyBatis mapper was found in '[com.flyxy.ai]' package. Please check your configuration.
25-08-26.20:53:58.647 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-08-26.20:54:00.630 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store. Is empty: false
25-08-26.20:54:00.643 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store in schema: public
25-08-26.20:54:00.643 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-08-26.20:54:00.735 [main            ] INFO  AiModelApiTest         - Started AiModelApiTest in 4.576 seconds (process running for 6.224)
25-08-26.20:54:03.023 [main            ] INFO  TextSplitter           - Splitting up document into 410 chunks.
25-08-26.20:56:42.624 [main            ] INFO  AiModelApiTest         - Starting AiModelApiTest using Java 17.0.12 with PID 32528 (started by 30562 in D:\ACode\project\ai-agent\ai-agent-app)
25-08-26.20:56:42.625 [main            ] INFO  AiModelApiTest         - The following 1 profile is active: "dev"
25-08-26.20:56:43.804 [main            ] WARN  ClassPathMapperScanner - No MyBatis mapper was found in '[com.flyxy.ai]' package. Please check your configuration.
25-08-26.20:56:44.632 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-08-26.20:56:46.658 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store. Is empty: false
25-08-26.20:56:46.665 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store in schema: public
25-08-26.20:56:46.665 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-08-26.20:56:46.743 [main            ] INFO  AiModelApiTest         - Started AiModelApiTest in 4.838 seconds (process running for 6.592)
25-08-26.20:56:49.141 [main            ] INFO  TextSplitter           - Splitting up document into 663 chunks.
25-08-26.20:59:11.878 [main            ] INFO  AiModelApiTest         - Starting AiModelApiTest using Java 17.0.12 with PID 22672 (started by 30562 in D:\ACode\project\ai-agent\ai-agent-app)
25-08-26.20:59:11.880 [main            ] INFO  AiModelApiTest         - The following 1 profile is active: "dev"
25-08-26.20:59:13.152 [main            ] WARN  ClassPathMapperScanner - No MyBatis mapper was found in '[com.flyxy.ai]' package. Please check your configuration.
25-08-26.20:59:14.000 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-08-26.20:59:15.956 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store. Is empty: false
25-08-26.20:59:15.962 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store in schema: public
25-08-26.20:59:15.962 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-08-26.20:59:16.045 [main            ] INFO  AiModelApiTest         - Started AiModelApiTest in 4.871 seconds (process running for 6.602)
25-08-26.20:59:18.406 [main            ] INFO  TextSplitter           - Splitting up document into 663 chunks.
25-08-26.21:01:05.303 [main            ] INFO  AiModelApiTest         - Starting AiModelApiTest using Java 17.0.12 with PID 23580 (started by 30562 in D:\ACode\project\ai-agent\ai-agent-app)
25-08-26.21:01:05.305 [main            ] INFO  AiModelApiTest         - The following 1 profile is active: "dev"
25-08-26.21:01:06.489 [main            ] WARN  ClassPathMapperScanner - No MyBatis mapper was found in '[com.flyxy.ai]' package. Please check your configuration.
25-08-26.21:01:07.287 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-08-26.21:01:09.279 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store. Is empty: false
25-08-26.21:01:09.287 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store in schema: public
25-08-26.21:01:09.287 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-08-26.21:01:09.379 [main            ] INFO  AiModelApiTest         - Started AiModelApiTest in 4.75 seconds (process running for 6.381)
25-08-26.21:01:11.676 [main            ] INFO  TextSplitter           - Splitting up document into 1257 chunks.
25-08-26.21:03:03.709 [main            ] INFO  AiModelApiTest         - Starting AiModelApiTest using Java 17.0.12 with PID 26480 (started by 30562 in D:\ACode\project\ai-agent\ai-agent-app)
25-08-26.21:03:03.711 [main            ] INFO  AiModelApiTest         - The following 1 profile is active: "dev"
25-08-26.21:03:04.853 [main            ] WARN  ClassPathMapperScanner - No MyBatis mapper was found in '[com.flyxy.ai]' package. Please check your configuration.
25-08-26.21:03:05.607 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-08-26.21:03:07.421 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store. Is empty: false
25-08-26.21:03:07.429 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store in schema: public
25-08-26.21:03:07.429 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-08-26.21:03:07.540 [main            ] INFO  AiModelApiTest         - Started AiModelApiTest in 4.502 seconds (process running for 6.201)
25-08-26.21:03:09.764 [main            ] INFO  TextSplitter           - Splitting up document into 410 chunks.
25-08-26.21:03:55.354 [main            ] INFO  HikariDataSource       - Retail_HikariCP - Starting...
25-08-26.21:12:44.015 [main            ] INFO  AiModelApiTest         - Starting AiModelApiTest using Java 17.0.12 with PID 16724 (started by 30562 in D:\ACode\project\ai-agent\ai-agent-app)
25-08-26.21:12:44.016 [main            ] INFO  AiModelApiTest         - The following 1 profile is active: "dev"
25-08-26.21:12:45.163 [main            ] WARN  ClassPathMapperScanner - No MyBatis mapper was found in '[com.flyxy.ai]' package. Please check your configuration.
25-08-26.21:12:45.967 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-08-26.21:12:47.679 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store. Is empty: false
25-08-26.21:12:47.687 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store in schema: public
25-08-26.21:12:47.687 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-08-26.21:12:47.770 [main            ] INFO  AiModelApiTest         - Started AiModelApiTest in 4.441 seconds (process running for 6.109)
25-08-26.21:12:49.577 [main            ] INFO  TextSplitter           - Splitting up document into 7 chunks.
25-08-26.21:12:50.901 [main            ] INFO  HikariDataSource       - Retail_HikariCP - Starting...
25-08-26.21:12:51.088 [main            ] INFO  HikariPool             - Retail_HikariCP - Added connection org.postgresql.jdbc.PgConnection@5ee64dc2
25-08-26.21:12:51.089 [main            ] INFO  HikariDataSource       - Retail_HikariCP - Start completed.
25-08-26.21:12:51.222 [SpringApplicationShutdownHook] INFO  HikariDataSource       - Retail_HikariCP - Shutdown initiated...
25-08-26.21:12:51.256 [SpringApplicationShutdownHook] INFO  HikariDataSource       - Retail_HikariCP - Shutdown completed.
25-08-26.21:19:35.953 [main            ] INFO  AiModelApiTest         - Starting AiModelApiTest using Java 17.0.12 with PID 660 (started by 30562 in D:\ACode\project\ai-agent\ai-agent-app)
25-08-26.21:19:35.956 [main            ] INFO  AiModelApiTest         - The following 1 profile is active: "dev"
25-08-26.21:19:37.128 [main            ] WARN  ClassPathMapperScanner - No MyBatis mapper was found in '[com.flyxy.ai]' package. Please check your configuration.
25-08-26.21:19:37.933 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-08-26.21:19:39.759 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_openai. Is empty: false
25-08-26.21:19:39.766 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_openai in schema: public
25-08-26.21:19:39.767 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-08-26.21:19:39.837 [main            ] INFO  AiModelApiTest         - Started AiModelApiTest in 4.573 seconds (process running for 6.228)
25-08-26.21:19:41.705 [main            ] INFO  TextSplitter           - Splitting up document into 7 chunks.
25-08-26.21:19:43.090 [main            ] INFO  HikariDataSource       - Retail_HikariCP - Starting...
25-08-26.21:19:43.262 [main            ] INFO  HikariPool             - Retail_HikariCP - Added connection org.postgresql.jdbc.PgConnection@2cce346b
25-08-26.21:19:43.265 [main            ] INFO  HikariDataSource       - Retail_HikariCP - Start completed.
25-08-26.21:19:43.342 [main            ] INFO  AiModelApiTest         - 上传完成
25-08-26.21:19:43.361 [SpringApplicationShutdownHook] INFO  HikariDataSource       - Retail_HikariCP - Shutdown initiated...
25-08-26.21:19:43.365 [SpringApplicationShutdownHook] INFO  HikariDataSource       - Retail_HikariCP - Shutdown completed.
25-08-26.21:19:54.093 [main            ] INFO  AiModelApiTest         - Starting AiModelApiTest using Java 17.0.12 with PID 13216 (started by 30562 in D:\ACode\project\ai-agent\ai-agent-app)
25-08-26.21:19:54.097 [main            ] INFO  AiModelApiTest         - The following 1 profile is active: "dev"
25-08-26.21:19:55.199 [main            ] WARN  ClassPathMapperScanner - No MyBatis mapper was found in '[com.flyxy.ai]' package. Please check your configuration.
25-08-26.21:19:56.106 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-08-26.21:19:58.130 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_openai. Is empty: false
25-08-26.21:19:58.139 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_openai in schema: public
25-08-26.21:19:58.139 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-08-26.21:19:58.231 [main            ] INFO  AiModelApiTest         - Started AiModelApiTest in 4.822 seconds (process running for 6.623)
25-08-26.21:19:59.961 [main            ] INFO  HikariDataSource       - Retail_HikariCP - Starting...
25-08-26.21:20:00.116 [main            ] INFO  HikariPool             - Retail_HikariCP - Added connection org.postgresql.jdbc.PgConnection@7ea9cd34
25-08-26.21:20:00.117 [main            ] INFO  HikariDataSource       - Retail_HikariCP - Start completed.
25-08-26.21:20:01.241 [main            ] INFO  AiModelApiTest         - 测试结果: {"metadata":{"empty":false,"id":"chatcmpl-5e987f77-8694-9c8a-b61c-eec9c292662b","model":"qwen-plus","rateLimit":{},"usage":{"promptTokens":2012,"completionTokens":7,"totalTokens":2019,"nativeUsage":{"completion_tokens":7,"prompt_tokens":2012,"prompt_tokens_details":{"cached_tokens":0},"total_tokens":2019}}},"result":{"metadata":{"contentFilters":[],"empty":true,"finishReason":"STOP"},"output":{"media":[],"messageType":"ASSISTANT","metadata":{"role":"ASSISTANT","messageType":"ASSISTANT","refusal":"","finishReason":"STOP","annotations":[],"index":0,"id":"chatcmpl-5e987f77-8694-9c8a-b61c-eec9c292662b"},"text":"小说的主角叫阿哲。"{"$ref":"$.metadata.rateLimit.usage.nativeUsage.prompt_tokens_details.result.metadata.contentFilters.output.metadata.annotations"}}},"results":[{"$ref":"$.metadata.rateLimit.usage.nativeUsage.prompt_tokens_details.result"}]}
25-08-26.21:20:01.254 [SpringApplicationShutdownHook] INFO  HikariDataSource       - Retail_HikariCP - Shutdown initiated...
25-08-26.21:20:01.256 [SpringApplicationShutdownHook] INFO  HikariDataSource       - Retail_HikariCP - Shutdown completed.
25-08-26.21:41:20.526 [main            ] INFO  AiAgentTest            - Starting AiAgentTest using Java 17.0.12 with PID 32020 (started by 30562 in D:\ACode\project\ai-agent\ai-agent-app)
25-08-26.21:41:20.529 [main            ] INFO  AiAgentTest            - The following 1 profile is active: "dev"
25-08-26.21:41:21.673 [main            ] WARN  ClassPathMapperScanner - No MyBatis mapper was found in '[com.flyxy.ai]' package. Please check your configuration.
25-08-26.21:41:22.477 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-08-26.21:41:24.421 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_openai. Is empty: false
25-08-26.21:41:24.428 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_openai in schema: public
25-08-26.21:41:24.428 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-08-26.21:41:24.513 [main            ] INFO  AiAgentTest            - Started AiAgentTest in 4.654 seconds (process running for 6.175)
25-08-26.21:41:25.416 [boundedElastic-1] ERROR Operators              - Operator called default onErrorDropped
reactor.core.Exceptions$ErrorCallbackNotImplemented: java.lang.RuntimeException: Failed to start process with command: [npx, -y, @modelcontextprotocol/server-filesystem, /Users/<USER>/Desktop, /Users/<USER>/Desktop]
Caused by: java.lang.RuntimeException: Failed to start process with command: [npx, -y, @modelcontextprotocol/server-filesystem, /Users/<USER>/Desktop, /Users/<USER>/Desktop]
	at io.modelcontextprotocol.client.transport.StdioClientTransport.lambda$connect$1(StdioClientTransport.java:132)
	at reactor.core.publisher.MonoRunnable.call(MonoRunnable.java:73)
	at reactor.core.publisher.MonoRunnable.call(MonoRunnable.java:32)
	at reactor.core.publisher.FluxSubscribeOnCallable$CallableSubscribeOnSubscription.run(FluxSubscribeOnCallable.java:228)
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:68)
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:28)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.io.IOException: Cannot run program "npx": CreateProcess error=2, 系统找不到指定的文件。
	at java.base/java.lang.ProcessBuilder.start(ProcessBuilder.java:1143)
	at java.base/java.lang.ProcessBuilder.start(ProcessBuilder.java:1073)
	at io.modelcontextprotocol.client.transport.StdioClientTransport.lambda$connect$1(StdioClientTransport.java:129)
	... 11 common frames omitted
Caused by: java.io.IOException: CreateProcess error=2, 系统找不到指定的文件。
	at java.base/java.lang.ProcessImpl.create(Native Method)
	at java.base/java.lang.ProcessImpl.<init>(ProcessImpl.java:499)
	at java.base/java.lang.ProcessImpl.start(ProcessImpl.java:158)
	at java.base/java.lang.ProcessBuilder.start(ProcessBuilder.java:1110)
	... 13 common frames omitted
25-08-26.21:47:46.640 [main            ] INFO  AiAgentTest            - Starting AiAgentTest using Java 17.0.12 with PID 22912 (started by 30562 in D:\ACode\project\ai-agent\ai-agent-app)
25-08-26.21:47:46.644 [main            ] INFO  AiAgentTest            - The following 1 profile is active: "dev"
25-08-26.21:47:47.822 [main            ] WARN  ClassPathMapperScanner - No MyBatis mapper was found in '[com.flyxy.ai]' package. Please check your configuration.
25-08-26.21:47:48.711 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-08-26.21:47:50.701 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_openai. Is empty: false
25-08-26.21:47:50.707 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_openai in schema: public
25-08-26.21:47:50.707 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-08-26.21:47:50.784 [main            ] INFO  AiAgentTest            - Started AiAgentTest in 4.814 seconds (process running for 6.425)
25-08-26.21:47:52.238 [HttpClient-10-Worker-0] INFO  McpAsyncClient         - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[completions=null, experimental=null, logging=LoggingCapabilities[], prompts=null, resources=null, tools=ToolCapabilities[listChanged=true]], Info: Implementation[name=mcp-server-weixin, version=1.0.0] and Instructions null
25-08-26.21:47:52.405 [HttpClient-11-Worker-2] INFO  McpAsyncClient         - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[completions=null, experimental=null, logging=LoggingCapabilities[], prompts=null, resources=null, tools=ToolCapabilities[listChanged=true]], Info: Implementation[name=mcp-server-csdn, version=1.0.0] and Instructions null
25-08-26.21:47:52.934 [main            ] WARN  RetryUtils             - Retry error. Retry count:1
org.springframework.ai.retry.NonTransientAiException: 404 - 
	at org.springframework.ai.retry.RetryUtils$1.handleError(RetryUtils.java:63)
	at org.springframework.web.client.ResponseErrorHandler.handleError(ResponseErrorHandler.java:58)
	at org.springframework.web.client.StatusHandler.lambda$fromErrorHandler$1(StatusHandler.java:71)
	at org.springframework.web.client.StatusHandler.handle(StatusHandler.java:146)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.applyStatusHandlers(DefaultRestClient.java:826)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.lambda$readBody$4(DefaultRestClient.java:815)
	at org.springframework.web.client.DefaultRestClient.readWithMessageConverters(DefaultRestClient.java:215)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.readBody(DefaultRestClient.java:814)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.lambda$toEntityInternal$2(DefaultRestClient.java:770)
	at org.springframework.web.client.DefaultRestClient$DefaultRequestBodyUriSpec.exchangeInternal(DefaultRestClient.java:574)
	at org.springframework.web.client.DefaultRestClient$DefaultRequestBodyUriSpec.exchange(DefaultRestClient.java:535)
	at org.springframework.web.client.RestClient$RequestHeadersSpec.exchange(RestClient.java:677)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.executeAndExtract(DefaultRestClient.java:809)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.toEntityInternal(DefaultRestClient.java:769)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.toEntity(DefaultRestClient.java:758)
	at org.springframework.ai.openai.api.OpenAiApi.chatCompletionEntity(OpenAiApi.java:187)
	at org.springframework.ai.openai.OpenAiChatModel.lambda$internalCall$1(OpenAiChatModel.java:199)
	at org.springframework.retry.support.RetryTemplate.doExecute(RetryTemplate.java:357)
	at org.springframework.retry.support.RetryTemplate.execute(RetryTemplate.java:230)
	at org.springframework.ai.openai.OpenAiChatModel.lambda$internalCall$3(OpenAiChatModel.java:199)
	at io.micrometer.observation.Observation.observe(Observation.java:564)
	at org.springframework.ai.openai.OpenAiChatModel.internalCall(OpenAiChatModel.java:196)
	at org.springframework.ai.openai.OpenAiChatModel.call(OpenAiChatModel.java:181)
	at com.flyxy.ai.test.AiAgentTest.test_chat_model_call(AiAgentTest.java:137)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.springframework.test.context.junit4.statements.RunBeforeTestExecutionCallbacks.evaluate(RunBeforeTestExecutionCallbacks.java:76)
	at org.springframework.test.context.junit4.statements.RunAfterTestExecutionCallbacks.evaluate(RunAfterTestExecutionCallbacks.java:84)
	at org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:26)
	at org.springframework.test.context.junit4.statements.RunBeforeTestMethodCallbacks.evaluate(RunBeforeTestMethodCallbacks.java:75)
	at org.springframework.test.context.junit4.statements.RunAfterTestMethodCallbacks.evaluate(RunAfterTestMethodCallbacks.java:86)
	at org.springframework.test.context.junit4.statements.SpringRepeat.evaluate(SpringRepeat.java:84)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:252)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:97)
	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at org.springframework.test.context.junit4.statements.RunBeforeTestClassCallbacks.evaluate(RunBeforeTestClassCallbacks.java:61)
	at org.springframework.test.context.junit4.statements.RunAfterTestClassCallbacks.evaluate(RunAfterTestClassCallbacks.java:70)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.run(SpringJUnit4ClassRunner.java:191)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
	at com.intellij.junit4.JUnit4IdeaTestRunner.startRunnerWithArgs(JUnit4IdeaTestRunner.java:69)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater$1.execute(IdeaTestRunner.java:38)
	at com.intellij.rt.execution.junit.TestsRepeater.repeat(TestsRepeater.java:11)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:35)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:231)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:55)
25-08-26.21:50:35.167 [main            ] INFO  AiAgentTest            - Starting AiAgentTest using Java 17.0.12 with PID 10692 (started by 30562 in D:\ACode\project\ai-agent\ai-agent-app)
25-08-26.21:50:35.170 [main            ] INFO  AiAgentTest            - The following 1 profile is active: "dev"
25-08-26.21:50:36.302 [main            ] WARN  ClassPathMapperScanner - No MyBatis mapper was found in '[com.flyxy.ai]' package. Please check your configuration.
25-08-26.21:50:37.066 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-08-26.21:50:38.827 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_openai. Is empty: false
25-08-26.21:50:38.834 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_openai in schema: public
25-08-26.21:50:38.834 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-08-26.21:50:38.921 [main            ] INFO  AiAgentTest            - Started AiAgentTest in 4.414 seconds (process running for 5.887)
25-08-26.21:50:40.029 [HttpClient-10-Worker-0] INFO  McpAsyncClient         - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[completions=null, experimental=null, logging=LoggingCapabilities[], prompts=null, resources=null, tools=ToolCapabilities[listChanged=true]], Info: Implementation[name=mcp-server-weixin, version=1.0.0] and Instructions null
25-08-26.21:50:40.078 [HttpClient-11-Worker-2] INFO  McpAsyncClient         - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[completions=null, experimental=null, logging=LoggingCapabilities[], prompts=null, resources=null, tools=ToolCapabilities[listChanged=true]], Info: Implementation[name=mcp-server-csdn, version=1.0.0] and Instructions null
25-08-26.21:50:44.598 [main            ] INFO  AiAgentTest            - 测试结果(call):{"metadata":{"empty":false,"id":"chatcmpl-6af74094-ce6f-935b-a881-ef0fd40649d3","model":"deepseek-v3","rateLimit":{},"usage":{"promptTokens":397,"completionTokens":163,"totalTokens":560,"nativeUsage":{"completion_tokens":163,"prompt_tokens":397,"total_tokens":560}}},"result":{"metadata":{"contentFilters":[],"empty":true,"finishReason":"STOP"},"output":{"media":[],"messageType":"ASSISTANT","metadata":{"role":"ASSISTANT","messageType":"ASSISTANT","finishReason":"STOP","refusal":"","index":0,"annotations":[],"id":"chatcmpl-6af74094-ce6f-935b-a881-ef0fd40649d3"},"text":"我可以使用以下工具来协助完成任务：\n\n1. **微信公众号消息通知**  \n   - 功能：发送消息到微信公众号通知用户。  \n   - 参数：  \n     - `description`（描述）  \n     - `jumpUrl`（跳转地址）  \n     - `platform`（平台）  \n     - `subject`（主题）  \n\n2. **发布文章到CSDN**  \n   - 功能：将文章发布到CSDN平台。  \n   - 参数：  \n     - `Description`（文章简述）  \n     - `markdowncontent`（文章内容）  \n     - `tags`（文章标签，英文逗号隔开）  \n     - `title`（文章标题）  \n\n如果需要使用这些工具，请告诉我具体需求！"{"$ref":"$.metadata.rateLimit.usage.nativeUsage.result.metadata.contentFilters.output.metadata.annotations"}}},"results":[{"$ref":"$.metadata.rateLimit.usage.nativeUsage.result"}]}
25-08-26.21:52:18.533 [main            ] INFO  AiAgentTest            - Starting AiAgentTest using Java 17.0.12 with PID 19328 (started by 30562 in D:\ACode\project\ai-agent\ai-agent-app)
25-08-26.21:52:18.537 [main            ] INFO  AiAgentTest            - The following 1 profile is active: "dev"
25-08-26.21:52:19.693 [main            ] WARN  ClassPathMapperScanner - No MyBatis mapper was found in '[com.flyxy.ai]' package. Please check your configuration.
25-08-26.21:52:20.637 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-08-26.21:52:22.749 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_openai. Is empty: false
25-08-26.21:52:22.757 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_openai in schema: public
25-08-26.21:52:22.757 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-08-26.21:52:22.849 [main            ] INFO  AiAgentTest            - Started AiAgentTest in 4.992 seconds (process running for 6.595)
25-08-26.21:52:23.873 [HttpClient-10-Worker-0] INFO  McpAsyncClient         - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[completions=null, experimental=null, logging=LoggingCapabilities[], prompts=null, resources=null, tools=ToolCapabilities[listChanged=true]], Info: Implementation[name=mcp-server-weixin, version=1.0.0] and Instructions null
25-08-26.21:52:23.921 [HttpClient-11-Worker-2] INFO  McpAsyncClient         - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[completions=null, experimental=null, logging=LoggingCapabilities[], prompts=null, resources=null, tools=ToolCapabilities[listChanged=true]], Info: Implementation[name=mcp-server-csdn, version=1.0.0] and Instructions null
25-08-26.21:52:26.776 [main            ] INFO  AiAgentTest            - 测试结果(call):{"metadata":{"empty":false,"id":"chatcmpl-f98bf9b6-e811-9ebc-9ca8-03ea03556cc0","model":"deepseek-v3","rateLimit":{},"usage":{"promptTokens":397,"completionTokens":76,"totalTokens":473,"nativeUsage":{"completion_tokens":76,"prompt_tokens":397,"total_tokens":473}}},"result":{"metadata":{"contentFilters":[],"empty":true,"finishReason":"STOP"},"output":{"media":[],"messageType":"ASSISTANT","metadata":{"role":"ASSISTANT","messageType":"ASSISTANT","refusal":"","finishReason":"STOP","annotations":[],"index":0,"id":"chatcmpl-f98bf9b6-e811-9ebc-9ca8-03ea03556cc0"},"text":"请提供以下信息以便我为您发送微信公众号通知：\n\n1. **主题**（subject）：通知的标题或主题。\n2. **描述**（description）：通知的详细内容。\n3. **跳转地址**（jumpUrl）：点击通知后跳转的链接。\n4. **平台**（platform）：发送通知的平台名称（如“微信”）。"{"$ref":"$.metadata.rateLimit.usage.nativeUsage.result.metadata.contentFilters.output.metadata.annotations"}}},"results":[{"$ref":"$.metadata.rateLimit.usage.nativeUsage.result"}]}
25-08-26.21:53:11.611 [main            ] INFO  AiAgentTest            - Starting AiAgentTest using Java 17.0.12 with PID 31564 (started by 30562 in D:\ACode\project\ai-agent\ai-agent-app)
25-08-26.21:53:11.613 [main            ] INFO  AiAgentTest            - The following 1 profile is active: "dev"
25-08-26.21:53:12.671 [main            ] WARN  ClassPathMapperScanner - No MyBatis mapper was found in '[com.flyxy.ai]' package. Please check your configuration.
25-08-26.21:53:13.467 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-08-26.21:53:15.346 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_openai. Is empty: false
25-08-26.21:53:15.353 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_openai in schema: public
25-08-26.21:53:15.353 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-08-26.21:53:15.438 [main            ] INFO  AiAgentTest            - Started AiAgentTest in 4.488 seconds (process running for 5.988)
25-08-26.21:53:16.462 [HttpClient-10-Worker-0] INFO  McpAsyncClient         - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[completions=null, experimental=null, logging=LoggingCapabilities[], prompts=null, resources=null, tools=ToolCapabilities[listChanged=true]], Info: Implementation[name=mcp-server-weixin, version=1.0.0] and Instructions null
25-08-26.21:53:16.511 [HttpClient-11-Worker-2] INFO  McpAsyncClient         - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[completions=null, experimental=null, logging=LoggingCapabilities[], prompts=null, resources=null, tools=ToolCapabilities[listChanged=true]], Info: Implementation[name=mcp-server-csdn, version=1.0.0] and Instructions null
25-08-26.21:53:20.666 [main            ] INFO  AiAgentTest            - 测试结果(call):{"metadata":{"empty":false,"id":"chatcmpl-4f210045-bbc0-9bbc-aef6-70ede77774ee","model":"deepseek-v3","rateLimit":{},"usage":{"promptTokens":886,"completionTokens":72,"totalTokens":958}},"result":{"metadata":{"contentFilters":[],"empty":true,"finishReason":"STOP"},"output":{"media":[],"messageType":"ASSISTANT","metadata":{"role":"ASSISTANT","messageType":"ASSISTANT","finishReason":"STOP","refusal":"","index":0,"annotations":[],"id":"chatcmpl-4f210045-bbc0-9bbc-aef6-70ede77774ee"},"text":"微信公众号通知已成功发送，返回结果为：{\"success\":true}"{"$ref":"$.metadata.rateLimit.usage.result.metadata.contentFilters.output.metadata.annotations"}}},"results":[{"$ref":"$.metadata.rateLimit.usage.result"}]}
25-08-26.22:02:35.414 [main            ] INFO  AiAgentTest            - Starting AiAgentTest using Java 17.0.12 with PID 17916 (started by 30562 in D:\ACode\project\ai-agent\ai-agent-app)
25-08-26.22:02:35.418 [main            ] INFO  AiAgentTest            - The following 1 profile is active: "dev"
25-08-26.22:02:36.517 [main            ] WARN  ClassPathMapperScanner - No MyBatis mapper was found in '[com.flyxy.ai]' package. Please check your configuration.
25-08-26.22:02:37.319 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-08-26.22:02:39.162 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_openai. Is empty: false
25-08-26.22:02:39.170 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_openai in schema: public
25-08-26.22:02:39.170 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-08-26.22:02:39.257 [main            ] INFO  AiAgentTest            - Started AiAgentTest in 4.517 seconds (process running for 6.019)
25-08-26.22:02:40.595 [HttpClient-10-Worker-0] INFO  McpAsyncClient         - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[completions=null, experimental=null, logging=LoggingCapabilities[], prompts=null, resources=null, tools=ToolCapabilities[listChanged=true]], Info: Implementation[name=mcp-server-weixin, version=1.0.0] and Instructions null
25-08-26.22:02:40.654 [HttpClient-11-Worker-2] INFO  McpAsyncClient         - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[completions=null, experimental=null, logging=LoggingCapabilities[], prompts=null, resources=null, tools=ToolCapabilities[listChanged=true]], Info: Implementation[name=mcp-server-csdn, version=1.0.0] and Instructions null
25-08-26.22:02:45.076 [main            ] INFO  AiAgentTest            - 测试结果(call):{"metadata":{"empty":false,"id":"chatcmpl-68975919-a02e-954a-8f06-f8de846d5e7c","model":"deepseek-v3","rateLimit":{},"usage":{"promptTokens":887,"completionTokens":74,"totalTokens":961}},"result":{"metadata":{"contentFilters":[],"empty":true,"finishReason":"STOP"},"output":{"media":[],"messageType":"ASSISTANT","metadata":{"role":"ASSISTANT","messageType":"ASSISTANT","finishReason":"STOP","refusal":"","index":0,"annotations":[],"id":"chatcmpl-68975919-a02e-954a-8f06-f8de846d5e7c"},"text":"微信公众号通知已成功发送，返回结果为：{\"success\":true}。"{"$ref":"$.metadata.rateLimit.usage.result.metadata.contentFilters.output.metadata.annotations"}}},"results":[{"$ref":"$.metadata.rateLimit.usage.result"}]}
25-08-26.22:05:36.566 [main            ] INFO  AiAgentTest            - Starting AiAgentTest using Java 17.0.12 with PID 14344 (started by 30562 in D:\ACode\project\ai-agent\ai-agent-app)
25-08-26.22:05:36.568 [main            ] INFO  AiAgentTest            - The following 1 profile is active: "dev"
25-08-26.22:05:37.631 [main            ] WARN  ClassPathMapperScanner - No MyBatis mapper was found in '[com.flyxy.ai]' package. Please check your configuration.
25-08-26.22:05:38.412 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-08-26.22:05:40.295 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_openai. Is empty: false
25-08-26.22:05:40.302 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_openai in schema: public
25-08-26.22:05:40.302 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-08-26.22:05:40.370 [main            ] INFO  AiAgentTest            - Started AiAgentTest in 4.464 seconds (process running for 5.963)
25-08-26.22:05:41.453 [HttpClient-10-Worker-0] INFO  McpAsyncClient         - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[completions=null, experimental=null, logging=LoggingCapabilities[], prompts=null, resources=null, tools=ToolCapabilities[listChanged=true]], Info: Implementation[name=mcp-server-weixin, version=1.0.0] and Instructions null
25-08-26.22:05:41.498 [HttpClient-11-Worker-2] INFO  McpAsyncClient         - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[completions=null, experimental=null, logging=LoggingCapabilities[], prompts=null, resources=null, tools=ToolCapabilities[listChanged=true]], Info: Implementation[name=mcp-server-csdn, version=1.0.0] and Instructions null
25-08-26.22:05:42.473 [main            ] INFO  HikariDataSource       - Retail_HikariCP - Starting...
25-08-26.22:05:42.631 [main            ] INFO  HikariPool             - Retail_HikariCP - Added connection org.postgresql.jdbc.PgConnection@7eb618c1
25-08-26.22:05:42.633 [main            ] INFO  HikariDataSource       - Retail_HikariCP - Start completed.
25-08-26.22:05:44.403 [main            ] WARN  RetryUtils             - Retry error. Retry count:1
org.springframework.ai.retry.NonTransientAiException: 404 - {"error":{"message":"The model `gpt-4.1` does not exist or you do not have access to it.","type":"invalid_request_error","param":null,"code":"model_not_found"},"request_id":"c5133037-d880-9b28-b632-69910ccd7586"}
	at org.springframework.ai.retry.RetryUtils$1.handleError(RetryUtils.java:63)
	at org.springframework.web.client.ResponseErrorHandler.handleError(ResponseErrorHandler.java:58)
	at org.springframework.web.client.StatusHandler.lambda$fromErrorHandler$1(StatusHandler.java:71)
	at org.springframework.web.client.StatusHandler.handle(StatusHandler.java:146)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.applyStatusHandlers(DefaultRestClient.java:826)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.lambda$readBody$4(DefaultRestClient.java:815)
	at org.springframework.web.client.DefaultRestClient.readWithMessageConverters(DefaultRestClient.java:215)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.readBody(DefaultRestClient.java:814)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.lambda$toEntityInternal$2(DefaultRestClient.java:770)
	at org.springframework.web.client.DefaultRestClient$DefaultRequestBodyUriSpec.exchangeInternal(DefaultRestClient.java:574)
	at org.springframework.web.client.DefaultRestClient$DefaultRequestBodyUriSpec.exchange(DefaultRestClient.java:535)
	at org.springframework.web.client.RestClient$RequestHeadersSpec.exchange(RestClient.java:677)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.executeAndExtract(DefaultRestClient.java:809)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.toEntityInternal(DefaultRestClient.java:769)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.toEntity(DefaultRestClient.java:758)
	at org.springframework.ai.openai.api.OpenAiApi.chatCompletionEntity(OpenAiApi.java:187)
	at org.springframework.ai.openai.OpenAiChatModel.lambda$internalCall$1(OpenAiChatModel.java:199)
	at org.springframework.retry.support.RetryTemplate.doExecute(RetryTemplate.java:357)
	at org.springframework.retry.support.RetryTemplate.execute(RetryTemplate.java:230)
	at org.springframework.ai.openai.OpenAiChatModel.lambda$internalCall$3(OpenAiChatModel.java:199)
	at io.micrometer.observation.Observation.observe(Observation.java:564)
	at org.springframework.ai.openai.OpenAiChatModel.internalCall(OpenAiChatModel.java:196)
	at org.springframework.ai.openai.OpenAiChatModel.call(OpenAiChatModel.java:181)
	at org.springframework.ai.chat.client.advisor.ChatModelCallAdvisor.adviseCall(ChatModelCallAdvisor.java:54)
	at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextCall$1(DefaultAroundAdvisorChain.java:110)
	at io.micrometer.observation.Observation.observe(Observation.java:564)
	at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextCall(DefaultAroundAdvisorChain.java:110)
	at org.springframework.ai.chat.client.advisor.SimpleLoggerAdvisor.adviseCall(SimpleLoggerAdvisor.java:74)
	at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextCall$1(DefaultAroundAdvisorChain.java:110)
	at io.micrometer.observation.Observation.observe(Observation.java:564)
	at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextCall(DefaultAroundAdvisorChain.java:110)
	at org.springframework.ai.chat.client.advisor.api.BaseAdvisor.adviseCall(BaseAdvisor.java:52)
	at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextCall$1(DefaultAroundAdvisorChain.java:110)
	at io.micrometer.observation.Observation.observe(Observation.java:564)
	at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextCall(DefaultAroundAdvisorChain.java:110)
	at org.springframework.ai.chat.client.DefaultChatClient$DefaultCallResponseSpec.lambda$doGetObservableChatClientResponse$1(DefaultChatClient.java:469)
	at io.micrometer.observation.Observation.observe(Observation.java:564)
	at org.springframework.ai.chat.client.DefaultChatClient$DefaultCallResponseSpec.doGetObservableChatClientResponse(DefaultChatClient.java:467)
	at org.springframework.ai.chat.client.DefaultChatClient$DefaultCallResponseSpec.doGetObservableChatClientResponse(DefaultChatClient.java:446)
	at org.springframework.ai.chat.client.DefaultChatClient$DefaultCallResponseSpec.content(DefaultChatClient.java:441)
	at com.flyxy.ai.test.AiAgentTest.test_client03(AiAgentTest.java:291)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.springframework.test.context.junit4.statements.RunBeforeTestExecutionCallbacks.evaluate(RunBeforeTestExecutionCallbacks.java:76)
	at org.springframework.test.context.junit4.statements.RunAfterTestExecutionCallbacks.evaluate(RunAfterTestExecutionCallbacks.java:84)
	at org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:26)
	at org.springframework.test.context.junit4.statements.RunBeforeTestMethodCallbacks.evaluate(RunBeforeTestMethodCallbacks.java:75)
	at org.springframework.test.context.junit4.statements.RunAfterTestMethodCallbacks.evaluate(RunAfterTestMethodCallbacks.java:86)
	at org.springframework.test.context.junit4.statements.SpringRepeat.evaluate(SpringRepeat.java:84)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:252)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:97)
	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at org.springframework.test.context.junit4.statements.RunBeforeTestClassCallbacks.evaluate(RunBeforeTestClassCallbacks.java:61)
	at org.springframework.test.context.junit4.statements.RunAfterTestClassCallbacks.evaluate(RunAfterTestClassCallbacks.java:70)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.run(SpringJUnit4ClassRunner.java:191)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
	at com.intellij.junit4.JUnit4IdeaTestRunner.startRunnerWithArgs(JUnit4IdeaTestRunner.java:69)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater$1.execute(IdeaTestRunner.java:38)
	at com.intellij.rt.execution.junit.TestsRepeater.repeat(TestsRepeater.java:11)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:35)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:231)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:55)
25-08-26.22:05:44.423 [SpringApplicationShutdownHook] INFO  HikariDataSource       - Retail_HikariCP - Shutdown initiated...
25-08-26.22:05:44.425 [SpringApplicationShutdownHook] INFO  HikariDataSource       - Retail_HikariCP - Shutdown completed.
25-08-26.22:06:48.829 [main            ] INFO  AiAgentTest            - Starting AiAgentTest using Java 17.0.12 with PID 24492 (started by 30562 in D:\ACode\project\ai-agent\ai-agent-app)
25-08-26.22:06:48.833 [main            ] INFO  AiAgentTest            - The following 1 profile is active: "dev"
25-08-26.22:06:49.906 [main            ] WARN  ClassPathMapperScanner - No MyBatis mapper was found in '[com.flyxy.ai]' package. Please check your configuration.
25-08-26.22:06:50.715 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-08-26.22:06:52.734 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_openai. Is empty: false
25-08-26.22:06:52.741 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_openai in schema: public
25-08-26.22:06:52.742 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-08-26.22:06:52.823 [main            ] INFO  AiAgentTest            - Started AiAgentTest in 4.656 seconds (process running for 6.193)
25-08-26.22:06:53.854 [HttpClient-10-Worker-0] INFO  McpAsyncClient         - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[completions=null, experimental=null, logging=LoggingCapabilities[], prompts=null, resources=null, tools=ToolCapabilities[listChanged=true]], Info: Implementation[name=mcp-server-weixin, version=1.0.0] and Instructions null
25-08-26.22:06:53.902 [HttpClient-11-Worker-2] INFO  McpAsyncClient         - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[completions=null, experimental=null, logging=LoggingCapabilities[], prompts=null, resources=null, tools=ToolCapabilities[listChanged=true]], Info: Implementation[name=mcp-server-csdn, version=1.0.0] and Instructions null
25-08-26.22:06:54.710 [main            ] INFO  HikariDataSource       - Retail_HikariCP - Starting...
25-08-26.22:06:54.868 [main            ] INFO  HikariPool             - Retail_HikariCP - Added connection org.postgresql.jdbc.PgConnection@7b894f5f
25-08-26.22:06:54.870 [main            ] INFO  HikariDataSource       - Retail_HikariCP - Start completed.
25-08-26.22:06:58.349 [SpringApplicationShutdownHook] INFO  HikariDataSource       - Retail_HikariCP - Shutdown initiated...
25-08-26.22:06:58.353 [SpringApplicationShutdownHook] INFO  HikariDataSource       - Retail_HikariCP - Shutdown completed.
25-08-26.22:10:16.285 [main            ] INFO  AiAgentTest            - Starting AiAgentTest using Java 17.0.12 with PID 11828 (started by 30562 in D:\ACode\project\ai-agent\ai-agent-app)
25-08-26.22:10:16.288 [main            ] INFO  AiAgentTest            - The following 1 profile is active: "dev"
25-08-26.22:10:17.364 [main            ] WARN  ClassPathMapperScanner - No MyBatis mapper was found in '[com.flyxy.ai]' package. Please check your configuration.
25-08-26.22:10:18.167 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-08-26.22:10:20.089 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_openai. Is empty: false
25-08-26.22:10:20.097 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_openai in schema: public
25-08-26.22:10:20.098 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-08-26.22:10:20.182 [main            ] INFO  AiAgentTest            - Started AiAgentTest in 4.558 seconds (process running for 6.123)
25-08-26.22:10:21.315 [HttpClient-10-Worker-0] INFO  McpAsyncClient         - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[completions=null, experimental=null, logging=LoggingCapabilities[], prompts=null, resources=null, tools=ToolCapabilities[listChanged=true]], Info: Implementation[name=mcp-server-weixin, version=1.0.0] and Instructions null
25-08-26.22:10:21.359 [HttpClient-11-Worker-0] INFO  McpAsyncClient         - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[completions=null, experimental=null, logging=LoggingCapabilities[], prompts=null, resources=null, tools=ToolCapabilities[listChanged=true]], Info: Implementation[name=mcp-server-csdn, version=1.0.0] and Instructions null
25-08-26.22:10:22.232 [main            ] INFO  HikariDataSource       - Retail_HikariCP - Starting...
25-08-26.22:10:22.391 [main            ] INFO  HikariPool             - Retail_HikariCP - Added connection org.postgresql.jdbc.PgConnection@614805a5
25-08-26.22:10:22.393 [main            ] INFO  HikariDataSource       - Retail_HikariCP - Start completed.
25-08-26.22:10:35.991 [SpringApplicationShutdownHook] INFO  HikariDataSource       - Retail_HikariCP - Shutdown initiated...
25-08-26.22:10:35.993 [SpringApplicationShutdownHook] INFO  HikariDataSource       - Retail_HikariCP - Shutdown completed.
