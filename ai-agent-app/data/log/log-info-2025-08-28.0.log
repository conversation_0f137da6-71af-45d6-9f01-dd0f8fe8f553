25-08-28.21:57:56.216 [main            ] INFO  AgentArmoryTest        - Starting AgentArmoryTest using Java 17.0.12 with PID 18168 (started by 30562 in D:\ACode\project\ai-agent\ai-agent-app)
25-08-28.21:57:56.218 [main            ] INFO  AgentArmoryTest        - The following 1 profile is active: "dev"
25-08-28.21:57:59.419 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-08-28.21:58:01.708 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_openai. Is empty: false
25-08-28.21:58:01.717 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_openai in schema: public
25-08-28.21:58:01.718 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-08-28.21:58:01.856 [main            ] INFO  AgentArmoryTest        - Started AgentArmoryTest in 6.478 seconds (process running for 8.663)
25-08-28.21:58:46.103 [pool-2-thread-1 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_api) [3001]
25-08-28.21:58:57.351 [pool-2-thread-2 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_model) [3001]
25-08-28.21:58:57.359 [pool-2-thread-3 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_tool_mcp) [3001]
25-08-28.21:59:14.186 [pool-2-thread-4 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_system_prompt) [3001]
25-08-28.21:59:14.188 [pool-2-thread-5 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_advisor) [3001]
25-08-28.21:59:14.189 [pool-2-thread-6 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client) [3001]
25-08-28.21:59:14.211 [pool-2-thread-4 ] INFO  HikariDataSource       - MainHikariPool - Starting...
25-08-28.21:59:14.526 [pool-2-thread-4 ] INFO  HikariPool             - MainHikariPool - Added connection com.mysql.cj.jdbc.ConnectionImpl@2552656
25-08-28.21:59:14.529 [pool-2-thread-4 ] INFO  HikariDataSource       - MainHikariPool - Start completed.
25-08-28.22:00:48.642 [MainHikariPool housekeeper] WARN  HikariPool             - MainHikariPool - Thread starvation or clock leap detected (housekeeper delta=51s76ms386µs800ns).
25-08-28.22:00:48.644 [main            ] INFO  AgentArmoryTest        - 测试结果：org.springframework.ai.openai.api.OpenAiApi@4dd5a57e
25-08-28.22:00:51.300 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MainHikariPool - Shutdown initiated...
25-08-28.22:00:51.320 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MainHikariPool - Shutdown completed.
