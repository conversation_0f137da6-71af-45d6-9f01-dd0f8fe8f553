25-08-29.20:12:41.103 [main            ] INFO  AgentArmoryTest        - Starting AgentArmoryTest using Java 17.0.12 with PID 32908 (started by 30562 in D:\ACode\project\ai-agent\ai-agent-app)
25-08-29.20:12:41.105 [main            ] INFO  AgentArmoryTest        - The following 1 profile is active: "dev"
25-08-29.20:12:43.913 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-08-29.20:12:45.675 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_openai. Is empty: false
25-08-29.20:12:45.683 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_openai in schema: public
25-08-29.20:12:45.683 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-08-29.20:12:45.758 [main            ] INFO  AgentArmoryTest        - Started AgentArmoryTest in 5.367 seconds (process running for 7.05)
25-08-29.20:12:46.601 [pool-2-thread-1 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_api) [3001]
25-08-29.20:12:46.601 [pool-2-thread-2 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_model) [3001]
25-08-29.20:12:46.601 [pool-2-thread-3 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_tool_mcp) [3001]
25-08-29.20:12:46.603 [pool-2-thread-4 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_system_prompt) [3001]
25-08-29.20:12:46.603 [pool-2-thread-5 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_advisor) [3001]
25-08-29.20:12:46.603 [pool-2-thread-6 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client) [3001]
25-08-29.20:12:46.627 [pool-2-thread-4 ] INFO  HikariDataSource       - MainHikariPool - Starting...
25-08-29.20:12:46.921 [pool-2-thread-4 ] INFO  HikariPool             - MainHikariPool - Added connection com.mysql.cj.jdbc.ConnectionImpl@578cc3bd
25-08-29.20:12:46.923 [pool-2-thread-4 ] INFO  HikariDataSource       - MainHikariPool - Start completed.
25-08-29.20:12:47.234 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MainHikariPool - Shutdown initiated...
25-08-29.20:12:47.282 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MainHikariPool - Shutdown completed.
25-08-29.20:14:19.251 [main            ] INFO  AgentArmoryTest        - Starting AgentArmoryTest using Java 17.0.12 with PID 38780 (started by 30562 in D:\ACode\project\ai-agent\ai-agent-app)
25-08-29.20:14:19.254 [main            ] INFO  AgentArmoryTest        - The following 1 profile is active: "dev"
25-08-29.20:14:22.428 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-08-29.20:14:24.651 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_openai. Is empty: false
25-08-29.20:14:24.658 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_openai in schema: public
25-08-29.20:14:24.658 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-08-29.20:14:24.809 [main            ] INFO  AgentArmoryTest        - Started AgentArmoryTest in 6.407 seconds (process running for 8.303)
25-08-29.20:14:25.714 [pool-2-thread-1 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_api) [3001]
25-08-29.20:14:25.714 [pool-2-thread-2 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_model) [3001]
25-08-29.20:14:25.714 [pool-2-thread-3 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_tool_mcp) [3001]
25-08-29.20:14:25.715 [pool-2-thread-4 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_system_prompt) [3001]
25-08-29.20:14:25.715 [pool-2-thread-5 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_advisor) [3001]
25-08-29.20:14:25.715 [pool-2-thread-6 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client) [3001]
25-08-29.20:14:25.736 [pool-2-thread-5 ] INFO  HikariDataSource       - MainHikariPool - Starting...
25-08-29.20:14:26.031 [pool-2-thread-5 ] INFO  HikariPool             - MainHikariPool - Added connection com.mysql.cj.jdbc.ConnectionImpl@9bbe248
25-08-29.20:14:26.033 [pool-2-thread-5 ] INFO  HikariDataSource       - MainHikariPool - Start completed.
25-08-29.20:14:26.338 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MainHikariPool - Shutdown initiated...
25-08-29.20:14:26.347 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MainHikariPool - Shutdown completed.
25-08-29.20:15:25.512 [main            ] INFO  AgentArmoryTest        - Starting AgentArmoryTest using Java 17.0.12 with PID 29988 (started by 30562 in D:\ACode\project\ai-agent\ai-agent-app)
25-08-29.20:15:25.515 [main            ] INFO  AgentArmoryTest        - The following 1 profile is active: "dev"
25-08-29.20:15:28.695 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-08-29.20:15:30.945 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_openai. Is empty: false
25-08-29.20:15:30.951 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_openai in schema: public
25-08-29.20:15:30.952 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-08-29.20:15:31.089 [main            ] INFO  AgentArmoryTest        - Started AgentArmoryTest in 6.375 seconds (process running for 8.065)
25-08-29.20:15:32.066 [pool-2-thread-2 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_model) [3001]
25-08-29.20:15:32.066 [pool-2-thread-1 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_api) [3001]
25-08-29.20:15:32.067 [pool-2-thread-3 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_tool_mcp) [3001]
25-08-29.20:15:32.068 [pool-2-thread-4 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_system_prompt) [3001]
25-08-29.20:15:32.068 [pool-2-thread-5 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_advisor) [3001]
25-08-29.20:15:32.069 [pool-2-thread-6 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client) [3001]
25-08-29.20:15:32.092 [pool-2-thread-3 ] INFO  HikariDataSource       - MainHikariPool - Starting...
25-08-29.20:15:32.375 [pool-2-thread-3 ] INFO  HikariPool             - MainHikariPool - Added connection com.mysql.cj.jdbc.ConnectionImpl@7441e6ca
25-08-29.20:15:32.377 [pool-2-thread-3 ] INFO  HikariDataSource       - MainHikariPool - Start completed.
25-08-29.20:15:32.696 [main            ] INFO  AiClientModelNode      - 注册模型-modelId:2001, modelName:gpt-4.1-mini-成功
25-08-29.20:15:33.574 [main            ] WARN  RetryUtils             - Retry error. Retry count:1
org.springframework.ai.retry.NonTransientAiException: 401 - {"error":{"message":"该令牌已过期 (request id: 2025082920153346695286545595405)","type":"one_api_error"}}
	at org.springframework.ai.retry.RetryUtils$1.handleError(RetryUtils.java:63)
	at org.springframework.web.client.ResponseErrorHandler.handleError(ResponseErrorHandler.java:58)
	at org.springframework.web.client.StatusHandler.lambda$fromErrorHandler$1(StatusHandler.java:71)
	at org.springframework.web.client.StatusHandler.handle(StatusHandler.java:146)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.applyStatusHandlers(DefaultRestClient.java:826)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.lambda$readBody$4(DefaultRestClient.java:815)
	at org.springframework.web.client.DefaultRestClient.readWithMessageConverters(DefaultRestClient.java:215)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.readBody(DefaultRestClient.java:814)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.lambda$toEntityInternal$2(DefaultRestClient.java:770)
	at org.springframework.web.client.DefaultRestClient$DefaultRequestBodyUriSpec.exchangeInternal(DefaultRestClient.java:574)
	at org.springframework.web.client.DefaultRestClient$DefaultRequestBodyUriSpec.exchange(DefaultRestClient.java:535)
	at org.springframework.web.client.RestClient$RequestHeadersSpec.exchange(RestClient.java:677)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.executeAndExtract(DefaultRestClient.java:809)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.toEntityInternal(DefaultRestClient.java:769)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.toEntity(DefaultRestClient.java:758)
	at org.springframework.ai.openai.api.OpenAiApi.chatCompletionEntity(OpenAiApi.java:187)
	at org.springframework.ai.openai.OpenAiChatModel.lambda$internalCall$1(OpenAiChatModel.java:199)
	at org.springframework.retry.support.RetryTemplate.doExecute(RetryTemplate.java:357)
	at org.springframework.retry.support.RetryTemplate.execute(RetryTemplate.java:230)
	at org.springframework.ai.openai.OpenAiChatModel.lambda$internalCall$3(OpenAiChatModel.java:199)
	at io.micrometer.observation.Observation.observe(Observation.java:564)
	at org.springframework.ai.openai.OpenAiChatModel.internalCall(OpenAiChatModel.java:196)
	at org.springframework.ai.openai.OpenAiChatModel.call(OpenAiChatModel.java:181)
	at com.flyxy.ai.test.ai.AgentArmoryTest.test_aiClientModelNode(AgentArmoryTest.java:69)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.springframework.test.context.junit4.statements.RunBeforeTestExecutionCallbacks.evaluate(RunBeforeTestExecutionCallbacks.java:76)
	at org.springframework.test.context.junit4.statements.RunAfterTestExecutionCallbacks.evaluate(RunAfterTestExecutionCallbacks.java:84)
	at org.springframework.test.context.junit4.statements.RunBeforeTestMethodCallbacks.evaluate(RunBeforeTestMethodCallbacks.java:75)
	at org.springframework.test.context.junit4.statements.RunAfterTestMethodCallbacks.evaluate(RunAfterTestMethodCallbacks.java:86)
	at org.springframework.test.context.junit4.statements.SpringRepeat.evaluate(SpringRepeat.java:84)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:252)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:97)
	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at org.springframework.test.context.junit4.statements.RunBeforeTestClassCallbacks.evaluate(RunBeforeTestClassCallbacks.java:61)
	at org.springframework.test.context.junit4.statements.RunAfterTestClassCallbacks.evaluate(RunAfterTestClassCallbacks.java:70)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.run(SpringJUnit4ClassRunner.java:191)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
	at com.intellij.junit4.JUnit4IdeaTestRunner.startRunnerWithArgs(JUnit4IdeaTestRunner.java:69)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater$1.execute(IdeaTestRunner.java:38)
	at com.intellij.rt.execution.junit.TestsRepeater.repeat(TestsRepeater.java:11)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:35)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:231)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:55)
25-08-29.20:15:33.600 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MainHikariPool - Shutdown initiated...
25-08-29.20:15:33.620 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MainHikariPool - Shutdown completed.
25-08-29.20:20:37.214 [main            ] INFO  AgentArmoryTest        - Starting AgentArmoryTest using Java 17.0.12 with PID 37816 (started by 30562 in D:\ACode\project\ai-agent\ai-agent-app)
25-08-29.20:20:37.218 [main            ] INFO  AgentArmoryTest        - The following 1 profile is active: "dev"
25-08-29.20:20:40.111 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-08-29.20:20:42.240 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_openai. Is empty: false
25-08-29.20:20:42.247 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_openai in schema: public
25-08-29.20:20:42.248 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-08-29.20:20:42.372 [main            ] INFO  AgentArmoryTest        - Started AgentArmoryTest in 5.933 seconds (process running for 7.691)
25-08-29.20:20:43.231 [pool-2-thread-1 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_api) [3001]
25-08-29.20:20:43.231 [pool-2-thread-2 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_model) [3001]
25-08-29.20:20:43.231 [pool-2-thread-3 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_tool_mcp) [3001]
25-08-29.20:20:43.231 [pool-2-thread-4 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_system_prompt) [3001]
25-08-29.20:20:43.233 [pool-2-thread-5 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_advisor) [3001]
25-08-29.20:20:43.233 [pool-2-thread-6 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client) [3001]
25-08-29.20:20:43.255 [pool-2-thread-6 ] INFO  HikariDataSource       - MainHikariPool - Starting...
25-08-29.20:20:43.518 [pool-2-thread-6 ] INFO  HikariPool             - MainHikariPool - Added connection com.mysql.cj.jdbc.ConnectionImpl@531f95b2
25-08-29.20:20:43.520 [pool-2-thread-6 ] INFO  HikariDataSource       - MainHikariPool - Start completed.
25-08-29.20:20:43.823 [main            ] INFO  AiClientModelNode      - 注册模型-modelId:2001, modelName:gpt-4.1-mini-成功
25-08-29.20:20:44.574 [main            ] WARN  RetryUtils             - Retry error. Retry count:1
org.springframework.ai.retry.NonTransientAiException: 404 - {"error":{"message":"The model `gpt-4.1-mini` does not exist or you do not have access to it.","type":"invalid_request_error","param":null,"code":"model_not_found"},"request_id":"f5985781-bd81-9ee2-b420-5864bb2db9cd"}
	at org.springframework.ai.retry.RetryUtils$1.handleError(RetryUtils.java:63)
	at org.springframework.web.client.ResponseErrorHandler.handleError(ResponseErrorHandler.java:58)
	at org.springframework.web.client.StatusHandler.lambda$fromErrorHandler$1(StatusHandler.java:71)
	at org.springframework.web.client.StatusHandler.handle(StatusHandler.java:146)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.applyStatusHandlers(DefaultRestClient.java:826)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.lambda$readBody$4(DefaultRestClient.java:815)
	at org.springframework.web.client.DefaultRestClient.readWithMessageConverters(DefaultRestClient.java:215)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.readBody(DefaultRestClient.java:814)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.lambda$toEntityInternal$2(DefaultRestClient.java:770)
	at org.springframework.web.client.DefaultRestClient$DefaultRequestBodyUriSpec.exchangeInternal(DefaultRestClient.java:574)
	at org.springframework.web.client.DefaultRestClient$DefaultRequestBodyUriSpec.exchange(DefaultRestClient.java:535)
	at org.springframework.web.client.RestClient$RequestHeadersSpec.exchange(RestClient.java:677)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.executeAndExtract(DefaultRestClient.java:809)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.toEntityInternal(DefaultRestClient.java:769)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.toEntity(DefaultRestClient.java:758)
	at org.springframework.ai.openai.api.OpenAiApi.chatCompletionEntity(OpenAiApi.java:187)
	at org.springframework.ai.openai.OpenAiChatModel.lambda$internalCall$1(OpenAiChatModel.java:199)
	at org.springframework.retry.support.RetryTemplate.doExecute(RetryTemplate.java:357)
	at org.springframework.retry.support.RetryTemplate.execute(RetryTemplate.java:230)
	at org.springframework.ai.openai.OpenAiChatModel.lambda$internalCall$3(OpenAiChatModel.java:199)
	at io.micrometer.observation.Observation.observe(Observation.java:564)
	at org.springframework.ai.openai.OpenAiChatModel.internalCall(OpenAiChatModel.java:196)
	at org.springframework.ai.openai.OpenAiChatModel.call(OpenAiChatModel.java:181)
	at com.flyxy.ai.test.ai.AgentArmoryTest.test_aiClientModelNode(AgentArmoryTest.java:69)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.springframework.test.context.junit4.statements.RunBeforeTestExecutionCallbacks.evaluate(RunBeforeTestExecutionCallbacks.java:76)
	at org.springframework.test.context.junit4.statements.RunAfterTestExecutionCallbacks.evaluate(RunAfterTestExecutionCallbacks.java:84)
	at org.springframework.test.context.junit4.statements.RunBeforeTestMethodCallbacks.evaluate(RunBeforeTestMethodCallbacks.java:75)
	at org.springframework.test.context.junit4.statements.RunAfterTestMethodCallbacks.evaluate(RunAfterTestMethodCallbacks.java:86)
	at org.springframework.test.context.junit4.statements.SpringRepeat.evaluate(SpringRepeat.java:84)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:252)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:97)
	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at org.springframework.test.context.junit4.statements.RunBeforeTestClassCallbacks.evaluate(RunBeforeTestClassCallbacks.java:61)
	at org.springframework.test.context.junit4.statements.RunAfterTestClassCallbacks.evaluate(RunAfterTestClassCallbacks.java:70)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.run(SpringJUnit4ClassRunner.java:191)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
	at com.intellij.junit4.JUnit4IdeaTestRunner.startRunnerWithArgs(JUnit4IdeaTestRunner.java:69)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater$1.execute(IdeaTestRunner.java:38)
	at com.intellij.rt.execution.junit.TestsRepeater.repeat(TestsRepeater.java:11)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:35)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:231)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:55)
25-08-29.20:20:44.597 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MainHikariPool - Shutdown initiated...
25-08-29.20:20:44.614 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MainHikariPool - Shutdown completed.
25-08-29.20:21:33.786 [main            ] INFO  AgentArmoryTest        - Starting AgentArmoryTest using Java 17.0.12 with PID 35692 (started by 30562 in D:\ACode\project\ai-agent\ai-agent-app)
25-08-29.20:21:33.786 [main            ] INFO  AgentArmoryTest        - The following 1 profile is active: "dev"
25-08-29.20:21:36.405 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-08-29.20:21:38.061 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_openai. Is empty: false
25-08-29.20:21:38.071 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_openai in schema: public
25-08-29.20:21:38.071 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-08-29.20:21:38.163 [main            ] INFO  AgentArmoryTest        - Started AgentArmoryTest in 5.053 seconds (process running for 6.568)
25-08-29.20:21:39.013 [pool-2-thread-1 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_api) [3001]
25-08-29.20:21:39.013 [pool-2-thread-2 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_model) [3001]
25-08-29.20:21:39.013 [pool-2-thread-3 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_tool_mcp) [3001]
25-08-29.20:21:39.013 [pool-2-thread-4 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_system_prompt) [3001]
25-08-29.20:21:39.015 [pool-2-thread-5 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_advisor) [3001]
25-08-29.20:21:39.015 [pool-2-thread-6 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client) [3001]
25-08-29.20:21:39.037 [pool-2-thread-4 ] INFO  HikariDataSource       - MainHikariPool - Starting...
25-08-29.20:21:39.275 [pool-2-thread-4 ] INFO  HikariPool             - MainHikariPool - Added connection com.mysql.cj.jdbc.ConnectionImpl@3b2f371f
25-08-29.20:21:39.277 [pool-2-thread-4 ] INFO  HikariDataSource       - MainHikariPool - Start completed.
25-08-29.20:21:39.529 [main            ] INFO  AiClientModelNode      - 注册模型-modelId:2001, modelName:qwen-plus-成功
25-08-29.20:21:42.612 [main            ] INFO  AgentArmoryTest        - 测试结果：{"metadata":{"empty":false,"id":"chatcmpl-e2be8981-d2d9-9dd5-9a2f-35581a895c2e","model":"qwen-plus","rateLimit":{},"usage":{"promptTokens":17,"completionTokens":67,"totalTokens":84,"nativeUsage":{"completion_tokens":67,"prompt_tokens":17,"prompt_tokens_details":{"cached_tokens":0},"total_tokens":84}}},"result":{"metadata":{"contentFilters":[],"empty":true,"finishReason":"STOP"},"output":{"media":[],"messageType":"ASSISTANT","metadata":{"role":"ASSISTANT","messageType":"ASSISTANT","refusal":"","finishReason":"STOP","annotations":[],"index":0,"id":"chatcmpl-e2be8981-d2d9-9dd5-9a2f-35581a895c2e"},"text":"在当前对话中，你提到的“有哪些工具可以使用？”可能指的是我在Qwen中的工具能力。不过，我目前无法访问具体的工具信息。建议你前往通义实验室官网或Qwen官方文档，获取最新的工具列表和功能说明。如果你有其他具体问题或需要帮助，欢迎随时告诉我！","toolCalls":[]}},"results":[{"metadata":{"contentFilters":[],"empty":true,"finishReason":"STOP"},"output":{"media":[],"messageType":"ASSISTANT","metadata":{"role":"ASSISTANT","messageType":"ASSISTANT","refusal":"","finishReason":"STOP","annotations":[],"index":0,"id":"chatcmpl-e2be8981-d2d9-9dd5-9a2f-35581a895c2e"},"text":"在当前对话中，你提到的“有哪些工具可以使用？”可能指的是我在Qwen中的工具能力。不过，我目前无法访问具体的工具信息。建议你前往通义实验室官网或Qwen官方文档，获取最新的工具列表和功能说明。如果你有其他具体问题或需要帮助，欢迎随时告诉我！","toolCalls":[]}}]}
25-08-29.20:21:42.629 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MainHikariPool - Shutdown initiated...
25-08-29.20:21:42.648 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MainHikariPool - Shutdown completed.
25-08-29.20:22:14.979 [main            ] INFO  AgentArmoryTest        - Starting AgentArmoryTest using Java 17.0.12 with PID 35552 (started by 30562 in D:\ACode\project\ai-agent\ai-agent-app)
25-08-29.20:22:14.981 [main            ] INFO  AgentArmoryTest        - The following 1 profile is active: "dev"
25-08-29.20:22:18.077 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-08-29.20:22:19.921 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_openai. Is empty: false
25-08-29.20:22:19.928 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_openai in schema: public
25-08-29.20:22:19.928 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-08-29.20:22:20.017 [main            ] INFO  AgentArmoryTest        - Started AgentArmoryTest in 5.731 seconds (process running for 7.405)
25-08-29.20:22:20.925 [pool-2-thread-1 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_api) [3001]
25-08-29.20:22:20.925 [pool-2-thread-2 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_model) [3001]
25-08-29.20:22:20.925 [pool-2-thread-3 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_tool_mcp) [3001]
25-08-29.20:22:20.926 [pool-2-thread-4 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_system_prompt) [3001]
25-08-29.20:22:20.926 [pool-2-thread-5 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_advisor) [3001]
25-08-29.20:22:20.928 [pool-2-thread-6 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client) [3001]
25-08-29.20:22:20.951 [pool-2-thread-2 ] INFO  HikariDataSource       - MainHikariPool - Starting...
25-08-29.20:22:21.237 [pool-2-thread-2 ] INFO  HikariPool             - MainHikariPool - Added connection com.mysql.cj.jdbc.ConnectionImpl@2d85c1ad
25-08-29.20:22:21.239 [pool-2-thread-2 ] INFO  HikariDataSource       - MainHikariPool - Start completed.
25-08-29.20:22:21.577 [main            ] INFO  AiClientModelNode      - 注册模型-modelId:2001, modelName:qwen-plus-成功
25-08-29.20:22:56.330 [main            ] INFO  AgentArmoryTest        - 测试结果：{"metadata":{"empty":false,"id":"chatcmpl-fabfc81f-b8c6-9634-a766-7fd7f3d327a9","model":"qwen-plus","rateLimit":{},"usage":{"promptTokens":19,"completionTokens":707,"totalTokens":726,"nativeUsage":{"completion_tokens":707,"prompt_tokens":19,"prompt_tokens_details":{"cached_tokens":0},"total_tokens":726}}},"result":{"metadata":{"contentFilters":[],"empty":true,"finishReason":"STOP"},"output":{"media":[],"messageType":"ASSISTANT","metadata":{"role":"ASSISTANT","messageType":"ASSISTANT","finishReason":"STOP","refusal":"","index":0,"annotations":[],"id":"chatcmpl-fabfc81f-b8c6-9634-a766-7fd7f3d327a9"},"text":"MCP（Multi-Concept Prompting，多概念提示）是一种结合多个概念、指令或任务的提示工程方法，通常用于提升大语言模型（LLM）在复杂任务中的表现。在实际应用中，“MCP工具”可能有不同的含义，具体取决于上下文。以下是几种可能的解释和对应的工具或方法：\n\n1. **提示工程中的MCP方法（Multi-Concept Prompting）相关工具或框架**：\n\n   - **PromptFlow（微软）**：\n     - 用于构建、测试和优化提示流程的工具。\n     - 支持将多个提示模块组合成一个流程，适合实现MCP思想。\n     - GitHub：https://github.com/microsoft/promptflow\n\n   - **LangChain**：\n     - 一个用于构建LLM应用的框架，支持链式调用多个提示、工具和模型。\n     - 可以通过组合多个提示模板和工具调用来实现MCP。\n     - 官网：https://www.langchain.com/\n\n   - **LlamaIndex（原GPT Index）**：\n     - 支持构建基于LLM的数据检索和提示系统。\n     - 可以集成多个数据源和提示模板，适合实现多概念提示。\n\n   - **DSPy（Stanford NLP Group）**：\n     - 一种声明式提示编程框架，允许开发者定义模块化提示逻辑。\n     - 支持自动优化提示策略，适合构建复杂的MCP系统。\n     - GitHub：https://github.com/stanfordnlp/dspy\n\n2. **MCP（Model Control Prompting）相关工具**：\n\n   - 某些研究者将MCP理解为“模型控制提示”，即通过特定的提示来控制模型的行为、风格或输出格式。\n   - 工具包括：\n     - **PromptPerfect**：自动优化提示语的工具。\n     - **Claude（Anthropic）**：支持高度结构化的提示控制。\n     - **OpenAI API + 自定义提示模板**：通过设计结构化提示实现控制。\n\n3. **MCP（Multi-Concept Prompting）开源项目/库**：\n\n   - 目前一些研究论文中提出MCP方法，如：\n     - **Self-Consistency Prompting**\n     - **Tree-of-Thoughts（ToT）**\n     - **Chain-of-Thought + Prompt Ensembling**\n   - 这些方法可以通过以下工具实现：\n     - **Hugging Face Transformers**：用于本地部署模型并实现自定义提示逻辑。\n     - **DeepSpeed / vLLM**：加速推理过程，支持复杂提示策略。\n\n4. **MCP作为具体软件工具（较少见）**：\n\n   - 如果你指的是某个具体的软件工具或平台名为“MCP”，请提供更具体的上下文（如开发环境、用途等），我可以更准确地推荐相关工具。\n\n总结：如果你是在寻找用于实现多概念提示（MCP）的工具或框架，推荐使用：\n\n- LangChain（适合构建多步骤提示流程）\n- DSPy（适合模块化提示编程）\n- PromptFlow（微软出品，适合企业级提示工程）\n- 自定义代码 + Hugging Face Transformers（灵活性高）\n\n如果你有具体的应用场景（如：问答系统、内容生成、数据分析等），我也可以为你推荐最适合的MCP工具组合。欢迎补充更多信息！","toolCalls":[]}},"results":[{"metadata":{"contentFilters":[],"empty":true,"finishReason":"STOP"},"output":{"media":[],"messageType":"ASSISTANT","metadata":{"role":"ASSISTANT","messageType":"ASSISTANT","finishReason":"STOP","refusal":"","index":0,"annotations":[],"id":"chatcmpl-fabfc81f-b8c6-9634-a766-7fd7f3d327a9"},"text":"MCP（Multi-Concept Prompting，多概念提示）是一种结合多个概念、指令或任务的提示工程方法，通常用于提升大语言模型（LLM）在复杂任务中的表现。在实际应用中，“MCP工具”可能有不同的含义，具体取决于上下文。以下是几种可能的解释和对应的工具或方法：\n\n1. **提示工程中的MCP方法（Multi-Concept Prompting）相关工具或框架**：\n\n   - **PromptFlow（微软）**：\n     - 用于构建、测试和优化提示流程的工具。\n     - 支持将多个提示模块组合成一个流程，适合实现MCP思想。\n     - GitHub：https://github.com/microsoft/promptflow\n\n   - **LangChain**：\n     - 一个用于构建LLM应用的框架，支持链式调用多个提示、工具和模型。\n     - 可以通过组合多个提示模板和工具调用来实现MCP。\n     - 官网：https://www.langchain.com/\n\n   - **LlamaIndex（原GPT Index）**：\n     - 支持构建基于LLM的数据检索和提示系统。\n     - 可以集成多个数据源和提示模板，适合实现多概念提示。\n\n   - **DSPy（Stanford NLP Group）**：\n     - 一种声明式提示编程框架，允许开发者定义模块化提示逻辑。\n     - 支持自动优化提示策略，适合构建复杂的MCP系统。\n     - GitHub：https://github.com/stanfordnlp/dspy\n\n2. **MCP（Model Control Prompting）相关工具**：\n\n   - 某些研究者将MCP理解为“模型控制提示”，即通过特定的提示来控制模型的行为、风格或输出格式。\n   - 工具包括：\n     - **PromptPerfect**：自动优化提示语的工具。\n     - **Claude（Anthropic）**：支持高度结构化的提示控制。\n     - **OpenAI API + 自定义提示模板**：通过设计结构化提示实现控制。\n\n3. **MCP（Multi-Concept Prompting）开源项目/库**：\n\n   - 目前一些研究论文中提出MCP方法，如：\n     - **Self-Consistency Prompting**\n     - **Tree-of-Thoughts（ToT）**\n     - **Chain-of-Thought + Prompt Ensembling**\n   - 这些方法可以通过以下工具实现：\n     - **Hugging Face Transformers**：用于本地部署模型并实现自定义提示逻辑。\n     - **DeepSpeed / vLLM**：加速推理过程，支持复杂提示策略。\n\n4. **MCP作为具体软件工具（较少见）**：\n\n   - 如果你指的是某个具体的软件工具或平台名为“MCP”，请提供更具体的上下文（如开发环境、用途等），我可以更准确地推荐相关工具。\n\n总结：如果你是在寻找用于实现多概念提示（MCP）的工具或框架，推荐使用：\n\n- LangChain（适合构建多步骤提示流程）\n- DSPy（适合模块化提示编程）\n- PromptFlow（微软出品，适合企业级提示工程）\n- 自定义代码 + Hugging Face Transformers（灵活性高）\n\n如果你有具体的应用场景（如：问答系统、内容生成、数据分析等），我也可以为你推荐最适合的MCP工具组合。欢迎补充更多信息！","toolCalls":[]}}]}
25-08-29.20:22:56.344 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MainHikariPool - Shutdown initiated...
25-08-29.20:22:56.360 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MainHikariPool - Shutdown completed.
25-08-29.20:35:40.064 [main            ] INFO  AgentArmoryTest        - Starting AgentArmoryTest using Java 17.0.12 with PID 24452 (started by 30562 in D:\ACode\project\ai-agent\ai-agent-app)
25-08-29.20:35:40.066 [main            ] INFO  AgentArmoryTest        - The following 1 profile is active: "dev"
25-08-29.20:35:42.675 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-08-29.20:35:44.464 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_openai. Is empty: false
25-08-29.20:35:44.469 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_openai in schema: public
25-08-29.20:35:44.469 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-08-29.20:35:44.534 [main            ] INFO  AgentArmoryTest        - Started AgentArmoryTest in 5.144 seconds (process running for 6.733)
25-08-29.20:35:45.348 [pool-2-thread-2 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_model) [3001]
25-08-29.20:35:45.348 [pool-2-thread-1 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_api) [3001]
25-08-29.20:35:45.350 [pool-2-thread-3 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_tool_mcp) [3001]
25-08-29.20:35:45.351 [pool-2-thread-4 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_system_prompt) [3001]
25-08-29.20:35:45.351 [pool-2-thread-5 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_advisor) [3001]
25-08-29.20:35:45.353 [pool-2-thread-6 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client) [3001]
25-08-29.20:35:45.374 [pool-2-thread-4 ] INFO  HikariDataSource       - MainHikariPool - Starting...
25-08-29.20:35:45.628 [pool-2-thread-4 ] INFO  HikariPool             - MainHikariPool - Added connection com.mysql.cj.jdbc.ConnectionImpl@12e39e70
25-08-29.20:35:45.630 [pool-2-thread-4 ] INFO  HikariDataSource       - MainHikariPool - Start completed.
25-08-29.20:35:45.904 [main            ] INFO  AiClientModelNode      - 注册模型-modelId:2001, modelName:qwen-plus-成功
25-08-29.20:35:50.556 [main            ] INFO  AgentArmoryTest        - 测试结果：{"metadata":{"empty":false,"id":"chatcmpl-1eb3cd4c-fdd8-9001-bc5e-49c27d7c7583","model":"qwen-plus","rateLimit":{},"usage":{"promptTokens":20,"completionTokens":119,"totalTokens":139,"nativeUsage":{"completion_tokens":119,"prompt_tokens":20,"prompt_tokens_details":{"cached_tokens":0},"total_tokens":139}}},"result":{"metadata":{"contentFilters":[],"empty":true,"finishReason":"STOP"},"output":{"media":[],"messageType":"ASSISTANT","metadata":{"role":"ASSISTANT","messageType":"ASSISTANT","refusal":"","finishReason":"STOP","annotations":[],"index":0,"id":"chatcmpl-1eb3cd4c-fdd8-9001-bc5e-49c27d7c7583"},"text":"要了解今天张家口市桥东区的实时天气情况，建议您使用天气预报应用或访问相关网站，例如：\n\n- **中国天气网**（www.weather.com.cn）  \n- **墨迹天气**（www.mojicn.com）  \n- **AccuWeather** 或 **Weather.com**\n\n这些平台可以提供张家口市桥东区的实时天气、温度、风力、空气质量等详细信息。\n\n如果您告诉我您所在的具体城市或地区，我也可以帮助您查找大致的天气趋势。不过对于最准确的信息，请以实时天气预报为准。","toolCalls":[]}},"results":[{"metadata":{"contentFilters":[],"empty":true,"finishReason":"STOP"},"output":{"media":[],"messageType":"ASSISTANT","metadata":{"role":"ASSISTANT","messageType":"ASSISTANT","refusal":"","finishReason":"STOP","annotations":[],"index":0,"id":"chatcmpl-1eb3cd4c-fdd8-9001-bc5e-49c27d7c7583"},"text":"要了解今天张家口市桥东区的实时天气情况，建议您使用天气预报应用或访问相关网站，例如：\n\n- **中国天气网**（www.weather.com.cn）  \n- **墨迹天气**（www.mojicn.com）  \n- **AccuWeather** 或 **Weather.com**\n\n这些平台可以提供张家口市桥东区的实时天气、温度、风力、空气质量等详细信息。\n\n如果您告诉我您所在的具体城市或地区，我也可以帮助您查找大致的天气趋势。不过对于最准确的信息，请以实时天气预报为准。","toolCalls":[]}}]}
25-08-29.20:35:50.571 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MainHikariPool - Shutdown initiated...
25-08-29.20:35:50.588 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MainHikariPool - Shutdown completed.
25-08-29.20:36:20.855 [main            ] INFO  AgentArmoryTest        - Starting AgentArmoryTest using Java 17.0.12 with PID 25352 (started by 30562 in D:\ACode\project\ai-agent\ai-agent-app)
25-08-29.20:36:20.858 [main            ] INFO  AgentArmoryTest        - The following 1 profile is active: "dev"
25-08-29.20:36:23.619 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-08-29.20:36:25.362 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_openai. Is empty: false
25-08-29.20:36:25.368 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_openai in schema: public
25-08-29.20:36:25.369 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-08-29.20:36:25.444 [main            ] INFO  AgentArmoryTest        - Started AgentArmoryTest in 5.303 seconds (process running for 6.934)
25-08-29.20:36:26.260 [pool-2-thread-1 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_api) [3001]
25-08-29.20:36:26.260 [pool-2-thread-3 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_tool_mcp) [3001]
25-08-29.20:36:26.260 [pool-2-thread-2 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_model) [3001]
25-08-29.20:36:26.261 [pool-2-thread-4 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_system_prompt) [3001]
25-08-29.20:36:26.262 [pool-2-thread-5 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_advisor) [3001]
25-08-29.20:36:26.262 [pool-2-thread-6 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client) [3001]
25-08-29.20:36:26.282 [pool-2-thread-3 ] INFO  HikariDataSource       - MainHikariPool - Starting...
25-08-29.20:36:26.532 [pool-2-thread-3 ] INFO  HikariPool             - MainHikariPool - Added connection com.mysql.cj.jdbc.ConnectionImpl@348a6d16
25-08-29.20:36:26.536 [pool-2-thread-3 ] INFO  HikariDataSource       - MainHikariPool - Start completed.
25-08-29.20:36:26.797 [main            ] INFO  AiClientModelNode      - 注册模型-modelId:2001, modelName:qwen-plus-成功
25-08-29.20:36:29.996 [main            ] INFO  AgentArmoryTest        - 测试结果：{"metadata":{"empty":false,"id":"chatcmpl-2a6e3f7b-9262-9bea-b2ee-0ad90a401b2a","model":"qwen-plus","rateLimit":{},"usage":{"promptTokens":15,"completionTokens":89,"totalTokens":104,"nativeUsage":{"completion_tokens":89,"prompt_tokens":15,"prompt_tokens_details":{"cached_tokens":0},"total_tokens":104}}},"result":{"metadata":{"contentFilters":[],"empty":true,"finishReason":"STOP"},"output":{"media":[],"messageType":"ASSISTANT","metadata":{"role":"ASSISTANT","messageType":"ASSISTANT","refusal":"","finishReason":"STOP","annotations":[],"index":0,"id":"chatcmpl-2a6e3f7b-9262-9bea-b2ee-0ad90a401b2a"},"text":"我是通义千问，是阿里巴巴集团旗下的通义实验室自主研发的超大规模语言模型。我能够回答问题、创作文字，比如写故事、写公文、写邮件、写剧本、逻辑推理、编程等等，还能表达观点，玩游戏等。我支持多种语言，包括但不限于中文、英文、德语、法语、西班牙语等。如果你有任何问题或需要帮助，欢迎随时向我提问！","toolCalls":[]}},"results":[{"metadata":{"contentFilters":[],"empty":true,"finishReason":"STOP"},"output":{"media":[],"messageType":"ASSISTANT","metadata":{"role":"ASSISTANT","messageType":"ASSISTANT","refusal":"","finishReason":"STOP","annotations":[],"index":0,"id":"chatcmpl-2a6e3f7b-9262-9bea-b2ee-0ad90a401b2a"},"text":"我是通义千问，是阿里巴巴集团旗下的通义实验室自主研发的超大规模语言模型。我能够回答问题、创作文字，比如写故事、写公文、写邮件、写剧本、逻辑推理、编程等等，还能表达观点，玩游戏等。我支持多种语言，包括但不限于中文、英文、德语、法语、西班牙语等。如果你有任何问题或需要帮助，欢迎随时向我提问！","toolCalls":[]}}]}
25-08-29.20:36:30.010 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MainHikariPool - Shutdown initiated...
25-08-29.20:36:30.027 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MainHikariPool - Shutdown completed.
