25-08-30.15:49:19.048 [main            ] INFO  AgentArmoryTest        - Starting AgentArmoryTest using Java 17.0.12 with PID 39480 (started by 30562 in D:\ACode\project\ai-agent\ai-agent-app)
25-08-30.15:49:19.050 [main            ] INFO  AgentArmoryTest        - The following 1 profile is active: "dev"
25-08-30.15:49:22.652 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_openai. Is empty: false
25-08-30.15:49:22.656 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_openai in schema: public
25-08-30.15:49:22.658 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-08-30.15:49:23.044 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-08-30.15:49:23.668 [main            ] INFO  AgentArmoryTest        - Started AgentArmoryTest in 5.321 seconds (process running for 6.94)
25-08-30.15:49:24.539 [main            ] INFO  RootNode               - AIClient装配链---RootNode---开始查询配置信息
25-08-30.15:49:24.540 [pool-2-thread-1 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_api) [3001]
25-08-30.15:49:24.540 [pool-2-thread-2 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_model) [3001]
25-08-30.15:49:24.540 [pool-2-thread-3 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_tool_mcp) [3001]
25-08-30.15:49:24.542 [pool-2-thread-4 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_system_prompt) [3001]
25-08-30.15:49:24.542 [pool-2-thread-5 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_advisor) [3001]
25-08-30.15:49:24.542 [pool-2-thread-6 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client) [3001]
25-08-30.15:49:24.561 [pool-2-thread-5 ] INFO  HikariDataSource       - MainHikariPool - Starting...
25-08-30.15:49:24.843 [pool-2-thread-5 ] INFO  HikariPool             - MainHikariPool - Added connection com.mysql.cj.jdbc.ConnectionImpl@3e117828
25-08-30.15:49:24.845 [pool-2-thread-5 ] INFO  HikariDataSource       - MainHikariPool - Start completed.
25-08-30.15:49:25.129 [main            ] INFO  RootNode               - AIClient装配链---RootNode---查询配置信息完毕
25-08-30.15:49:25.129 [main            ] INFO  AiClientApiNode        - AIClient装配链---AiClientApiNode---开始装配API
25-08-30.15:49:25.137 [main            ] INFO  AiClientMcpNode        - AIClient装配链---AiClientMcpNode---开始装配MCP
25-08-30.15:49:25.137 [main            ] INFO  AiClientModelNode      - AIClient装配链---AiClientModelNode---开始装配Model
25-08-30.15:49:25.138 [main            ] INFO  AiClientModelNode      - 注册模型-modelId:2001, modelName:qwen-plus-成功
25-08-30.15:49:25.138 [main            ] INFO  AiClientAdvisorNode    - AIClient装配链---AiClientAdvisorNode---开始装配顾问
25-08-30.15:49:25.164 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MainHikariPool - Shutdown initiated...
25-08-30.15:49:25.216 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MainHikariPool - Shutdown completed.
25-08-30.15:51:03.129 [main            ] INFO  AgentArmoryTest        - Starting AgentArmoryTest using Java 17.0.12 with PID 17444 (started by 30562 in D:\ACode\project\ai-agent\ai-agent-app)
25-08-30.15:51:03.131 [main            ] INFO  AgentArmoryTest        - The following 1 profile is active: "dev"
25-08-30.15:51:06.534 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_openai. Is empty: false
25-08-30.15:51:06.541 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_openai in schema: public
25-08-30.15:51:06.541 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-08-30.15:51:06.937 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-08-30.15:51:07.619 [main            ] INFO  AgentArmoryTest        - Started AgentArmoryTest in 5.167 seconds (process running for 6.642)
25-08-30.15:51:08.438 [main            ] INFO  RootNode               - AIClient装配链---RootNode---开始查询配置信息
25-08-30.15:51:08.440 [pool-2-thread-1 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_api) [3001]
25-08-30.15:51:08.440 [pool-2-thread-2 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_model) [3001]
25-08-30.15:51:08.440 [pool-2-thread-3 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_tool_mcp) [3001]
25-08-30.15:51:08.440 [pool-2-thread-4 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_system_prompt) [3001]
25-08-30.15:51:08.440 [pool-2-thread-5 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_advisor) [3001]
25-08-30.15:51:08.442 [pool-2-thread-6 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client) [3001]
25-08-30.15:51:08.462 [pool-2-thread-2 ] INFO  HikariDataSource       - MainHikariPool - Starting...
25-08-30.15:51:08.710 [pool-2-thread-2 ] INFO  HikariPool             - MainHikariPool - Added connection com.mysql.cj.jdbc.ConnectionImpl@20f8c0b0
25-08-30.15:51:08.712 [pool-2-thread-2 ] INFO  HikariDataSource       - MainHikariPool - Start completed.
25-08-30.15:51:08.787 [main            ] INFO  RootNode               - AIClient装配链---RootNode---查询配置信息完毕
25-08-30.15:51:08.787 [main            ] INFO  AiClientApiNode        - AIClient装配链---AiClientApiNode---开始装配API
25-08-30.15:51:08.796 [main            ] INFO  AiClientMcpNode        - AIClient装配链---AiClientMcpNode---开始装配MCP
25-08-30.15:51:08.796 [main            ] INFO  AiClientModelNode      - AIClient装配链---AiClientModelNode---开始装配Model
25-08-30.15:51:08.797 [main            ] INFO  AiClientModelNode      - 注册模型-modelId:2001, modelName:qwen-plus-成功
25-08-30.15:51:08.797 [main            ] INFO  AiClientAdvisorNode    - AIClient装配链---AiClientAdvisorNode---开始装配顾问
25-08-30.15:51:08.829 [main            ] INFO  AiClientAdvisorNode    - 注册顾问---advisorId:4001, advisorName:记忆---成功
25-08-30.15:51:08.829 [main            ] INFO  AiClientNode           - AIClient装配链---AiClientNode---开始装配Client
25-08-30.15:51:08.837 [main            ] INFO  AiClientNode           - 注册Client---clientId:3001, clientName:提示词优化---成功
25-08-30.15:51:34.043 [main            ] INFO  AgentArmoryTest        - 测试结果：# Role: 内容发布智能体

## Profile

- language: 中文
- description: 一个能够根据用户输入信息生成高质量技术文章，自动发布到CSDN平台，并完成微信公众号消息通知的AI智能体
- background: 基于Planning模式的智能内容处理系统，具备文章生成、平台交互和消息推送能力
- personality: 专业、高效、细致、可靠
- expertise: 技术文章创作、CSDN平台运营、微信公众号消息推送
- target_audience: 开发者、技术爱好者、内容创作者

## Skills

1. 内容创作技能
   - 语义理解：准确解析用户输入内容生成技术文章
   - 标题优化：提炼包含技术点的专业文章标题
   - 内容结构化：组织符合技术写作规范的文章结构
   - 标签提取：识别并提取文章核心技术关键词

2. 平台操作技能
   - CSDN对接：完成文章发布全流程操作
   - URL获取：准确抓取发布后的文章链接地址
   - 消息模板生成：构建符合微信公众号要求的推送模板
   - 多平台协调：确保内容在不同平台间的一致性

## Rules

1. 基本原则：
   - 内容准确性：确保生成文章的技术细节准确无误
   - 信息完整性：包含标题、正文、标签、简述等全部必要元素
   - 格式规范性：遵循各平台的内容格式要求
   - 流程顺序性：严格按照工作流程顺序执行各步骤

2. 行为准则：
   - 数据保密：不泄露用户未公开的技术内容
   - 时效保障：当天完成文章生成和发布全流程
   - 质量控制：确保文章达到技术社区专业标准
   - 异常处理：遇到发布失败等情况自动启动重试机制

3. 限制条件：
   - 不处理非技术类内容
   - 不修改用户提供的原始技术信息
   - 不自动添加未获授权的第三方链接
   - 不跨平台推送未经验证的内容

## Workflows

- 目标: 完成从内容生成到多平台发布的全流程操作
- 步骤 1: 解析用户输入，生成符合技术写作规范的文章，包含标题（含技术点）、正文、标签（英文逗号分隔）、简述（100字）
- 步骤 2: 执行CSDN平台文章发布操作，获取文章URL地址
- 步骤 3: 构建微信公众号消息模板，包含平台标识（CSDN）、文章标题、100字简述、https开头的CSDN文章链接
- 预期结果: 完成高质量技术文章的自动生成与发布，实现跨平台内容同步与消息通知

## Initialization

作为内容发布智能体，你必须遵守上述Rules，按照Workflows执行任务。
25-08-30.15:51:34.055 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MainHikariPool - Shutdown initiated...
25-08-30.15:51:34.075 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MainHikariPool - Shutdown completed.
