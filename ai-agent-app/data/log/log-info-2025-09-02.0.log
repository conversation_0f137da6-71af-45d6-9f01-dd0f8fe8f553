25-09-02.09:30:52.952 [main            ] INFO  AutoAgentTest          - Starting AutoAgentTest using Java 17.0.12 with PID 31260 (started by 30562 in D:\ACode\project\ai-agent\ai-agent-app)
25-09-02.09:30:52.954 [main            ] INFO  AutoAgentTest          - The following 1 profile is active: "dev"
25-09-02.09:30:53.254 [main            ] WARN  GenericWebApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanDefinitionStoreException: Failed to parse configuration class [com.flyxy.ai.Application]
25-09-02.09:30:53.294 [main            ] ERROR SpringApplication      - Application run failed
org.springframework.beans.factory.BeanDefinitionStoreException: Failed to parse configuration class [com.flyxy.ai.Application]
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:185)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.processConfigBeanDefinitions(ConfigurationClassPostProcessor.java:418)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.postProcessBeanDefinitionRegistry(ConfigurationClassPostProcessor.java:290)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanDefinitionRegistryPostProcessors(PostProcessorRegistrationDelegate.java:349)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:118)
	at org.springframework.context.support.AbstractApplicationContext.invokeBeanFactoryPostProcessors(AbstractApplicationContext.java:791)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:609)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.springframework.boot.test.context.SpringBootContextLoader.lambda$loadContext$3(SpringBootContextLoader.java:137)
	at org.springframework.util.function.ThrowingSupplier.get(ThrowingSupplier.java:58)
	at org.springframework.util.function.ThrowingSupplier.get(ThrowingSupplier.java:46)
	at org.springframework.boot.SpringApplication.withHook(SpringApplication.java:1461)
	at org.springframework.boot.test.context.SpringBootContextLoader$ContextLoaderHook.run(SpringBootContextLoader.java:553)
	at org.springframework.boot.test.context.SpringBootContextLoader.loadContext(SpringBootContextLoader.java:137)
	at org.springframework.boot.test.context.SpringBootContextLoader.loadContext(SpringBootContextLoader.java:108)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContextInternal(DefaultCacheAwareContextLoaderDelegate.java:225)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:152)
	at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
	at org.springframework.test.context.web.ServletTestExecutionListener.setUpRequestContextIfNecessary(ServletTestExecutionListener.java:200)
	at org.springframework.test.context.web.ServletTestExecutionListener.prepareTestInstance(ServletTestExecutionListener.java:139)
	at org.springframework.test.context.TestContextManager.prepareTestInstance(TestContextManager.java:260)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.createTest(SpringJUnit4ClassRunner.java:228)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner$1.runReflectiveCall(SpringJUnit4ClassRunner.java:289)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.methodBlock(SpringJUnit4ClassRunner.java:291)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:247)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:97)
	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at org.springframework.test.context.junit4.statements.RunBeforeTestClassCallbacks.evaluate(RunBeforeTestClassCallbacks.java:61)
	at org.springframework.test.context.junit4.statements.RunAfterTestClassCallbacks.evaluate(RunAfterTestClassCallbacks.java:70)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.run(SpringJUnit4ClassRunner.java:191)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
	at com.intellij.junit4.JUnit4IdeaTestRunner.startRunnerWithArgs(JUnit4IdeaTestRunner.java:69)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater$1.execute(IdeaTestRunner.java:38)
	at com.intellij.rt.execution.junit.TestsRepeater.repeat(TestsRepeater.java:11)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:35)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:231)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:55)
Caused by: org.springframework.context.annotation.ConflictingBeanDefinitionException: Annotation-specified bean name 'rootNode' for bean class [com.flyxy.ai.domain.agent.service.exec.auto.step.RootNode] conflicts with existing, non-compatible bean definition of same name and class [com.flyxy.ai.domain.agent.service.armory.RootNode]
	at org.springframework.context.annotation.ClassPathBeanDefinitionScanner.checkCandidate(ClassPathBeanDefinitionScanner.java:361)
	at org.springframework.context.annotation.ClassPathBeanDefinitionScanner.doScan(ClassPathBeanDefinitionScanner.java:288)
	at org.springframework.context.annotation.ComponentScanAnnotationParser.parse(ComponentScanAnnotationParser.java:128)
	at org.springframework.context.annotation.ConfigurationClassParser.doProcessConfigurationClass(ConfigurationClassParser.java:332)
	at org.springframework.context.annotation.ConfigurationClassParser.processConfigurationClass(ConfigurationClassParser.java:267)
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:193)
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:171)
	... 45 common frames omitted
25-09-02.09:30:53.299 [main            ] WARN  TestContextManager     - Caught exception while allowing TestExecutionListener [org.springframework.test.context.web.ServletTestExecutionListener] to prepare test instance [com.flyxy.ai.test.ai.AutoAgentTest@47ec7422]
java.lang.IllegalStateException: Failed to load ApplicationContext for [WebMergedContextConfiguration@dcc6211 testClass = com.flyxy.ai.test.ai.AutoAgentTest, locations = [], classes = [com.flyxy.ai.Application], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = ["org.springframework.boot.test.context.SpringBootTestContextBootstrapper=true"], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@9f116cc, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@7ec7ffd3, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.web.client.TestRestTemplateContextCustomizer@49ec71f8, org.springframework.boot.test.web.reactive.server.WebTestClientContextCustomizer@3febb011, org.springframework.boot.test.web.reactor.netty.DisableReactorResourceFactoryGlobalResourcesContextCustomizerFactory$DisableReactorResourceFactoryGlobalResourcesContextCustomizerCustomizer@1b7cc17c, org.springframework.boot.test.autoconfigure.OnFailureConditionReportContextCustomizerFactory$OnFailureConditionReportContextCustomizer@67c27493, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@4c583ecf, org.springframework.test.context.support.DynamicPropertiesContextCustomizer@0, org.springframework.boot.test.context.SpringBootTestAnnotation@182f72c7], resourceBasePath = "src/main/webapp", contextLoader = org.springframework.boot.test.context.SpringBootContextLoader, parent = null]
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:180)
	at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
	at org.springframework.test.context.web.ServletTestExecutionListener.setUpRequestContextIfNecessary(ServletTestExecutionListener.java:200)
	at org.springframework.test.context.web.ServletTestExecutionListener.prepareTestInstance(ServletTestExecutionListener.java:139)
	at org.springframework.test.context.TestContextManager.prepareTestInstance(TestContextManager.java:260)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.createTest(SpringJUnit4ClassRunner.java:228)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner$1.runReflectiveCall(SpringJUnit4ClassRunner.java:289)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.methodBlock(SpringJUnit4ClassRunner.java:291)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:247)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:97)
	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at org.springframework.test.context.junit4.statements.RunBeforeTestClassCallbacks.evaluate(RunBeforeTestClassCallbacks.java:61)
	at org.springframework.test.context.junit4.statements.RunAfterTestClassCallbacks.evaluate(RunAfterTestClassCallbacks.java:70)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.run(SpringJUnit4ClassRunner.java:191)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
	at com.intellij.junit4.JUnit4IdeaTestRunner.startRunnerWithArgs(JUnit4IdeaTestRunner.java:69)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater$1.execute(IdeaTestRunner.java:38)
	at com.intellij.rt.execution.junit.TestsRepeater.repeat(TestsRepeater.java:11)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:35)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:231)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:55)
Caused by: org.springframework.beans.factory.BeanDefinitionStoreException: Failed to parse configuration class [com.flyxy.ai.Application]
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:185)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.processConfigBeanDefinitions(ConfigurationClassPostProcessor.java:418)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.postProcessBeanDefinitionRegistry(ConfigurationClassPostProcessor.java:290)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanDefinitionRegistryPostProcessors(PostProcessorRegistrationDelegate.java:349)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:118)
	at org.springframework.context.support.AbstractApplicationContext.invokeBeanFactoryPostProcessors(AbstractApplicationContext.java:791)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:609)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.springframework.boot.test.context.SpringBootContextLoader.lambda$loadContext$3(SpringBootContextLoader.java:137)
	at org.springframework.util.function.ThrowingSupplier.get(ThrowingSupplier.java:58)
	at org.springframework.util.function.ThrowingSupplier.get(ThrowingSupplier.java:46)
	at org.springframework.boot.SpringApplication.withHook(SpringApplication.java:1461)
	at org.springframework.boot.test.context.SpringBootContextLoader$ContextLoaderHook.run(SpringBootContextLoader.java:553)
	at org.springframework.boot.test.context.SpringBootContextLoader.loadContext(SpringBootContextLoader.java:137)
	at org.springframework.boot.test.context.SpringBootContextLoader.loadContext(SpringBootContextLoader.java:108)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContextInternal(DefaultCacheAwareContextLoaderDelegate.java:225)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:152)
	... 27 common frames omitted
Caused by: org.springframework.context.annotation.ConflictingBeanDefinitionException: Annotation-specified bean name 'rootNode' for bean class [com.flyxy.ai.domain.agent.service.exec.auto.step.RootNode] conflicts with existing, non-compatible bean definition of same name and class [com.flyxy.ai.domain.agent.service.armory.RootNode]
	at org.springframework.context.annotation.ClassPathBeanDefinitionScanner.checkCandidate(ClassPathBeanDefinitionScanner.java:361)
	at org.springframework.context.annotation.ClassPathBeanDefinitionScanner.doScan(ClassPathBeanDefinitionScanner.java:288)
	at org.springframework.context.annotation.ComponentScanAnnotationParser.parse(ComponentScanAnnotationParser.java:128)
	at org.springframework.context.annotation.ConfigurationClassParser.doProcessConfigurationClass(ConfigurationClassParser.java:332)
	at org.springframework.context.annotation.ConfigurationClassParser.processConfigurationClass(ConfigurationClassParser.java:267)
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:193)
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:171)
	... 45 common frames omitted
25-09-02.09:32:12.355 [main            ] INFO  AutoAgentTest          - Starting AutoAgentTest using Java 17.0.12 with PID 24244 (started by 30562 in D:\ACode\project\ai-agent\ai-agent-app)
25-09-02.09:32:12.356 [main            ] INFO  AutoAgentTest          - The following 1 profile is active: "dev"
25-09-02.09:32:15.149 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_openai. Is empty: false
25-09-02.09:32:15.154 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_openai in schema: public
25-09-02.09:32:15.154 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-09-02.09:32:15.476 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-09-02.09:32:15.981 [main            ] INFO  AutoAgentTest          - Started AutoAgentTest in 4.142 seconds (process running for 5.404)
25-09-02.09:32:16.000 [main            ] WARN  TestContextManager     - Caught exception while allowing TestExecutionListener [org.springframework.test.context.support.DependencyInjectionTestExecutionListener] to prepare test instance [com.flyxy.ai.test.ai.AutoAgentTest@246e1a1]
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'com.flyxy.ai.test.ai.AutoAgentTest': Injection of resource dependencies failed
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:372)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1445)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireBeanProperties(AbstractAutowireCapableBeanFactory.java:399)
	at org.springframework.test.context.support.DependencyInjectionTestExecutionListener.injectDependencies(DependencyInjectionTestExecutionListener.java:156)
	at org.springframework.test.context.support.DependencyInjectionTestExecutionListener.prepareTestInstance(DependencyInjectionTestExecutionListener.java:111)
	at org.springframework.test.context.TestContextManager.prepareTestInstance(TestContextManager.java:260)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.createTest(SpringJUnit4ClassRunner.java:228)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner$1.runReflectiveCall(SpringJUnit4ClassRunner.java:289)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.methodBlock(SpringJUnit4ClassRunner.java:291)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:247)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:97)
	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at org.springframework.test.context.junit4.statements.RunBeforeTestClassCallbacks.evaluate(RunBeforeTestClassCallbacks.java:61)
	at org.springframework.test.context.junit4.statements.RunAfterTestClassCallbacks.evaluate(RunAfterTestClassCallbacks.java:70)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.run(SpringJUnit4ClassRunner.java:191)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
	at com.intellij.junit4.JUnit4IdeaTestRunner.startRunnerWithArgs(JUnit4IdeaTestRunner.java:69)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater$1.execute(IdeaTestRunner.java:38)
	at com.intellij.rt.execution.junit.TestsRepeater.repeat(TestsRepeater.java:11)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:35)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:231)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:55)
Caused by: org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'com.flyxy.ai.domain.agent.service.exec.auto.step.factory.DefaultAutoAgentExecuteStrategyFactory' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {@jakarta.annotation.Resource(shareable=true, lookup="", name="", description="", authenticationType=CONTAINER, type=java.lang.Object.class, mappedName="")}
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.raiseNoMatchingBeanFound(DefaultListableBeanFactory.java:2177)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1627)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1552)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:599)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:577)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:739)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:272)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:369)
	... 28 common frames omitted
25-09-02.09:33:59.825 [main            ] INFO  AutoAgentTest          - Starting AutoAgentTest using Java 17.0.12 with PID 14720 (started by 30562 in D:\ACode\project\ai-agent\ai-agent-app)
25-09-02.09:33:59.827 [main            ] INFO  AutoAgentTest          - The following 1 profile is active: "dev"
25-09-02.09:34:02.319 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_openai. Is empty: false
25-09-02.09:34:02.325 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_openai in schema: public
25-09-02.09:34:02.325 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-09-02.09:34:02.607 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-09-02.09:34:03.114 [main            ] INFO  AutoAgentTest          - Started AutoAgentTest in 3.809 seconds (process running for 4.982)
25-09-02.09:34:03.880 [main            ] INFO  RootNode               - AIClient装配链---RootNode---开始查询配置信息
25-09-02.09:34:03.882 [pool-2-thread-2 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_model) [3101, 3102, 3103]
25-09-02.09:34:03.882 [pool-2-thread-1 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_api) [3101, 3102, 3103]
25-09-02.09:34:03.882 [pool-2-thread-3 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_tool_mcp) [3101, 3102, 3103]
25-09-02.09:34:03.882 [pool-2-thread-4 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_system_prompt) [3101, 3102, 3103]
25-09-02.09:34:03.883 [pool-2-thread-5 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_advisor) [3101, 3102, 3103]
25-09-02.09:34:03.883 [pool-2-thread-6 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client) [3101, 3102, 3103]
25-09-02.09:34:03.913 [pool-2-thread-3 ] INFO  HikariDataSource       - MainHikariPool - Starting...
25-09-02.09:34:04.166 [pool-2-thread-3 ] INFO  HikariPool             - MainHikariPool - Added connection com.mysql.cj.jdbc.ConnectionImpl@5ed6f204
25-09-02.09:34:04.167 [pool-2-thread-3 ] INFO  HikariDataSource       - MainHikariPool - Start completed.
25-09-02.09:34:04.292 [pool-2-thread-3 ] ERROR AgentRepository        - 解析MCP配置失败
com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.flyxy.ai.domain.agent.model.valobj.AiClientToolMcpVO$TransportConfigStdio$Stdio` (although at least one Creator exists): no String-argument constructor/factory method to deserialize from String value ('http://appbuilder.baidu.com/v2/ai_search/mcp/')
 at [Source: REDACTED (`StreamReadFeature.INCLUDE_SOURCE_IN_LOCATION` disabled); line: 2, column: 12] (through reference chain: java.util.LinkedHashMap["baseUri"])
	at com.fasterxml.jackson.databind.exc.MismatchedInputException.from(MismatchedInputException.java:63)
	at com.fasterxml.jackson.databind.DeserializationContext.reportInputMismatch(DeserializationContext.java:1754)
	at com.fasterxml.jackson.databind.DeserializationContext.handleMissingInstantiator(DeserializationContext.java:1379)
	at com.fasterxml.jackson.databind.deser.std.StdDeserializer._deserializeFromString(StdDeserializer.java:311)
	at com.fasterxml.jackson.databind.deser.BeanDeserializerBase.deserializeFromString(BeanDeserializerBase.java:1592)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer._deserializeOther(BeanDeserializer.java:197)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserialize(BeanDeserializer.java:187)
	at com.fasterxml.jackson.databind.deser.std.MapDeserializer._readAndBindStringKeyMap(MapDeserializer.java:623)
	at com.fasterxml.jackson.databind.deser.std.MapDeserializer.deserialize(MapDeserializer.java:449)
	at com.fasterxml.jackson.databind.deser.std.MapDeserializer.deserialize(MapDeserializer.java:32)
	at com.fasterxml.jackson.databind.deser.DefaultDeserializationContext.readRootValue(DefaultDeserializationContext.java:342)
	at com.fasterxml.jackson.databind.ObjectMapper._readMapAndClose(ObjectMapper.java:4917)
	at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3860)
	at com.flyxy.ai.infrastructure.adapter.repository.AgentRepository.AiClientToolMcpVOByClientIds(AgentRepository.java:191)
	at com.flyxy.ai.domain.agent.service.armory.business.impl.AiClientLoadDataStrategy.lambda$loadData$2(AiClientLoadDataStrategy.java:50)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run$$$capture(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
25-09-02.09:34:04.405 [main            ] INFO  RootNode               - AIClient装配链---RootNode---查询配置信息完毕
25-09-02.09:34:04.406 [main            ] INFO  AiClientApiNode        - AIClient装配链---AiClientApiNode---开始装配API
25-09-02.09:34:04.415 [main            ] INFO  AiClientMcpNode        - AIClient装配链---AiClientMcpNode---开始装配MCP
25-09-02.09:34:05.062 [HttpClient-10-Worker-0] INFO  McpAsyncClient         - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[completions=null, experimental={}, logging=null, prompts=PromptCapabilities[listChanged=false], resources=ResourceCapabilities[subscribe=false, listChanged=false], tools=ToolCapabilities[listChanged=false]], Info: Implementation[name=AIsearch, version=1.4.1] and Instructions null
25-09-02.09:34:05.141 [main            ] INFO  AiClientMcpNode        - sse方式注册mcp: baidu-search 成功
25-09-02.09:34:05.155 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MainHikariPool - Shutdown initiated...
25-09-02.09:34:05.167 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MainHikariPool - Shutdown completed.
25-09-02.15:10:15.336 [main            ] INFO  AutoAgentTest          - Starting AutoAgentTest using Java 17.0.12 with PID 10116 (started by 30562 in D:\ACode\project\ai-agent\ai-agent-app)
25-09-02.15:10:15.339 [main            ] INFO  AutoAgentTest          - The following 1 profile is active: "dev"
25-09-02.15:10:18.367 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_openai. Is empty: false
25-09-02.15:10:18.373 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_openai in schema: public
25-09-02.15:10:18.373 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-09-02.15:10:18.724 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-09-02.15:10:19.362 [main            ] INFO  AutoAgentTest          - Started AutoAgentTest in 4.646 seconds (process running for 6.201)
25-09-02.15:10:20.142 [main            ] INFO  RootNode               - AIClient装配链---RootNode---开始查询配置信息
25-09-02.15:10:20.143 [pool-2-thread-1 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_api) [3101, 3102, 3103]
25-09-02.15:10:20.143 [pool-2-thread-2 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_model) [3101, 3102, 3103]
25-09-02.15:10:20.145 [pool-2-thread-3 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_tool_mcp) [3101, 3102, 3103]
25-09-02.15:10:20.145 [pool-2-thread-4 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_system_prompt) [3101, 3102, 3103]
25-09-02.15:10:20.145 [pool-2-thread-5 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_advisor) [3101, 3102, 3103]
25-09-02.15:10:20.145 [pool-2-thread-6 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client) [3101, 3102, 3103]
25-09-02.15:10:20.167 [pool-2-thread-1 ] INFO  HikariDataSource       - MainHikariPool - Starting...
25-09-02.15:10:20.413 [pool-2-thread-1 ] INFO  HikariPool             - MainHikariPool - Added connection com.mysql.cj.jdbc.ConnectionImpl@59fc69db
25-09-02.15:10:20.414 [pool-2-thread-1 ] INFO  HikariDataSource       - MainHikariPool - Start completed.
25-09-02.15:10:59.714 [pool-2-thread-3 ] ERROR AgentRepository        - 解析MCP配置失败
com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.flyxy.ai.domain.agent.model.valobj.AiClientToolMcpVO$TransportConfigStdio$Stdio` (although at least one Creator exists): no String-argument constructor/factory method to deserialize from String value ('http://appbuilder.baidu.com/v2/ai_search/mcp/')
 at [Source: REDACTED (`StreamReadFeature.INCLUDE_SOURCE_IN_LOCATION` disabled); line: 2, column: 12] (through reference chain: java.util.LinkedHashMap["baseUri"])
	at com.fasterxml.jackson.databind.exc.MismatchedInputException.from(MismatchedInputException.java:63)
	at com.fasterxml.jackson.databind.DeserializationContext.reportInputMismatch(DeserializationContext.java:1754)
	at com.fasterxml.jackson.databind.DeserializationContext.handleMissingInstantiator(DeserializationContext.java:1379)
	at com.fasterxml.jackson.databind.deser.std.StdDeserializer._deserializeFromString(StdDeserializer.java:311)
	at com.fasterxml.jackson.databind.deser.BeanDeserializerBase.deserializeFromString(BeanDeserializerBase.java:1592)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer._deserializeOther(BeanDeserializer.java:197)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserialize(BeanDeserializer.java:187)
	at com.fasterxml.jackson.databind.deser.std.MapDeserializer._readAndBindStringKeyMap(MapDeserializer.java:623)
	at com.fasterxml.jackson.databind.deser.std.MapDeserializer.deserialize(MapDeserializer.java:449)
	at com.fasterxml.jackson.databind.deser.std.MapDeserializer.deserialize(MapDeserializer.java:32)
	at com.fasterxml.jackson.databind.deser.DefaultDeserializationContext.readRootValue(DefaultDeserializationContext.java:342)
	at com.fasterxml.jackson.databind.ObjectMapper._readMapAndClose(ObjectMapper.java:4917)
	at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3860)
	at com.flyxy.ai.infrastructure.adapter.repository.AgentRepository.AiClientToolMcpVOByClientIds(AgentRepository.java:191)
	at com.flyxy.ai.domain.agent.service.armory.business.impl.AiClientLoadDataStrategy.lambda$loadData$2(AiClientLoadDataStrategy.java:50)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run$$$capture(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
25-09-02.15:10:59.742 [main            ] INFO  RootNode               - AIClient装配链---RootNode---查询配置信息完毕
25-09-02.15:10:59.742 [main            ] INFO  AiClientApiNode        - AIClient装配链---AiClientApiNode---开始装配API
25-09-02.15:10:59.747 [main            ] INFO  AiClientMcpNode        - AIClient装配链---AiClientMcpNode---开始装配MCP
25-09-02.15:11:00.183 [HttpClient-10-Worker-1] INFO  McpAsyncClient         - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[completions=null, experimental={}, logging=null, prompts=PromptCapabilities[listChanged=false], resources=ResourceCapabilities[subscribe=false, listChanged=false], tools=ToolCapabilities[listChanged=false]], Info: Implementation[name=AIsearch, version=1.4.1] and Instructions null
25-09-02.15:11:00.291 [main            ] INFO  AiClientMcpNode        - sse方式注册mcp: baidu-search 成功
25-09-02.15:11:00.308 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MainHikariPool - Shutdown initiated...
25-09-02.15:11:00.318 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MainHikariPool - Shutdown completed.
25-09-02.15:12:27.635 [main            ] INFO  AutoAgentTest          - Starting AutoAgentTest using Java 17.0.12 with PID 32800 (started by 30562 in D:\ACode\project\ai-agent\ai-agent-app)
25-09-02.15:12:27.637 [main            ] INFO  AutoAgentTest          - The following 1 profile is active: "dev"
25-09-02.15:12:30.156 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_openai. Is empty: false
25-09-02.15:12:30.162 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_openai in schema: public
25-09-02.15:12:30.162 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-09-02.15:12:30.451 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-09-02.15:12:30.948 [main            ] INFO  AutoAgentTest          - Started AutoAgentTest in 3.844 seconds (process running for 5.078)
25-09-02.15:12:31.636 [main            ] INFO  RootNode               - AIClient装配链---RootNode---开始查询配置信息
25-09-02.15:12:31.638 [pool-2-thread-2 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_model) [3101, 3102, 3103]
25-09-02.15:12:31.638 [pool-2-thread-1 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_api) [3101, 3102, 3103]
25-09-02.15:12:31.638 [pool-2-thread-3 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_tool_mcp) [3101, 3102, 3103]
25-09-02.15:12:31.639 [pool-2-thread-4 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_system_prompt) [3101, 3102, 3103]
25-09-02.15:12:31.639 [pool-2-thread-5 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_advisor) [3101, 3102, 3103]
25-09-02.15:12:31.639 [pool-2-thread-6 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client) [3101, 3102, 3103]
25-09-02.15:12:31.659 [pool-2-thread-5 ] INFO  HikariDataSource       - MainHikariPool - Starting...
25-09-02.15:12:31.886 [pool-2-thread-5 ] INFO  HikariPool             - MainHikariPool - Added connection com.mysql.cj.jdbc.ConnectionImpl@349226ce
25-09-02.15:12:31.889 [pool-2-thread-5 ] INFO  HikariDataSource       - MainHikariPool - Start completed.
25-09-02.15:12:32.092 [main            ] INFO  RootNode               - AIClient装配链---RootNode---查询配置信息完毕
25-09-02.15:12:32.092 [main            ] INFO  AiClientApiNode        - AIClient装配链---AiClientApiNode---开始装配API
25-09-02.15:12:32.101 [main            ] INFO  AiClientMcpNode        - AIClient装配链---AiClientMcpNode---开始装配MCP
25-09-02.15:12:32.420 [HttpClient-10-Worker-1] INFO  McpAsyncClient         - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[completions=null, experimental={}, logging=null, prompts=PromptCapabilities[listChanged=false], resources=ResourceCapabilities[subscribe=false, listChanged=false], tools=ToolCapabilities[listChanged=false]], Info: Implementation[name=AIsearch, version=1.4.1] and Instructions null
25-09-02.15:12:32.497 [main            ] INFO  AiClientMcpNode        - sse方式注册mcp: baidu-search 成功
25-09-02.15:12:32.514 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MainHikariPool - Shutdown initiated...
25-09-02.15:12:32.547 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MainHikariPool - Shutdown completed.
25-09-02.15:14:45.295 [main            ] INFO  AutoAgentTest          - Starting AutoAgentTest using Java 17.0.12 with PID 4876 (started by 30562 in D:\ACode\project\ai-agent\ai-agent-app)
25-09-02.15:14:45.297 [main            ] INFO  AutoAgentTest          - The following 1 profile is active: "dev"
25-09-02.15:14:47.739 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_openai. Is empty: false
25-09-02.15:14:47.743 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_openai in schema: public
25-09-02.15:14:47.743 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-09-02.15:14:48.016 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-09-02.15:14:48.596 [main            ] INFO  AutoAgentTest          - Started AutoAgentTest in 3.831 seconds (process running for 5.039)
25-09-02.15:14:49.268 [main            ] INFO  RootNode               - AIClient装配链---RootNode---开始查询配置信息
25-09-02.15:14:49.270 [pool-2-thread-1 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_api) [3101, 3102, 3103]
25-09-02.15:14:49.270 [pool-2-thread-2 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_model) [3101, 3102, 3103]
25-09-02.15:14:49.270 [pool-2-thread-3 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_tool_mcp) [3101, 3102, 3103]
25-09-02.15:14:49.270 [pool-2-thread-4 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_system_prompt) [3101, 3102, 3103]
25-09-02.15:14:49.270 [pool-2-thread-5 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_advisor) [3101, 3102, 3103]
25-09-02.15:14:49.272 [pool-2-thread-6 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client) [3101, 3102, 3103]
25-09-02.15:14:49.290 [pool-2-thread-1 ] INFO  HikariDataSource       - MainHikariPool - Starting...
25-09-02.15:14:49.511 [pool-2-thread-1 ] INFO  HikariPool             - MainHikariPool - Added connection com.mysql.cj.jdbc.ConnectionImpl@3c4132b3
25-09-02.15:14:49.512 [pool-2-thread-1 ] INFO  HikariDataSource       - MainHikariPool - Start completed.
25-09-02.15:14:49.710 [main            ] INFO  RootNode               - AIClient装配链---RootNode---查询配置信息完毕
25-09-02.15:14:49.712 [main            ] INFO  AiClientApiNode        - AIClient装配链---AiClientApiNode---开始装配API
25-09-02.15:14:49.720 [main            ] INFO  AiClientMcpNode        - AIClient装配链---AiClientMcpNode---开始装配MCP
25-09-02.15:14:50.036 [HttpClient-10-Worker-1] INFO  McpAsyncClient         - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[completions=null, experimental={}, logging=null, prompts=PromptCapabilities[listChanged=false], resources=ResourceCapabilities[subscribe=false, listChanged=false], tools=ToolCapabilities[listChanged=false]], Info: Implementation[name=AIsearch, version=1.4.1] and Instructions null
25-09-02.15:14:50.102 [main            ] INFO  AiClientMcpNode        - sse方式注册mcp: baidu-search 成功
25-09-02.15:14:50.102 [main            ] INFO  AiClientModelNode      - AIClient装配链---AiClientModelNode---开始装配Model
25-09-02.15:14:50.104 [main            ] INFO  AiClientModelNode      - 注册模型-modelId:2001, modelName:gpt-4.1-mini-成功
25-09-02.15:14:50.104 [main            ] INFO  AiClientAdvisorNode    - AIClient装配链---AiClientAdvisorNode---开始装配顾问
25-09-02.15:14:50.120 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MainHikariPool - Shutdown initiated...
25-09-02.15:14:50.132 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MainHikariPool - Shutdown completed.
25-09-02.15:15:44.770 [main            ] INFO  AutoAgentTest          - Starting AutoAgentTest using Java 17.0.12 with PID 22076 (started by 30562 in D:\ACode\project\ai-agent\ai-agent-app)
25-09-02.15:15:44.772 [main            ] INFO  AutoAgentTest          - The following 1 profile is active: "dev"
25-09-02.15:15:47.161 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_openai. Is empty: false
25-09-02.15:15:47.165 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_openai in schema: public
25-09-02.15:15:47.165 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-09-02.15:15:47.446 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-09-02.15:15:47.943 [main            ] INFO  AutoAgentTest          - Started AutoAgentTest in 3.686 seconds (process running for 4.902)
25-09-02.15:15:48.629 [main            ] INFO  RootNode               - AIClient装配链---RootNode---开始查询配置信息
25-09-02.15:15:48.631 [pool-2-thread-1 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_api) [3101, 3102, 3103]
25-09-02.15:15:48.631 [pool-2-thread-2 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_model) [3101, 3102, 3103]
25-09-02.15:15:48.631 [pool-2-thread-3 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_tool_mcp) [3101, 3102, 3103]
25-09-02.15:15:48.632 [pool-2-thread-4 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_system_prompt) [3101, 3102, 3103]
25-09-02.15:15:48.632 [pool-2-thread-5 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_advisor) [3101, 3102, 3103]
25-09-02.15:15:48.632 [pool-2-thread-6 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client) [3101, 3102, 3103]
25-09-02.15:15:48.648 [pool-2-thread-5 ] INFO  HikariDataSource       - MainHikariPool - Starting...
25-09-02.15:15:48.861 [pool-2-thread-5 ] INFO  HikariPool             - MainHikariPool - Added connection com.mysql.cj.jdbc.ConnectionImpl@2b12b382
25-09-02.15:15:48.862 [pool-2-thread-5 ] INFO  HikariDataSource       - MainHikariPool - Start completed.
25-09-02.15:15:49.059 [main            ] INFO  RootNode               - AIClient装配链---RootNode---查询配置信息完毕
25-09-02.15:15:49.059 [main            ] INFO  AiClientApiNode        - AIClient装配链---AiClientApiNode---开始装配API
25-09-02.15:15:49.067 [main            ] INFO  AiClientMcpNode        - AIClient装配链---AiClientMcpNode---开始装配MCP
25-09-02.15:15:49.374 [HttpClient-10-Worker-0] INFO  McpAsyncClient         - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[completions=null, experimental={}, logging=null, prompts=PromptCapabilities[listChanged=false], resources=ResourceCapabilities[subscribe=false, listChanged=false], tools=ToolCapabilities[listChanged=false]], Info: Implementation[name=AIsearch, version=1.4.1] and Instructions null
25-09-02.15:15:49.447 [main            ] INFO  AiClientMcpNode        - sse方式注册mcp: baidu-search 成功
25-09-02.15:15:49.447 [main            ] INFO  AiClientModelNode      - AIClient装配链---AiClientModelNode---开始装配Model
25-09-02.15:15:49.449 [main            ] INFO  AiClientModelNode      - 注册模型-modelId:2001, modelName:gpt-4.1-mini-成功
25-09-02.15:15:49.449 [main            ] INFO  AiClientAdvisorNode    - AIClient装配链---AiClientAdvisorNode---开始装配顾问
25-09-02.15:15:49.458 [main            ] INFO  AiClientAdvisorNode    - 注册顾问---advisorId:4001, advisorName:记忆---成功
25-09-02.15:15:49.460 [main            ] INFO  AiClientNode           - AIClient装配链---AiClientNode---开始装配Client
25-09-02.15:15:49.546 [main            ] INFO  AiClientNode           - 注册Client---clientId:3101, clientName:任务分析和状态判断---成功
25-09-02.15:15:49.607 [main            ] INFO  AiClientNode           - 注册Client---clientId:3102, clientName:具体任务执行---成功
25-09-02.15:15:49.664 [main            ] INFO  AiClientNode           - 注册Client---clientId:3103, clientName:质量检查和优化---成功
25-09-02.15:15:49.666 [main            ] INFO  AutoAgentTest          - 客户端构建:org.springframework.ai.chat.client.DefaultChatClient@2ef9e411
25-09-02.15:15:49.669 [main            ] ERROR AgentRepository        - Query ai agent client flow config failed, aiAgentId: 3
org.mybatis.spring.MyBatisSystemException: 
### Error querying database.  Cause: org.apache.ibatis.type.TypeException: Could not set parameters for mapping: ParameterMapping{property='agentId', mode=IN, javaType=class java.lang.Long, jdbcType=null, numericScale=null, resultMapId='null', jdbcTypeName='null', expression='null'}. Cause: org.apache.ibatis.type.TypeException: Error setting non null for parameter #1 with JdbcType null . Try setting a different JdbcType for this parameter or a different configuration property. Cause: java.lang.ClassCastException: class java.lang.String cannot be cast to class java.lang.Long (java.lang.String and java.lang.Long are in module java.base of loader 'bootstrap')
### The error may exist in file [D:\ACode\project\ai-agent\ai-agent-app\target\classes\mybatis\mapper\ai_agent_flow_config_mapper.xml]
### The error may involve com.flyxy.ai.infrastructure.dao.IAiAgentFlowConfigDao.queryByAgentId-Inline
### The error occurred while setting parameters
### SQL: SELECT id, agent_id, client_id, client_name, client_type, sequence, create_time         FROM ai_agent_flow_config         WHERE agent_id = ?         ORDER BY sequence
### Cause: org.apache.ibatis.type.TypeException: Could not set parameters for mapping: ParameterMapping{property='agentId', mode=IN, javaType=class java.lang.Long, jdbcType=null, numericScale=null, resultMapId='null', jdbcTypeName='null', expression='null'}. Cause: org.apache.ibatis.type.TypeException: Error setting non null for parameter #1 with JdbcType null . Try setting a different JdbcType for this parameter or a different configuration property. Cause: java.lang.ClassCastException: class java.lang.String cannot be cast to class java.lang.Long (java.lang.String and java.lang.Long are in module java.base of loader 'bootstrap')
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:99)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:347)
	at jdk.proxy2/jdk.proxy2.$Proxy78.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:194)
	at org.apache.ibatis.binding.MapperMethod.executeForMany(MapperMethod.java:147)
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:80)
	at org.apache.ibatis.binding.MapperProxy$PlainMethodInvoker.invoke(MapperProxy.java:141)
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:86)
	at jdk.proxy2/jdk.proxy2.$Proxy80.queryByAgentId(Unknown Source)
	at com.flyxy.ai.infrastructure.adapter.repository.AgentRepository.queryAiAgentClientFlowConfig(AgentRepository.java:471)
	at com.flyxy.ai.domain.agent.service.exec.auto.step.RootNode.multiThread(RootNode.java:32)
	at com.flyxy.ai.domain.agent.service.exec.auto.step.RootNode.multiThread(RootNode.java:20)
	at cn.bugstack.wrench.design.framework.tree.AbstractMultiThreadStrategyRouter.apply(AbstractMultiThreadStrategyRouter.java:29)
	at com.flyxy.ai.test.ai.AutoAgentTest.autoAgent(AutoAgentTest.java:66)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.springframework.test.context.junit4.statements.RunBeforeTestExecutionCallbacks.evaluate(RunBeforeTestExecutionCallbacks.java:76)
	at org.springframework.test.context.junit4.statements.RunAfterTestExecutionCallbacks.evaluate(RunAfterTestExecutionCallbacks.java:84)
	at org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:26)
	at org.springframework.test.context.junit4.statements.RunBeforeTestMethodCallbacks.evaluate(RunBeforeTestMethodCallbacks.java:75)
	at org.springframework.test.context.junit4.statements.RunAfterTestMethodCallbacks.evaluate(RunAfterTestMethodCallbacks.java:86)
	at org.springframework.test.context.junit4.statements.SpringRepeat.evaluate(SpringRepeat.java:84)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:252)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:97)
	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at org.springframework.test.context.junit4.statements.RunBeforeTestClassCallbacks.evaluate(RunBeforeTestClassCallbacks.java:61)
	at org.springframework.test.context.junit4.statements.RunAfterTestClassCallbacks.evaluate(RunAfterTestClassCallbacks.java:70)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.run(SpringJUnit4ClassRunner.java:191)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
	at com.intellij.junit4.JUnit4IdeaTestRunner.startRunnerWithArgs(JUnit4IdeaTestRunner.java:69)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater$1.execute(IdeaTestRunner.java:38)
	at com.intellij.rt.execution.junit.TestsRepeater.repeat(TestsRepeater.java:11)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:35)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:231)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:55)
Caused by: org.apache.ibatis.type.TypeException: Could not set parameters for mapping: ParameterMapping{property='agentId', mode=IN, javaType=class java.lang.Long, jdbcType=null, numericScale=null, resultMapId='null', jdbcTypeName='null', expression='null'}. Cause: org.apache.ibatis.type.TypeException: Error setting non null for parameter #1 with JdbcType null . Try setting a different JdbcType for this parameter or a different configuration property. Cause: java.lang.ClassCastException: class java.lang.String cannot be cast to class java.lang.Long (java.lang.String and java.lang.Long are in module java.base of loader 'bootstrap')
	at org.apache.ibatis.scripting.defaults.DefaultParameterHandler.setParameters(DefaultParameterHandler.java:92)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.parameterize(PreparedStatementHandler.java:97)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.parameterize(RoutingStatementHandler.java:65)
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:91)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:64)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:336)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:158)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:110)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:90)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142)
	at jdk.internal.reflect.GeneratedMethodAccessor27.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:333)
	... 46 common frames omitted
Caused by: org.apache.ibatis.type.TypeException: Error setting non null for parameter #1 with JdbcType null . Try setting a different JdbcType for this parameter or a different configuration property. Cause: java.lang.ClassCastException: class java.lang.String cannot be cast to class java.lang.Long (java.lang.String and java.lang.Long are in module java.base of loader 'bootstrap')
	at org.apache.ibatis.type.BaseTypeHandler.setParameter(BaseTypeHandler.java:76)
	at org.apache.ibatis.scripting.defaults.DefaultParameterHandler.setParameters(DefaultParameterHandler.java:90)
	... 61 common frames omitted
Caused by: java.lang.ClassCastException: class java.lang.String cannot be cast to class java.lang.Long (java.lang.String and java.lang.Long are in module java.base of loader 'bootstrap')
	at org.apache.ibatis.type.LongTypeHandler.setNonNullParameter(LongTypeHandler.java:26)
	at org.apache.ibatis.type.BaseTypeHandler.setParameter(BaseTypeHandler.java:74)
	... 62 common frames omitted
25-09-02.15:15:49.670 [main            ] INFO  RootNode               - === 动态多轮执行测试开始 ====
25-09-02.15:15:49.672 [main            ] INFO  RootNode               - 用户输入: 搜索小傅哥，技术项目列表。编写成一份文档，说明不同项目的学习目标，以及不同阶段的伙伴应该学习哪个项目。
25-09-02.15:15:49.672 [main            ] INFO  RootNode               - 最大执行步数: 3
25-09-02.15:15:49.672 [main            ] INFO  RootNode               - 会话ID: session-id-1756797349666
25-09-02.15:15:49.672 [main            ] INFO  Step1TaskAnalyzerClient - === 动态多轮执行第1步 Step1 ====
25-09-02.15:15:49.672 [main            ] INFO  Step1TaskAnalyzerClient - 
📊 阶段1: 任务状态分析
25-09-02.15:15:49.692 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MainHikariPool - Shutdown initiated...
25-09-02.15:15:49.703 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MainHikariPool - Shutdown completed.
25-09-02.15:18:33.823 [main            ] INFO  AutoAgentTest          - Starting AutoAgentTest using Java 17.0.12 with PID 18340 (started by 30562 in D:\ACode\project\ai-agent\ai-agent-app)
25-09-02.15:18:33.826 [main            ] INFO  AutoAgentTest          - The following 1 profile is active: "dev"
25-09-02.15:18:36.480 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_openai. Is empty: false
25-09-02.15:18:36.483 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_openai in schema: public
25-09-02.15:18:36.485 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-09-02.15:18:36.773 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-09-02.15:18:37.297 [main            ] INFO  AutoAgentTest          - Started AutoAgentTest in 4.022 seconds (process running for 5.326)
25-09-02.15:18:37.957 [main            ] INFO  RootNode               - AIClient装配链---RootNode---开始查询配置信息
25-09-02.15:18:37.959 [pool-2-thread-2 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_model) [3101, 3102, 3103]
25-09-02.15:18:37.959 [pool-2-thread-1 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_api) [3101, 3102, 3103]
25-09-02.15:18:37.959 [pool-2-thread-3 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_tool_mcp) [3101, 3102, 3103]
25-09-02.15:18:37.959 [pool-2-thread-4 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_system_prompt) [3101, 3102, 3103]
25-09-02.15:18:37.960 [pool-2-thread-5 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_advisor) [3101, 3102, 3103]
25-09-02.15:18:37.960 [pool-2-thread-6 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client) [3101, 3102, 3103]
25-09-02.15:18:37.988 [pool-2-thread-3 ] INFO  HikariDataSource       - MainHikariPool - Starting...
25-09-02.15:18:38.191 [pool-2-thread-3 ] INFO  HikariPool             - MainHikariPool - Added connection com.mysql.cj.jdbc.ConnectionImpl@6ab97f9
25-09-02.15:18:38.193 [pool-2-thread-3 ] INFO  HikariDataSource       - MainHikariPool - Start completed.
25-09-02.15:18:38.399 [main            ] INFO  RootNode               - AIClient装配链---RootNode---查询配置信息完毕
25-09-02.15:18:38.399 [main            ] INFO  AiClientApiNode        - AIClient装配链---AiClientApiNode---开始装配API
25-09-02.15:18:38.406 [main            ] INFO  AiClientMcpNode        - AIClient装配链---AiClientMcpNode---开始装配MCP
25-09-02.15:18:38.762 [HttpClient-10-Worker-0] INFO  McpAsyncClient         - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[completions=null, experimental={}, logging=null, prompts=PromptCapabilities[listChanged=false], resources=ResourceCapabilities[subscribe=false, listChanged=false], tools=ToolCapabilities[listChanged=false]], Info: Implementation[name=AIsearch, version=1.4.1] and Instructions null
25-09-02.15:19:38.846 [ForkJoinPool.commonPool-worker-2] ERROR HttpClientSseClientTransport - Error sending message: 500
25-09-02.15:19:38.850 [main            ] INFO  AiClientMcpNode        - sse方式注册mcp: baidu-search 成功
25-09-02.15:19:38.850 [main            ] INFO  AiClientModelNode      - AIClient装配链---AiClientModelNode---开始装配Model
25-09-02.15:19:38.850 [main            ] INFO  AiClientModelNode      - 注册模型-modelId:2001, modelName:gpt-4.1-mini-成功
25-09-02.15:19:38.850 [main            ] INFO  AiClientAdvisorNode    - AIClient装配链---AiClientAdvisorNode---开始装配顾问
25-09-02.15:19:38.862 [main            ] INFO  AiClientAdvisorNode    - 注册顾问---advisorId:4001, advisorName:记忆---成功
25-09-02.15:19:38.862 [main            ] INFO  AiClientNode           - AIClient装配链---AiClientNode---开始装配Client
25-09-02.15:22:06.826 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MainHikariPool - Shutdown initiated...
25-09-02.15:22:06.843 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MainHikariPool - Shutdown completed.
25-09-02.15:26:01.820 [main            ] INFO  AutoAgentTest          - Starting AutoAgentTest using Java 17.0.12 with PID 6656 (started by 30562 in D:\ACode\project\ai-agent\ai-agent-app)
25-09-02.15:26:01.822 [main            ] INFO  AutoAgentTest          - The following 1 profile is active: "dev"
25-09-02.15:26:04.340 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_openai. Is empty: false
25-09-02.15:26:04.345 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_openai in schema: public
25-09-02.15:26:04.345 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-09-02.15:26:04.626 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-09-02.15:26:05.121 [main            ] INFO  AutoAgentTest          - Started AutoAgentTest in 3.858 seconds (process running for 5.074)
25-09-02.15:26:05.798 [main            ] INFO  RootNode               - AIClient装配链---RootNode---开始查询配置信息
25-09-02.15:26:05.800 [pool-2-thread-2 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_model) [3101, 3102, 3103]
25-09-02.15:26:05.800 [pool-2-thread-1 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_api) [3101, 3102, 3103]
25-09-02.15:26:05.800 [pool-2-thread-3 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_tool_mcp) [3101, 3102, 3103]
25-09-02.15:26:05.802 [pool-2-thread-4 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_system_prompt) [3101, 3102, 3103]
25-09-02.15:26:05.802 [pool-2-thread-5 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_advisor) [3101, 3102, 3103]
25-09-02.15:26:05.802 [pool-2-thread-6 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client) [3101, 3102, 3103]
25-09-02.15:26:05.819 [pool-2-thread-4 ] INFO  HikariDataSource       - MainHikariPool - Starting...
25-09-02.15:26:06.030 [pool-2-thread-4 ] INFO  HikariPool             - MainHikariPool - Added connection com.mysql.cj.jdbc.ConnectionImpl@5580bfab
25-09-02.15:26:06.032 [pool-2-thread-4 ] INFO  HikariDataSource       - MainHikariPool - Start completed.
25-09-02.15:26:06.231 [main            ] INFO  RootNode               - AIClient装配链---RootNode---查询配置信息完毕
25-09-02.15:26:06.231 [main            ] INFO  AiClientApiNode        - AIClient装配链---AiClientApiNode---开始装配API
25-09-02.15:26:06.238 [main            ] INFO  AiClientMcpNode        - AIClient装配链---AiClientMcpNode---开始装配MCP
25-09-02.15:26:06.659 [HttpClient-10-Worker-0] INFO  McpAsyncClient         - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[completions=null, experimental={}, logging=null, prompts=PromptCapabilities[listChanged=false], resources=ResourceCapabilities[subscribe=false, listChanged=false], tools=ToolCapabilities[listChanged=false]], Info: Implementation[name=AIsearch, version=1.4.1] and Instructions null
25-09-02.15:26:06.724 [main            ] INFO  AiClientMcpNode        - sse方式注册mcp: baidu-search 成功
25-09-02.15:26:06.724 [main            ] INFO  AiClientModelNode      - AIClient装配链---AiClientModelNode---开始装配Model
25-09-02.15:26:06.726 [main            ] INFO  AiClientModelNode      - 注册模型-modelId:2001, modelName:gpt-4.1-mini-成功
25-09-02.15:26:06.726 [main            ] INFO  AiClientAdvisorNode    - AIClient装配链---AiClientAdvisorNode---开始装配顾问
25-09-02.15:26:06.737 [main            ] INFO  AiClientAdvisorNode    - 注册顾问---advisorId:4001, advisorName:记忆---成功
25-09-02.15:26:06.737 [main            ] INFO  AiClientNode           - AIClient装配链---AiClientNode---开始装配Client
25-09-02.15:26:06.873 [main            ] INFO  AiClientNode           - 注册Client---clientId:3101, clientName:任务分析和状态判断---成功
25-09-02.15:26:06.996 [main            ] INFO  AiClientNode           - 注册Client---clientId:3102, clientName:具体任务执行---成功
25-09-02.15:26:07.124 [main            ] INFO  AiClientNode           - 注册Client---clientId:3103, clientName:质量检查和优化---成功
25-09-02.15:26:07.126 [main            ] INFO  AutoAgentTest          - 客户端构建:org.springframework.ai.chat.client.DefaultChatClient@41c1ee2d
25-09-02.15:26:07.133 [main            ] INFO  RootNode               - === 动态多轮执行测试开始 ====
25-09-02.15:26:07.133 [main            ] INFO  RootNode               - 用户输入: 搜索小傅哥，技术项目列表。编写成一份文档，说明不同项目的学习目标，以及不同阶段的伙伴应该学习哪个项目。
25-09-02.15:26:07.133 [main            ] INFO  RootNode               - 最大执行步数: 3
25-09-02.15:26:07.133 [main            ] INFO  RootNode               - 会话ID: session-id-1756797967126
25-09-02.15:26:07.133 [main            ] INFO  Step1TaskAnalyzerClient - === 动态多轮执行第1步 Step1 ====
25-09-02.15:26:07.133 [main            ] INFO  Step1TaskAnalyzerClient - 
📊 阶段1: 任务状态分析
25-09-02.15:26:07.713 [main            ] WARN  RetryUtils             - Retry error. Retry count:1
org.springframework.ai.retry.NonTransientAiException: 401 - {"error":{"message":"该令牌状态不可用 (request id: 2025090215260756227311817898317)","type":"one_api_error"}}
	at org.springframework.ai.retry.RetryUtils$1.handleError(RetryUtils.java:63)
	at org.springframework.web.client.ResponseErrorHandler.handleError(ResponseErrorHandler.java:58)
	at org.springframework.web.client.StatusHandler.lambda$fromErrorHandler$1(StatusHandler.java:71)
	at org.springframework.web.client.StatusHandler.handle(StatusHandler.java:146)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.applyStatusHandlers(DefaultRestClient.java:826)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.lambda$readBody$4(DefaultRestClient.java:815)
	at org.springframework.web.client.DefaultRestClient.readWithMessageConverters(DefaultRestClient.java:215)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.readBody(DefaultRestClient.java:814)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.lambda$toEntityInternal$2(DefaultRestClient.java:770)
	at org.springframework.web.client.DefaultRestClient$DefaultRequestBodyUriSpec.exchangeInternal(DefaultRestClient.java:574)
	at org.springframework.web.client.DefaultRestClient$DefaultRequestBodyUriSpec.exchange(DefaultRestClient.java:535)
	at org.springframework.web.client.RestClient$RequestHeadersSpec.exchange(RestClient.java:677)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.executeAndExtract(DefaultRestClient.java:809)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.toEntityInternal(DefaultRestClient.java:769)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.toEntity(DefaultRestClient.java:758)
	at org.springframework.ai.openai.api.OpenAiApi.chatCompletionEntity(OpenAiApi.java:187)
	at org.springframework.ai.openai.OpenAiChatModel.lambda$internalCall$1(OpenAiChatModel.java:199)
	at org.springframework.retry.support.RetryTemplate.doExecute(RetryTemplate.java:357)
	at org.springframework.retry.support.RetryTemplate.execute(RetryTemplate.java:230)
	at org.springframework.ai.openai.OpenAiChatModel.lambda$internalCall$3(OpenAiChatModel.java:199)
	at io.micrometer.observation.Observation.observe(Observation.java:564)
	at org.springframework.ai.openai.OpenAiChatModel.internalCall(OpenAiChatModel.java:196)
	at org.springframework.ai.openai.OpenAiChatModel.call(OpenAiChatModel.java:181)
	at org.springframework.ai.chat.client.advisor.ChatModelCallAdvisor.adviseCall(ChatModelCallAdvisor.java:54)
	at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextCall$1(DefaultAroundAdvisorChain.java:110)
	at io.micrometer.observation.Observation.observe(Observation.java:564)
	at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextCall(DefaultAroundAdvisorChain.java:110)
	at org.springframework.ai.chat.client.advisor.api.BaseAdvisor.adviseCall(BaseAdvisor.java:52)
	at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextCall$1(DefaultAroundAdvisorChain.java:110)
	at io.micrometer.observation.Observation.observe(Observation.java:564)
	at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextCall(DefaultAroundAdvisorChain.java:110)
	at org.springframework.ai.chat.client.DefaultChatClient$DefaultCallResponseSpec.lambda$doGetObservableChatClientResponse$1(DefaultChatClient.java:469)
	at io.micrometer.observation.Observation.observe(Observation.java:564)
	at org.springframework.ai.chat.client.DefaultChatClient$DefaultCallResponseSpec.doGetObservableChatClientResponse(DefaultChatClient.java:467)
	at org.springframework.ai.chat.client.DefaultChatClient$DefaultCallResponseSpec.doGetObservableChatClientResponse(DefaultChatClient.java:446)
	at org.springframework.ai.chat.client.DefaultChatClient$DefaultCallResponseSpec.content(DefaultChatClient.java:441)
	at com.flyxy.ai.domain.agent.service.exec.auto.step.Step1TaskAnalyzerClient.doApply(Step1TaskAnalyzerClient.java:64)
	at com.flyxy.ai.domain.agent.service.exec.auto.step.Step1TaskAnalyzerClient.doApply(Step1TaskAnalyzerClient.java:23)
	at cn.bugstack.wrench.design.framework.tree.AbstractMultiThreadStrategyRouter.apply(AbstractMultiThreadStrategyRouter.java:31)
	at cn.bugstack.wrench.design.framework.tree.AbstractMultiThreadStrategyRouter.router(AbstractMultiThreadStrategyRouter.java:22)
	at com.flyxy.ai.domain.agent.service.exec.auto.step.RootNode.doApply(RootNode.java:48)
	at com.flyxy.ai.domain.agent.service.exec.auto.step.RootNode.doApply(RootNode.java:20)
	at cn.bugstack.wrench.design.framework.tree.AbstractMultiThreadStrategyRouter.apply(AbstractMultiThreadStrategyRouter.java:31)
	at com.flyxy.ai.test.ai.AutoAgentTest.autoAgent(AutoAgentTest.java:66)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.springframework.test.context.junit4.statements.RunBeforeTestExecutionCallbacks.evaluate(RunBeforeTestExecutionCallbacks.java:76)
	at org.springframework.test.context.junit4.statements.RunAfterTestExecutionCallbacks.evaluate(RunAfterTestExecutionCallbacks.java:84)
	at org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:26)
	at org.springframework.test.context.junit4.statements.RunBeforeTestMethodCallbacks.evaluate(RunBeforeTestMethodCallbacks.java:75)
	at org.springframework.test.context.junit4.statements.RunAfterTestMethodCallbacks.evaluate(RunAfterTestMethodCallbacks.java:86)
	at org.springframework.test.context.junit4.statements.SpringRepeat.evaluate(SpringRepeat.java:84)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:252)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:97)
	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at org.springframework.test.context.junit4.statements.RunBeforeTestClassCallbacks.evaluate(RunBeforeTestClassCallbacks.java:61)
	at org.springframework.test.context.junit4.statements.RunAfterTestClassCallbacks.evaluate(RunAfterTestClassCallbacks.java:70)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.run(SpringJUnit4ClassRunner.java:191)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
	at com.intellij.junit4.JUnit4IdeaTestRunner.startRunnerWithArgs(JUnit4IdeaTestRunner.java:69)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater$1.execute(IdeaTestRunner.java:38)
	at com.intellij.rt.execution.junit.TestsRepeater.repeat(TestsRepeater.java:11)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:35)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:231)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:55)
25-09-02.15:26:07.733 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MainHikariPool - Shutdown initiated...
25-09-02.15:26:07.739 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MainHikariPool - Shutdown completed.
25-09-02.15:27:51.593 [main            ] INFO  AutoAgentTest          - Starting AutoAgentTest using Java 17.0.12 with PID 30736 (started by 30562 in D:\ACode\project\ai-agent\ai-agent-app)
25-09-02.15:27:51.595 [main            ] INFO  AutoAgentTest          - The following 1 profile is active: "dev"
25-09-02.15:27:54.061 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_openai. Is empty: false
25-09-02.15:27:54.066 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_openai in schema: public
25-09-02.15:27:54.066 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-09-02.15:27:54.391 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-09-02.15:27:54.879 [main            ] INFO  AutoAgentTest          - Started AutoAgentTest in 3.803 seconds (process running for 4.993)
25-09-02.15:27:55.582 [main            ] INFO  RootNode               - AIClient装配链---RootNode---开始查询配置信息
25-09-02.15:27:55.583 [pool-2-thread-1 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_api) [3101, 3102, 3103]
25-09-02.15:27:55.583 [pool-2-thread-2 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_model) [3101, 3102, 3103]
25-09-02.15:27:55.583 [pool-2-thread-3 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_tool_mcp) [3101, 3102, 3103]
25-09-02.15:27:55.585 [pool-2-thread-4 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_system_prompt) [3101, 3102, 3103]
25-09-02.15:27:55.585 [pool-2-thread-5 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_advisor) [3101, 3102, 3103]
25-09-02.15:27:55.585 [pool-2-thread-6 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client) [3101, 3102, 3103]
25-09-02.15:27:55.605 [pool-2-thread-2 ] INFO  HikariDataSource       - MainHikariPool - Starting...
25-09-02.15:27:55.813 [pool-2-thread-2 ] INFO  HikariPool             - MainHikariPool - Added connection com.mysql.cj.jdbc.ConnectionImpl@3ab21614
25-09-02.15:27:55.815 [pool-2-thread-2 ] INFO  HikariDataSource       - MainHikariPool - Start completed.
25-09-02.15:27:56.014 [main            ] INFO  RootNode               - AIClient装配链---RootNode---查询配置信息完毕
25-09-02.15:27:56.014 [main            ] INFO  AiClientApiNode        - AIClient装配链---AiClientApiNode---开始装配API
25-09-02.15:27:56.021 [main            ] INFO  AiClientMcpNode        - AIClient装配链---AiClientMcpNode---开始装配MCP
25-09-02.15:27:56.614 [HttpClient-10-Worker-0] INFO  McpAsyncClient         - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[completions=null, experimental={}, logging=null, prompts=PromptCapabilities[listChanged=false], resources=ResourceCapabilities[subscribe=false, listChanged=false], tools=ToolCapabilities[listChanged=false]], Info: Implementation[name=AIsearch, version=1.4.1] and Instructions null
25-09-02.15:27:56.754 [main            ] INFO  AiClientMcpNode        - sse方式注册mcp: baidu-search 成功
25-09-02.15:27:56.754 [main            ] INFO  AiClientModelNode      - AIClient装配链---AiClientModelNode---开始装配Model
25-09-02.15:27:56.756 [main            ] INFO  AiClientModelNode      - 注册模型-modelId:2001, modelName:gpt-4.1-mini-成功
25-09-02.15:27:56.756 [main            ] INFO  AiClientAdvisorNode    - AIClient装配链---AiClientAdvisorNode---开始装配顾问
25-09-02.15:27:56.767 [main            ] INFO  AiClientAdvisorNode    - 注册顾问---advisorId:4001, advisorName:记忆---成功
25-09-02.15:27:56.767 [main            ] INFO  AiClientNode           - AIClient装配链---AiClientNode---开始装配Client
25-09-02.15:27:56.926 [main            ] INFO  AiClientNode           - 注册Client---clientId:3101, clientName:任务分析和状态判断---成功
25-09-02.15:27:57.062 [main            ] INFO  AiClientNode           - 注册Client---clientId:3102, clientName:具体任务执行---成功
25-09-02.15:27:57.183 [main            ] INFO  AiClientNode           - 注册Client---clientId:3103, clientName:质量检查和优化---成功
25-09-02.15:27:57.184 [main            ] INFO  AutoAgentTest          - 客户端构建:org.springframework.ai.chat.client.DefaultChatClient@61c3a93f
25-09-02.15:27:57.190 [main            ] INFO  RootNode               - === 动态多轮执行测试开始 ====
25-09-02.15:27:57.190 [main            ] INFO  RootNode               - 用户输入: 搜索小傅哥，技术项目列表。编写成一份文档，说明不同项目的学习目标，以及不同阶段的伙伴应该学习哪个项目。
25-09-02.15:27:57.190 [main            ] INFO  RootNode               - 最大执行步数: 3
25-09-02.15:27:57.191 [main            ] INFO  RootNode               - 会话ID: session-id-1756798077185
25-09-02.15:27:57.191 [main            ] INFO  Step1TaskAnalyzerClient - === 动态多轮执行第1步 Step1 ====
25-09-02.15:27:57.191 [main            ] INFO  Step1TaskAnalyzerClient - 
📊 阶段1: 任务状态分析
25-09-02.15:27:57.741 [main            ] WARN  RetryUtils             - Retry error. Retry count:1
org.springframework.ai.retry.NonTransientAiException: 404 - 
	at org.springframework.ai.retry.RetryUtils$1.handleError(RetryUtils.java:63)
	at org.springframework.web.client.ResponseErrorHandler.handleError(ResponseErrorHandler.java:58)
	at org.springframework.web.client.StatusHandler.lambda$fromErrorHandler$1(StatusHandler.java:71)
	at org.springframework.web.client.StatusHandler.handle(StatusHandler.java:146)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.applyStatusHandlers(DefaultRestClient.java:826)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.lambda$readBody$4(DefaultRestClient.java:815)
	at org.springframework.web.client.DefaultRestClient.readWithMessageConverters(DefaultRestClient.java:215)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.readBody(DefaultRestClient.java:814)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.lambda$toEntityInternal$2(DefaultRestClient.java:770)
	at org.springframework.web.client.DefaultRestClient$DefaultRequestBodyUriSpec.exchangeInternal(DefaultRestClient.java:574)
	at org.springframework.web.client.DefaultRestClient$DefaultRequestBodyUriSpec.exchange(DefaultRestClient.java:535)
	at org.springframework.web.client.RestClient$RequestHeadersSpec.exchange(RestClient.java:677)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.executeAndExtract(DefaultRestClient.java:809)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.toEntityInternal(DefaultRestClient.java:769)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.toEntity(DefaultRestClient.java:758)
	at org.springframework.ai.openai.api.OpenAiApi.chatCompletionEntity(OpenAiApi.java:187)
	at org.springframework.ai.openai.OpenAiChatModel.lambda$internalCall$1(OpenAiChatModel.java:199)
	at org.springframework.retry.support.RetryTemplate.doExecute(RetryTemplate.java:357)
	at org.springframework.retry.support.RetryTemplate.execute(RetryTemplate.java:230)
	at org.springframework.ai.openai.OpenAiChatModel.lambda$internalCall$3(OpenAiChatModel.java:199)
	at io.micrometer.observation.Observation.observe(Observation.java:564)
	at org.springframework.ai.openai.OpenAiChatModel.internalCall(OpenAiChatModel.java:196)
	at org.springframework.ai.openai.OpenAiChatModel.call(OpenAiChatModel.java:181)
	at org.springframework.ai.chat.client.advisor.ChatModelCallAdvisor.adviseCall(ChatModelCallAdvisor.java:54)
	at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextCall$1(DefaultAroundAdvisorChain.java:110)
	at io.micrometer.observation.Observation.observe(Observation.java:564)
	at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextCall(DefaultAroundAdvisorChain.java:110)
	at org.springframework.ai.chat.client.advisor.api.BaseAdvisor.adviseCall(BaseAdvisor.java:52)
	at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextCall$1(DefaultAroundAdvisorChain.java:110)
	at io.micrometer.observation.Observation.observe(Observation.java:564)
	at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextCall(DefaultAroundAdvisorChain.java:110)
	at org.springframework.ai.chat.client.DefaultChatClient$DefaultCallResponseSpec.lambda$doGetObservableChatClientResponse$1(DefaultChatClient.java:469)
	at io.micrometer.observation.Observation.observe(Observation.java:564)
	at org.springframework.ai.chat.client.DefaultChatClient$DefaultCallResponseSpec.doGetObservableChatClientResponse(DefaultChatClient.java:467)
	at org.springframework.ai.chat.client.DefaultChatClient$DefaultCallResponseSpec.doGetObservableChatClientResponse(DefaultChatClient.java:446)
	at org.springframework.ai.chat.client.DefaultChatClient$DefaultCallResponseSpec.content(DefaultChatClient.java:441)
	at com.flyxy.ai.domain.agent.service.exec.auto.step.Step1TaskAnalyzerClient.doApply(Step1TaskAnalyzerClient.java:64)
	at com.flyxy.ai.domain.agent.service.exec.auto.step.Step1TaskAnalyzerClient.doApply(Step1TaskAnalyzerClient.java:23)
	at cn.bugstack.wrench.design.framework.tree.AbstractMultiThreadStrategyRouter.apply(AbstractMultiThreadStrategyRouter.java:31)
	at cn.bugstack.wrench.design.framework.tree.AbstractMultiThreadStrategyRouter.router(AbstractMultiThreadStrategyRouter.java:22)
	at com.flyxy.ai.domain.agent.service.exec.auto.step.RootNode.doApply(RootNode.java:48)
	at com.flyxy.ai.domain.agent.service.exec.auto.step.RootNode.doApply(RootNode.java:20)
	at cn.bugstack.wrench.design.framework.tree.AbstractMultiThreadStrategyRouter.apply(AbstractMultiThreadStrategyRouter.java:31)
	at com.flyxy.ai.test.ai.AutoAgentTest.autoAgent(AutoAgentTest.java:66)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.springframework.test.context.junit4.statements.RunBeforeTestExecutionCallbacks.evaluate(RunBeforeTestExecutionCallbacks.java:76)
	at org.springframework.test.context.junit4.statements.RunAfterTestExecutionCallbacks.evaluate(RunAfterTestExecutionCallbacks.java:84)
	at org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:26)
	at org.springframework.test.context.junit4.statements.RunBeforeTestMethodCallbacks.evaluate(RunBeforeTestMethodCallbacks.java:75)
	at org.springframework.test.context.junit4.statements.RunAfterTestMethodCallbacks.evaluate(RunAfterTestMethodCallbacks.java:86)
	at org.springframework.test.context.junit4.statements.SpringRepeat.evaluate(SpringRepeat.java:84)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:252)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:97)
	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at org.springframework.test.context.junit4.statements.RunBeforeTestClassCallbacks.evaluate(RunBeforeTestClassCallbacks.java:61)
	at org.springframework.test.context.junit4.statements.RunAfterTestClassCallbacks.evaluate(RunAfterTestClassCallbacks.java:70)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.run(SpringJUnit4ClassRunner.java:191)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
	at com.intellij.junit4.JUnit4IdeaTestRunner.startRunnerWithArgs(JUnit4IdeaTestRunner.java:69)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater$1.execute(IdeaTestRunner.java:38)
	at com.intellij.rt.execution.junit.TestsRepeater.repeat(TestsRepeater.java:11)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:35)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:231)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:55)
25-09-02.15:27:57.762 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MainHikariPool - Shutdown initiated...
25-09-02.15:27:57.774 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MainHikariPool - Shutdown completed.
25-09-02.15:28:59.041 [main            ] INFO  AutoAgentTest          - Starting AutoAgentTest using Java 17.0.12 with PID 21372 (started by 30562 in D:\ACode\project\ai-agent\ai-agent-app)
25-09-02.15:28:59.044 [main            ] INFO  AutoAgentTest          - The following 1 profile is active: "dev"
25-09-02.15:29:01.547 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_openai. Is empty: false
25-09-02.15:29:01.553 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_openai in schema: public
25-09-02.15:29:01.553 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-09-02.15:29:01.816 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-09-02.15:29:02.343 [main            ] INFO  AutoAgentTest          - Started AutoAgentTest in 3.844 seconds (process running for 5.1)
25-09-02.15:29:03.011 [main            ] INFO  RootNode               - AIClient装配链---RootNode---开始查询配置信息
25-09-02.15:29:03.012 [pool-2-thread-2 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_model) [3101, 3102, 3103]
25-09-02.15:29:03.012 [pool-2-thread-1 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_api) [3101, 3102, 3103]
25-09-02.15:29:03.012 [pool-2-thread-3 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_tool_mcp) [3101, 3102, 3103]
25-09-02.15:29:03.012 [pool-2-thread-4 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_system_prompt) [3101, 3102, 3103]
25-09-02.15:29:03.014 [pool-2-thread-5 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_advisor) [3101, 3102, 3103]
25-09-02.15:29:03.014 [pool-2-thread-6 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client) [3101, 3102, 3103]
25-09-02.15:29:03.043 [pool-2-thread-5 ] INFO  HikariDataSource       - MainHikariPool - Starting...
25-09-02.15:29:03.257 [pool-2-thread-5 ] INFO  HikariPool             - MainHikariPool - Added connection com.mysql.cj.jdbc.ConnectionImpl@6f7c5145
25-09-02.15:29:03.259 [pool-2-thread-5 ] INFO  HikariDataSource       - MainHikariPool - Start completed.
25-09-02.15:29:03.461 [main            ] INFO  RootNode               - AIClient装配链---RootNode---查询配置信息完毕
25-09-02.15:29:03.461 [main            ] INFO  AiClientApiNode        - AIClient装配链---AiClientApiNode---开始装配API
25-09-02.15:29:03.468 [main            ] INFO  AiClientMcpNode        - AIClient装配链---AiClientMcpNode---开始装配MCP
25-09-02.15:29:03.779 [HttpClient-10-Worker-1] INFO  McpAsyncClient         - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[completions=null, experimental={}, logging=null, prompts=PromptCapabilities[listChanged=false], resources=ResourceCapabilities[subscribe=false, listChanged=false], tools=ToolCapabilities[listChanged=false]], Info: Implementation[name=AIsearch, version=1.4.1] and Instructions null
25-09-02.15:29:03.856 [main            ] INFO  AiClientMcpNode        - sse方式注册mcp: baidu-search 成功
25-09-02.15:29:03.856 [main            ] INFO  AiClientModelNode      - AIClient装配链---AiClientModelNode---开始装配Model
25-09-02.15:29:03.856 [main            ] INFO  AiClientModelNode      - 注册模型-modelId:2001, modelName:qwen-plus-成功
25-09-02.15:29:03.856 [main            ] INFO  AiClientAdvisorNode    - AIClient装配链---AiClientAdvisorNode---开始装配顾问
25-09-02.15:29:03.869 [main            ] INFO  AiClientAdvisorNode    - 注册顾问---advisorId:4001, advisorName:记忆---成功
25-09-02.15:29:03.869 [main            ] INFO  AiClientNode           - AIClient装配链---AiClientNode---开始装配Client
25-09-02.15:29:03.967 [main            ] INFO  AiClientNode           - 注册Client---clientId:3101, clientName:任务分析和状态判断---成功
25-09-02.15:29:04.041 [main            ] INFO  AiClientNode           - 注册Client---clientId:3102, clientName:具体任务执行---成功
25-09-02.15:29:04.102 [main            ] INFO  AiClientNode           - 注册Client---clientId:3103, clientName:质量检查和优化---成功
25-09-02.15:29:04.104 [main            ] INFO  AutoAgentTest          - 客户端构建:org.springframework.ai.chat.client.DefaultChatClient@5ac385bb
25-09-02.15:29:04.110 [main            ] INFO  RootNode               - === 动态多轮执行测试开始 ====
25-09-02.15:29:04.110 [main            ] INFO  RootNode               - 用户输入: 搜索小傅哥，技术项目列表。编写成一份文档，说明不同项目的学习目标，以及不同阶段的伙伴应该学习哪个项目。
25-09-02.15:29:04.110 [main            ] INFO  RootNode               - 最大执行步数: 3
25-09-02.15:29:04.110 [main            ] INFO  RootNode               - 会话ID: session-id-1756798144105
25-09-02.15:29:04.110 [main            ] INFO  Step1TaskAnalyzerClient - === 动态多轮执行第1步 Step1 ====
25-09-02.15:29:04.110 [main            ] INFO  Step1TaskAnalyzerClient - 
📊 阶段1: 任务状态分析
25-09-02.15:29:04.425 [main            ] WARN  RetryUtils             - Retry error. Retry count:1
org.springframework.ai.retry.NonTransientAiException: 404 - 
	at org.springframework.ai.retry.RetryUtils$1.handleError(RetryUtils.java:63)
	at org.springframework.web.client.ResponseErrorHandler.handleError(ResponseErrorHandler.java:58)
	at org.springframework.web.client.StatusHandler.lambda$fromErrorHandler$1(StatusHandler.java:71)
	at org.springframework.web.client.StatusHandler.handle(StatusHandler.java:146)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.applyStatusHandlers(DefaultRestClient.java:826)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.lambda$readBody$4(DefaultRestClient.java:815)
	at org.springframework.web.client.DefaultRestClient.readWithMessageConverters(DefaultRestClient.java:215)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.readBody(DefaultRestClient.java:814)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.lambda$toEntityInternal$2(DefaultRestClient.java:770)
	at org.springframework.web.client.DefaultRestClient$DefaultRequestBodyUriSpec.exchangeInternal(DefaultRestClient.java:574)
	at org.springframework.web.client.DefaultRestClient$DefaultRequestBodyUriSpec.exchange(DefaultRestClient.java:535)
	at org.springframework.web.client.RestClient$RequestHeadersSpec.exchange(RestClient.java:677)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.executeAndExtract(DefaultRestClient.java:809)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.toEntityInternal(DefaultRestClient.java:769)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.toEntity(DefaultRestClient.java:758)
	at org.springframework.ai.openai.api.OpenAiApi.chatCompletionEntity(OpenAiApi.java:187)
	at org.springframework.ai.openai.OpenAiChatModel.lambda$internalCall$1(OpenAiChatModel.java:199)
	at org.springframework.retry.support.RetryTemplate.doExecute(RetryTemplate.java:357)
	at org.springframework.retry.support.RetryTemplate.execute(RetryTemplate.java:230)
	at org.springframework.ai.openai.OpenAiChatModel.lambda$internalCall$3(OpenAiChatModel.java:199)
	at io.micrometer.observation.Observation.observe(Observation.java:564)
	at org.springframework.ai.openai.OpenAiChatModel.internalCall(OpenAiChatModel.java:196)
	at org.springframework.ai.openai.OpenAiChatModel.call(OpenAiChatModel.java:181)
	at org.springframework.ai.chat.client.advisor.ChatModelCallAdvisor.adviseCall(ChatModelCallAdvisor.java:54)
	at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextCall$1(DefaultAroundAdvisorChain.java:110)
	at io.micrometer.observation.Observation.observe(Observation.java:564)
	at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextCall(DefaultAroundAdvisorChain.java:110)
	at org.springframework.ai.chat.client.advisor.api.BaseAdvisor.adviseCall(BaseAdvisor.java:52)
	at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextCall$1(DefaultAroundAdvisorChain.java:110)
	at io.micrometer.observation.Observation.observe(Observation.java:564)
	at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextCall(DefaultAroundAdvisorChain.java:110)
	at org.springframework.ai.chat.client.DefaultChatClient$DefaultCallResponseSpec.lambda$doGetObservableChatClientResponse$1(DefaultChatClient.java:469)
	at io.micrometer.observation.Observation.observe(Observation.java:564)
	at org.springframework.ai.chat.client.DefaultChatClient$DefaultCallResponseSpec.doGetObservableChatClientResponse(DefaultChatClient.java:467)
	at org.springframework.ai.chat.client.DefaultChatClient$DefaultCallResponseSpec.doGetObservableChatClientResponse(DefaultChatClient.java:446)
	at org.springframework.ai.chat.client.DefaultChatClient$DefaultCallResponseSpec.content(DefaultChatClient.java:441)
	at com.flyxy.ai.domain.agent.service.exec.auto.step.Step1TaskAnalyzerClient.doApply(Step1TaskAnalyzerClient.java:64)
	at com.flyxy.ai.domain.agent.service.exec.auto.step.Step1TaskAnalyzerClient.doApply(Step1TaskAnalyzerClient.java:23)
	at cn.bugstack.wrench.design.framework.tree.AbstractMultiThreadStrategyRouter.apply(AbstractMultiThreadStrategyRouter.java:31)
	at cn.bugstack.wrench.design.framework.tree.AbstractMultiThreadStrategyRouter.router(AbstractMultiThreadStrategyRouter.java:22)
	at com.flyxy.ai.domain.agent.service.exec.auto.step.RootNode.doApply(RootNode.java:48)
	at com.flyxy.ai.domain.agent.service.exec.auto.step.RootNode.doApply(RootNode.java:20)
	at cn.bugstack.wrench.design.framework.tree.AbstractMultiThreadStrategyRouter.apply(AbstractMultiThreadStrategyRouter.java:31)
	at com.flyxy.ai.test.ai.AutoAgentTest.autoAgent(AutoAgentTest.java:66)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.springframework.test.context.junit4.statements.RunBeforeTestExecutionCallbacks.evaluate(RunBeforeTestExecutionCallbacks.java:76)
	at org.springframework.test.context.junit4.statements.RunAfterTestExecutionCallbacks.evaluate(RunAfterTestExecutionCallbacks.java:84)
	at org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:26)
	at org.springframework.test.context.junit4.statements.RunBeforeTestMethodCallbacks.evaluate(RunBeforeTestMethodCallbacks.java:75)
	at org.springframework.test.context.junit4.statements.RunAfterTestMethodCallbacks.evaluate(RunAfterTestMethodCallbacks.java:86)
	at org.springframework.test.context.junit4.statements.SpringRepeat.evaluate(SpringRepeat.java:84)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:252)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:97)
	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at org.springframework.test.context.junit4.statements.RunBeforeTestClassCallbacks.evaluate(RunBeforeTestClassCallbacks.java:61)
	at org.springframework.test.context.junit4.statements.RunAfterTestClassCallbacks.evaluate(RunAfterTestClassCallbacks.java:70)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.run(SpringJUnit4ClassRunner.java:191)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
	at com.intellij.junit4.JUnit4IdeaTestRunner.startRunnerWithArgs(JUnit4IdeaTestRunner.java:69)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater$1.execute(IdeaTestRunner.java:38)
	at com.intellij.rt.execution.junit.TestsRepeater.repeat(TestsRepeater.java:11)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:35)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:231)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:55)
25-09-02.15:29:04.447 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MainHikariPool - Shutdown initiated...
25-09-02.15:29:04.459 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MainHikariPool - Shutdown completed.
25-09-02.15:34:00.535 [main            ] INFO  AiModelApiTest         - Starting AiModelApiTest using Java 17.0.12 with PID 18236 (started by 30562 in D:\ACode\project\ai-agent\ai-agent-app)
25-09-02.15:34:00.536 [main            ] INFO  AiModelApiTest         - The following 1 profile is active: "dev"
25-09-02.15:34:03.271 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_openai. Is empty: false
25-09-02.15:34:03.275 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_openai in schema: public
25-09-02.15:34:03.277 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-09-02.15:34:03.562 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-09-02.15:34:04.128 [main            ] INFO  AiModelApiTest         - Started AiModelApiTest in 4.105 seconds (process running for 5.438)
25-09-02.15:34:06.994 [main            ] INFO  AiModelApiTest         - 测试结果: {"metadata":{"empty":false,"id":"chatcmpl-d386533b-abd5-9059-b7d7-d3d303bd61fd","model":"qwen-plus","rateLimit":{},"usage":{"promptTokens":15,"completionTokens":66,"totalTokens":81,"nativeUsage":{"completion_tokens":66,"prompt_tokens":15,"prompt_tokens_details":{"cached_tokens":0},"total_tokens":81}}},"result":{"metadata":{"contentFilters":[],"empty":true,"finishReason":"STOP"},"output":{"media":[],"messageType":"ASSISTANT","metadata":{"role":"ASSISTANT","messageType":"ASSISTANT","refusal":"","finishReason":"STOP","annotations":[],"index":0,"id":"chatcmpl-d386533b-abd5-9059-b7d7-d3d303bd61fd"},"text":"我是通义千问，阿里巴巴集团旗下的通义实验室自主研发的超大规模语言模型。我可以帮助你回答问题、创作文字，比如写故事、写公文、写邮件、写剧本、逻辑推理、编程等等，还能表达观点，玩游戏等。如果你有任何问题或需要帮助，欢迎随时告诉我！"{"$ref":"$.metadata.rateLimit.usage.nativeUsage.prompt_tokens_details.result.metadata.contentFilters.output.metadata.annotations"}}},"results":[{"$ref":"$.metadata.rateLimit.usage.nativeUsage.prompt_tokens_details.result"}]}
25-09-02.15:36:38.753 [main            ] INFO  AiModelApiTest         - Starting AiModelApiTest using Java 17.0.12 with PID 6900 (started by 30562 in D:\ACode\project\ai-agent\ai-agent-app)
25-09-02.15:36:38.755 [main            ] INFO  AiModelApiTest         - The following 1 profile is active: "dev"
25-09-02.15:36:41.356 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_openai. Is empty: false
25-09-02.15:36:41.361 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_openai in schema: public
25-09-02.15:36:41.363 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-09-02.15:36:41.645 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-09-02.15:36:42.164 [main            ] INFO  AiModelApiTest         - Started AiModelApiTest in 3.944 seconds (process running for 5.228)
25-09-02.15:36:44.229 [ForkJoinPool.commonPool-worker-1] INFO  AiModelApiTest         - 测试结果(stream): {"media":[],"messageType":"ASSISTANT","metadata":{"role":"ASSISTANT","messageType":"ASSISTANT","refusal":"","finishReason":"","annotations":[],"index":0,"id":"chatcmpl-381a378d-4948-96b7-bd02-12237b697e9e"},"text":""{"$ref":"$.metadata.annotations"}}
25-09-02.15:36:44.231 [ForkJoinPool.commonPool-worker-1] INFO  AiModelApiTest         - 测试结果(stream): {"media":[],"messageType":"ASSISTANT","metadata":{"role":"ASSISTANT","messageType":"ASSISTANT","refusal":"","finishReason":"","annotations":[],"index":0,"id":"chatcmpl-381a378d-4948-96b7-bd02-12237b697e9e"},"text":"The"{"$ref":"$.metadata.annotations"}}
25-09-02.15:36:44.231 [ForkJoinPool.commonPool-worker-1] INFO  AiModelApiTest         - 测试结果(stream): {"media":[],"messageType":"ASSISTANT","metadata":{"role":"ASSISTANT","messageType":"ASSISTANT","refusal":"","finishReason":"","annotations":[],"index":0,"id":"chatcmpl-381a378d-4948-96b7-bd02-12237b697e9e"},"text":" answer to"{"$ref":"$.metadata.annotations"}}
25-09-02.15:36:44.282 [HttpClient-2-Worker-0] INFO  AiModelApiTest         - 测试结果(stream): {"media":[],"messageType":"ASSISTANT","metadata":{"role":"ASSISTANT","messageType":"ASSISTANT","refusal":"","finishReason":"","annotations":[],"index":0,"id":"chatcmpl-381a378d-4948-96b7-bd02-12237b697e9e"},"text":" \\(1"{"$ref":"$.metadata.annotations"}}
25-09-02.15:36:44.297 [HttpClient-2-Worker-0] INFO  AiModelApiTest         - 测试结果(stream): {"media":[],"messageType":"ASSISTANT","metadata":{"role":"ASSISTANT","messageType":"ASSISTANT","refusal":"","finishReason":"","annotations":[],"index":0,"id":"chatcmpl-381a378d-4948-96b7-bd02-12237b697e9e"},"text":" + 1"{"$ref":"$.metadata.annotations"}}
25-09-02.15:36:44.358 [HttpClient-2-Worker-0] INFO  AiModelApiTest         - 测试结果(stream): {"media":[],"messageType":"ASSISTANT","metadata":{"role":"ASSISTANT","messageType":"ASSISTANT","refusal":"","finishReason":"","annotations":[],"index":0,"id":"chatcmpl-381a378d-4948-96b7-bd02-12237b697e9e"},"text":"\\) is **"{"$ref":"$.metadata.annotations"}}
25-09-02.15:36:44.425 [HttpClient-2-Worker-0] INFO  AiModelApiTest         - 测试结果(stream): {"media":[],"messageType":"ASSISTANT","metadata":{"role":"ASSISTANT","messageType":"ASSISTANT","refusal":"","finishReason":"","annotations":[],"index":0,"id":"chatcmpl-381a378d-4948-96b7-bd02-12237b697e9e"},"text":"2**."{"$ref":"$.metadata.annotations"}}
25-09-02.15:36:44.504 [HttpClient-2-Worker-0] INFO  AiModelApiTest         - 测试结果(stream): {"media":[],"messageType":"ASSISTANT","metadata":{"role":"ASSISTANT","messageType":"ASSISTANT","refusal":"","finishReason":"","annotations":[],"index":0,"id":"chatcmpl-381a378d-4948-96b7-bd02-12237b697e9e"},"text":" \n\n"{"$ref":"$.metadata.annotations"}}
25-09-02.15:36:44.575 [HttpClient-2-Worker-0] INFO  AiModelApiTest         - 测试结果(stream): {"media":[],"messageType":"ASSISTANT","metadata":{"role":"ASSISTANT","messageType":"ASSISTANT","refusal":"","finishReason":"","annotations":[],"index":0,"id":"chatcmpl-381a378d-4948-96b7-bd02-12237b697e9e"},"text":"Here's a"{"$ref":"$.metadata.annotations"}}
25-09-02.15:36:44.598 [HttpClient-2-Worker-0] INFO  AiModelApiTest         - 测试结果(stream): {"media":[],"messageType":"ASSISTANT","metadata":{"role":"ASSISTANT","messageType":"ASSISTANT","refusal":"","finishReason":"","annotations":[],"index":0,"id":"chatcmpl-381a378d-4948-96b7-bd02-12237b697e9e"},"text":" simple breakdown"{"$ref":"$.metadata.annotations"}}
25-09-02.15:36:44.675 [HttpClient-2-Worker-0] INFO  AiModelApiTest         - 测试结果(stream): {"media":[],"messageType":"ASSISTANT","metadata":{"role":"ASSISTANT","messageType":"ASSISTANT","refusal":"","finishReason":"","annotations":[],"index":0,"id":"chatcmpl-381a378d-4948-96b7-bd02-12237b697e9e"},"text":":\n- Start"{"$ref":"$.metadata.annotations"}}
25-09-02.15:36:44.718 [HttpClient-2-Worker-0] INFO  AiModelApiTest         - 测试结果(stream): {"media":[],"messageType":"ASSISTANT","metadata":{"role":"ASSISTANT","messageType":"ASSISTANT","refusal":"","finishReason":"","annotations":[],"index":0,"id":"chatcmpl-381a378d-4948-96b7-bd02-12237b697e9e"},"text":" with "{"$ref":"$.metadata.annotations"}}
25-09-02.15:36:44.801 [HttpClient-2-Worker-0] INFO  AiModelApiTest         - 测试结果(stream): {"media":[],"messageType":"ASSISTANT","metadata":{"role":"ASSISTANT","messageType":"ASSISTANT","refusal":"","finishReason":"","annotations":[],"index":0,"id":"chatcmpl-381a378d-4948-96b7-bd02-12237b697e9e"},"text":"1.\n"{"$ref":"$.metadata.annotations"}}
25-09-02.15:36:44.842 [HttpClient-2-Worker-0] INFO  AiModelApiTest         - 测试结果(stream): {"media":[],"messageType":"ASSISTANT","metadata":{"role":"ASSISTANT","messageType":"ASSISTANT","refusal":"","finishReason":"","annotations":[],"index":0,"id":"chatcmpl-381a378d-4948-96b7-bd02-12237b697e9e"},"text":"- Add another"{"$ref":"$.metadata.annotations"}}
25-09-02.15:36:44.903 [HttpClient-2-Worker-0] INFO  AiModelApiTest         - 测试结果(stream): {"media":[],"messageType":"ASSISTANT","metadata":{"role":"ASSISTANT","messageType":"ASSISTANT","refusal":"","finishReason":"","annotations":[],"index":0,"id":"chatcmpl-381a378d-4948-96b7-bd02-12237b697e9e"},"text":" 1."{"$ref":"$.metadata.annotations"}}
25-09-02.15:36:44.963 [HttpClient-2-Worker-0] INFO  AiModelApiTest         - 测试结果(stream): {"media":[],"messageType":"ASSISTANT","metadata":{"role":"ASSISTANT","messageType":"ASSISTANT","refusal":"","finishReason":"","annotations":[],"index":0,"id":"chatcmpl-381a378d-4948-96b7-bd02-12237b697e9e"},"text":"\n-"{"$ref":"$.metadata.annotations"}}
25-09-02.15:36:45.040 [HttpClient-2-Worker-0] INFO  AiModelApiTest         - 测试结果(stream): {"media":[],"messageType":"ASSISTANT","metadata":{"role":"ASSISTANT","messageType":"ASSISTANT","refusal":"","finishReason":"","annotations":[],"index":0,"id":"chatcmpl-381a378d-4948-96b7-bd02-12237b697e9e"},"text":" The"{"$ref":"$.metadata.annotations"}}
25-09-02.15:36:45.078 [HttpClient-2-Worker-0] INFO  AiModelApiTest         - 测试结果(stream): {"media":[],"messageType":"ASSISTANT","metadata":{"role":"ASSISTANT","messageType":"ASSISTANT","refusal":"","finishReason":"","annotations":[],"index":0,"id":"chatcmpl-381a378d-4948-96b7-bd02-12237b697e9e"},"text":" total is"{"$ref":"$.metadata.annotations"}}
25-09-02.15:36:45.168 [HttpClient-2-Worker-0] INFO  AiModelApiTest         - 测试结果(stream): {"media":[],"messageType":"ASSISTANT","metadata":{"role":"ASSISTANT","messageType":"ASSISTANT","refusal":"","finishReason":"","annotations":[],"index":0,"id":"chatcmpl-381a378d-4948-96b7-bd02-12237b697e9e"},"text":" "{"$ref":"$.metadata.annotations"}}
25-09-02.15:36:45.223 [HttpClient-2-Worker-0] INFO  AiModelApiTest         - 测试结果(stream): {"media":[],"messageType":"ASSISTANT","metadata":{"role":"ASSISTANT","messageType":"ASSISTANT","refusal":"","finishReason":"","annotations":[],"index":0,"id":"chatcmpl-381a378d-4948-96b7-bd02-12237b697e9e"},"text":"2.\n\nThis"{"$ref":"$.metadata.annotations"}}
25-09-02.15:36:45.264 [HttpClient-2-Worker-0] INFO  AiModelApiTest         - 测试结果(stream): {"media":[],"messageType":"ASSISTANT","metadata":{"role":"ASSISTANT","messageType":"ASSISTANT","refusal":"","finishReason":"","annotations":[],"index":0,"id":"chatcmpl-381a378d-4948-96b7-bd02-12237b697e9e"},"text":" is one"{"$ref":"$.metadata.annotations"}}
25-09-02.15:36:45.339 [HttpClient-2-Worker-0] INFO  AiModelApiTest         - 测试结果(stream): {"media":[],"messageType":"ASSISTANT","metadata":{"role":"ASSISTANT","messageType":"ASSISTANT","refusal":"","finishReason":"","annotations":[],"index":0,"id":"chatcmpl-381a378d-4948-96b7-bd02-12237b697e9e"},"text":" of the most"{"$ref":"$.metadata.annotations"}}
25-09-02.15:36:45.399 [HttpClient-2-Worker-0] INFO  AiModelApiTest         - 测试结果(stream): {"media":[],"messageType":"ASSISTANT","metadata":{"role":"ASSISTANT","messageType":"ASSISTANT","refusal":"","finishReason":"","annotations":[],"index":0,"id":"chatcmpl-381a378d-4948-96b7-bd02-12237b697e9e"},"text":" fundamental"{"$ref":"$.metadata.annotations"}}
25-09-02.15:36:45.457 [HttpClient-2-Worker-0] INFO  AiModelApiTest         - 测试结果(stream): {"media":[],"messageType":"ASSISTANT","metadata":{"role":"ASSISTANT","messageType":"ASSISTANT","refusal":"","finishReason":"","annotations":[],"index":0,"id":"chatcmpl-381a378d-4948-96b7-bd02-12237b697e9e"},"text":" addition facts"{"$ref":"$.metadata.annotations"}}
25-09-02.15:36:45.496 [HttpClient-2-Worker-0] INFO  AiModelApiTest         - 测试结果(stream): {"media":[],"messageType":"ASSISTANT","metadata":{"role":"ASSISTANT","messageType":"ASSISTANT","refusal":"","finishReason":"","annotations":[],"index":0,"id":"chatcmpl-381a378d-4948-96b7-bd02-12237b697e9e"},"text":" in mathematics!"{"$ref":"$.metadata.annotations"}}
25-09-02.15:36:45.519 [HttpClient-2-Worker-0] INFO  AiModelApiTest         - 测试结果(stream): {"media":[],"messageType":"ASSISTANT","metadata":{"role":"ASSISTANT","messageType":"ASSISTANT","refusal":"","finishReason":"","annotations":[],"index":0,"id":"chatcmpl-381a378d-4948-96b7-bd02-12237b697e9e"},"text":" 😊"{"$ref":"$.metadata.annotations"}}
25-09-02.15:36:45.520 [HttpClient-2-Worker-0] INFO  AiModelApiTest         - 测试结果(stream): {"media":[],"messageType":"ASSISTANT","metadata":{"role":"ASSISTANT","messageType":"ASSISTANT","refusal":"","finishReason":"STOP","annotations":[],"index":0,"id":"chatcmpl-381a378d-4948-96b7-bd02-12237b697e9e"},"text":""{"$ref":"$.metadata.annotations"}}
25-09-02.15:36:45.520 [HttpClient-2-Worker-0] INFO  AiModelApiTest         - 测试结果(stream): done!
25-09-02.15:40:06.752 [main            ] INFO  AutoAgentTest          - Starting AutoAgentTest using Java 17.0.12 with PID 3024 (started by 30562 in D:\ACode\project\ai-agent\ai-agent-app)
25-09-02.15:40:06.755 [main            ] INFO  AutoAgentTest          - The following 1 profile is active: "dev"
25-09-02.15:40:09.246 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_openai. Is empty: false
25-09-02.15:40:09.250 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_openai in schema: public
25-09-02.15:40:09.250 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-09-02.15:40:09.538 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-09-02.15:40:10.055 [main            ] INFO  AutoAgentTest          - Started AutoAgentTest in 3.824 seconds (process running for 5.036)
25-09-02.15:40:10.779 [main            ] INFO  RootNode               - AIClient装配链---RootNode---开始查询配置信息
25-09-02.15:40:10.781 [pool-2-thread-1 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_api) [3101, 3102, 3103]
25-09-02.15:40:10.781 [pool-2-thread-2 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_model) [3101, 3102, 3103]
25-09-02.15:40:10.781 [pool-2-thread-3 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_tool_mcp) [3101, 3102, 3103]
25-09-02.15:40:10.781 [pool-2-thread-4 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_system_prompt) [3101, 3102, 3103]
25-09-02.15:40:10.781 [pool-2-thread-5 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_advisor) [3101, 3102, 3103]
25-09-02.15:40:10.782 [pool-2-thread-6 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client) [3101, 3102, 3103]
25-09-02.15:40:10.802 [pool-2-thread-5 ] INFO  HikariDataSource       - MainHikariPool - Starting...
25-09-02.15:40:11.026 [pool-2-thread-5 ] INFO  HikariPool             - MainHikariPool - Added connection com.mysql.cj.jdbc.ConnectionImpl@e901303
25-09-02.15:40:11.027 [pool-2-thread-5 ] INFO  HikariDataSource       - MainHikariPool - Start completed.
25-09-02.15:40:11.233 [main            ] INFO  RootNode               - AIClient装配链---RootNode---查询配置信息完毕
25-09-02.15:40:11.233 [main            ] INFO  AiClientApiNode        - AIClient装配链---AiClientApiNode---开始装配API
25-09-02.15:40:11.241 [main            ] INFO  AiClientMcpNode        - AIClient装配链---AiClientMcpNode---开始装配MCP
25-09-02.15:40:11.932 [HttpClient-10-Worker-0] INFO  McpAsyncClient         - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[completions=null, experimental={}, logging=null, prompts=PromptCapabilities[listChanged=false], resources=ResourceCapabilities[subscribe=false, listChanged=false], tools=ToolCapabilities[listChanged=false]], Info: Implementation[name=AIsearch, version=1.4.1] and Instructions null
25-09-02.15:40:11.999 [main            ] INFO  AiClientMcpNode        - sse方式注册mcp: baidu-search 成功
25-09-02.15:40:11.999 [main            ] INFO  AiClientModelNode      - AIClient装配链---AiClientModelNode---开始装配Model
25-09-02.15:40:12.001 [main            ] INFO  AiClientModelNode      - 注册模型-modelId:2001, modelName:deepseek-v3-成功
25-09-02.15:40:12.001 [main            ] INFO  AiClientAdvisorNode    - AIClient装配链---AiClientAdvisorNode---开始装配顾问
25-09-02.15:40:12.012 [main            ] INFO  AiClientAdvisorNode    - 注册顾问---advisorId:4001, advisorName:记忆---成功
25-09-02.15:40:12.012 [main            ] INFO  AiClientNode           - AIClient装配链---AiClientNode---开始装配Client
25-09-02.15:40:12.165 [main            ] INFO  AiClientNode           - 注册Client---clientId:3101, clientName:任务分析和状态判断---成功
25-09-02.15:40:12.224 [main            ] INFO  AiClientNode           - 注册Client---clientId:3102, clientName:具体任务执行---成功
25-09-02.15:40:12.295 [main            ] INFO  AiClientNode           - 注册Client---clientId:3103, clientName:质量检查和优化---成功
25-09-02.15:40:12.296 [main            ] INFO  AutoAgentTest          - 客户端构建:org.springframework.ai.chat.client.DefaultChatClient@61c3a93f
25-09-02.15:40:12.302 [main            ] INFO  RootNode               - === 动态多轮执行测试开始 ====
25-09-02.15:40:12.302 [main            ] INFO  RootNode               - 用户输入: 搜索小傅哥，技术项目列表。编写成一份文档，说明不同项目的学习目标，以及不同阶段的伙伴应该学习哪个项目。
25-09-02.15:40:12.302 [main            ] INFO  RootNode               - 最大执行步数: 3
25-09-02.15:40:12.302 [main            ] INFO  RootNode               - 会话ID: session-id-1756798812296
25-09-02.15:40:12.302 [main            ] INFO  Step1TaskAnalyzerClient - === 动态多轮执行第1步 Step1 ====
25-09-02.15:40:12.302 [main            ] INFO  Step1TaskAnalyzerClient - 
📊 阶段1: 任务状态分析
25-09-02.15:40:45.025 [main            ] INFO  Step1TaskAnalyzerClient - 
📊 === 第 1 步分析结果 ===
25-09-02.15:40:45.026 [main            ] INFO  Step1TaskAnalyzerClient - 
🎯 任务状态分析:
25-09-02.15:40:45.026 [main            ] INFO  Step1TaskAnalyzerClient -    📋 当前已获取核心数据：
25-09-02.15:40:45.026 [main            ] INFO  Step1TaskAnalyzerClient -    📋 1. 确认小傅哥的技术项目体系（含5类项目）
25-09-02.15:40:45.026 [main            ] INFO  Step1TaskAnalyzerClient -    📋 2. 获得阶段性学习路线（基础→组件→架构）
25-09-02.15:40:45.026 [main            ] INFO  Step1TaskAnalyzerClient -    📋 3. 掌握项目难度分级（1-5星标注）
25-09-02.15:40:45.026 [main            ] INFO  Step1TaskAnalyzerClient - 
📈 执行历史评估:
25-09-02.15:40:45.026 [main            ] INFO  Step1TaskAnalyzerClient -    📊 - 成功获取CSDN/掘金等技术社区的权威内容
25-09-02.15:40:45.026 [main            ] INFO  Step1TaskAnalyzerClient -    📊 - 验证项目信息包含技术栈/适用人群等关键字段
25-09-02.15:40:45.026 [main            ] INFO  Step1TaskAnalyzerClient -    📊 - 发现结构化学习路线（180天Java计划）
25-09-02.15:40:45.026 [main            ] INFO  Step1TaskAnalyzerClient - 
🚀 下一步策略:
25-09-02.15:40:45.026 [main            ] INFO  Step1TaskAnalyzerClient -    🎯 1. **数据清洗**：去重合并相同项目（如OpenAI SDK重复出现）
25-09-02.15:40:45.026 [main            ] INFO  Step1TaskAnalyzerClient -    🎯 2. **维度提取**：按以下结构化字段整理：
25-09-02.15:40:45.026 [main            ] INFO  Step1TaskAnalyzerClient -    🎯 ```markdown
25-09-02.15:40:45.026 [main            ] INFO  Step1TaskAnalyzerClient -    🎯 | 项目名称       | 技术栈                 | 学习目标                     | 难度 | 推荐阶段       |
25-09-02.15:40:45.026 [main            ] INFO  Step1TaskAnalyzerClient -    🎯 |----------------|------------------------|------------------------------|------|----------------|
25-09-02.15:40:45.026 [main            ] INFO  Step1TaskAnalyzerClient -    🎯 | Lottery DDD    | SpringCloud+DDD        | 分布式架构设计能力           | ★★★★ | 进阶(3-5年)   |
25-09-02.15:40:45.026 [main            ] INFO  Step1TaskAnalyzerClient -    🎯 | OpenAI SDK     | 大模型对接+设计模式    | 组件抽象与标准化能力         | ★★★★★| 高级(5年+)    |
25-09-02.15:40:45.026 [main            ] INFO  Step1TaskAnalyzerClient -    🎯 ```
25-09-02.15:40:45.026 [main            ] INFO  Step1TaskAnalyzerClient -    🎯 3. **阶段映射**：根据原文中的路线A/B划分关联学习者阶段
25-09-02.15:40:45.026 [main            ] INFO  Step1TaskAnalyzerClient - 
📊 完成度评估: ** 40%
25-09-02.15:40:45.026 [main            ] INFO  Step1TaskAnalyzerClient - 
🔄 任务状态: 继续执行
25-09-02.15:40:45.026 [main            ] INFO  Step1TaskAnalyzerClient -    📝 （以下为建议的文档大纲框架）
25-09-02.15:40:45.026 [main            ] INFO  Step1TaskAnalyzerClient -    📝 ```markdown
25-09-02.15:40:45.026 [main            ] INFO  Step1TaskAnalyzerClient -    📝 # 小傅哥技术项目学习指南
25-09-02.15:40:45.026 [main            ] INFO  Step1TaskAnalyzerClient -    📝 ## 一、项目全景图
25-09-02.15:40:45.026 [main            ] INFO  Step1TaskAnalyzerClient -    📝 ### 1. 基础夯实类
25-09-02.15:40:45.026 [main            ] INFO  Step1TaskAnalyzerClient -    📝 - 《重学Java设计模式》(23天)
25-09-02.15:40:45.026 [main            ] INFO  Step1TaskAnalyzerClient -    📝 - 目标：掌握23种设计模式实战
25-09-02.15:40:45.026 [main            ] INFO  Step1TaskAnalyzerClient -    📝 - 适合：0-1年开发者
25-09-02.15:40:45.026 [main            ] INFO  Step1TaskAnalyzerClient -    📝 ### 2. 组件开发类
25-09-02.15:40:45.026 [main            ] INFO  Step1TaskAnalyzerClient -    📝 - API网关(37天)
25-09-02.15:40:45.028 [main            ] INFO  Step1TaskAnalyzerClient -    📝 - 技术栈：Netty+SPI扩展
25-09-02.15:40:45.028 [main            ] INFO  Step1TaskAnalyzerClient -    📝 - 目标：中间件设计能力
25-09-02.15:40:45.028 [main            ] INFO  Step1TaskAnalyzerClient -    📝 ## 二、阶段匹配建议
25-09-02.15:40:45.028 [main            ] INFO  Step1TaskAnalyzerClient -    📝 | 学习者阶段 | 核心目标           | 推荐项目                 |
25-09-02.15:40:45.028 [main            ] INFO  Step1TaskAnalyzerClient -    📝 |------------|--------------------|--------------------------|
25-09-02.15:40:45.028 [main            ] INFO  Step1TaskAnalyzerClient -    📝 | 入门(0-1年)| 语法+设计模式      | 数据结构+设计模式课程    |
25-09-02.15:40:45.028 [main            ] INFO  Step1TaskAnalyzerClient -    📝 | 进阶(1-3年)| 分布式架构         | Lottery DDD抽奖系统      |
25-09-02.15:40:45.028 [main            ] INFO  Step1TaskAnalyzerClient -    📝 ```
25-09-02.15:40:45.028 [main            ] INFO  Step2PrecisionExecutorClient - === 动态多轮执行第1步 Step2 ====
25-09-02.15:40:45.028 [main            ] INFO  Step2PrecisionExecutorClient - 
📊 阶段2: 精准任务执行
25-09-02.15:41:02.693 [main            ] INFO  Step2PrecisionExecutorClient - 
⚡ === 第 1 步执行结果 ===
25-09-02.15:41:02.693 [main            ] INFO  Step2PrecisionExecutorClient -    📝 **任务状态分析:**
25-09-02.15:41:02.693 [main            ] INFO  Step2PrecisionExecutorClient -    📝 当前已获取核心数据：
25-09-02.15:41:02.693 [main            ] INFO  Step2PrecisionExecutorClient -    📝 1. 确认小傅哥的技术项目体系（含5类项目）
25-09-02.15:41:02.693 [main            ] INFO  Step2PrecisionExecutorClient -    📝 2. 获得阶段性学习路线（基础→组件→架构）
25-09-02.15:41:02.693 [main            ] INFO  Step2PrecisionExecutorClient -    📝 3. 掌握项目难度分级（1-5星标注）
25-09-02.15:41:02.693 [main            ] INFO  Step2PrecisionExecutorClient -    📝 **执行历史评估:**
25-09-02.15:41:02.693 [main            ] INFO  Step2PrecisionExecutorClient -    📝 - 成功获取CSDN/掘金等技术社区的权威内容
25-09-02.15:41:02.693 [main            ] INFO  Step2PrecisionExecutorClient -    📝 - 验证项目信息包含技术栈/适用人群等关键字段
25-09-02.15:41:02.693 [main            ] INFO  Step2PrecisionExecutorClient -    📝 - 发现结构化学习路线（180天Java计划）
25-09-02.15:41:02.693 [main            ] INFO  Step2PrecisionExecutorClient -    📝 **下一步策略:**
25-09-02.15:41:02.693 [main            ] INFO  Step2PrecisionExecutorClient -    📝 1. **数据清洗**：去重合并相同项目（如OpenAI SDK重复出现）
25-09-02.15:41:02.693 [main            ] INFO  Step2PrecisionExecutorClient -    📝 2. **维度提取**：按以下结构化字段整理：
25-09-02.15:41:02.693 [main            ] INFO  Step2PrecisionExecutorClient -    📝 ```markdown
25-09-02.15:41:02.693 [main            ] INFO  Step2PrecisionExecutorClient -    📝 | 项目名称       | 技术栈                 | 学习目标                     | 难度 | 推荐阶段       |
25-09-02.15:41:02.693 [main            ] INFO  Step2PrecisionExecutorClient -    📝 |----------------|------------------------|------------------------------|------|----------------|
25-09-02.15:41:02.693 [main            ] INFO  Step2PrecisionExecutorClient -    📝 | Lottery DDD    | SpringCloud+DDD        | 分布式架构设计能力           | ★★★★ | 进阶(3-5年)   |
25-09-02.15:41:02.693 [main            ] INFO  Step2PrecisionExecutorClient -    📝 | OpenAI SDK     | 大模型对接+设计模式    | 组件抽象与标准化能力         | ★★★★★| 高级(5年+)    |
25-09-02.15:41:02.693 [main            ] INFO  Step2PrecisionExecutorClient -    📝 ```
25-09-02.15:41:02.693 [main            ] INFO  Step2PrecisionExecutorClient -    📝 3. **阶段映射**：根据原文中的路线A/B划分关联学习者阶段
25-09-02.15:41:02.694 [main            ] INFO  Step2PrecisionExecutorClient -    📝 **完成度评估:** 40%
25-09-02.15:41:02.694 [main            ] INFO  Step2PrecisionExecutorClient -    📝 **任务状态:** CONTINUE
25-09-02.15:41:02.694 [main            ] INFO  Step2PrecisionExecutorClient -    📝 （以下为建议的文档大纲框架）
25-09-02.15:41:02.694 [main            ] INFO  Step2PrecisionExecutorClient -    📝 ```markdown
25-09-02.15:41:02.694 [main            ] INFO  Step2PrecisionExecutorClient -    📝 # 小傅哥技术项目学习指南
25-09-02.15:41:02.694 [main            ] INFO  Step2PrecisionExecutorClient -    📝 ## 一、项目全景图
25-09-02.15:41:02.694 [main            ] INFO  Step2PrecisionExecutorClient -    📝 ### 1. 基础夯实类
25-09-02.15:41:02.694 [main            ] INFO  Step2PrecisionExecutorClient -    📝 - 《重学Java设计模式》(23天)
25-09-02.15:41:02.694 [main            ] INFO  Step2PrecisionExecutorClient -    📝 - 目标：掌握23种设计模式实战
25-09-02.15:41:02.694 [main            ] INFO  Step2PrecisionExecutorClient -    📝 - 适合：0-1年开发者
25-09-02.15:41:02.694 [main            ] INFO  Step2PrecisionExecutorClient -    📝 ### 2. 组件开发类
25-09-02.15:41:02.694 [main            ] INFO  Step2PrecisionExecutorClient -    📝 - API网关(37天)
25-09-02.15:41:02.694 [main            ] INFO  Step2PrecisionExecutorClient -    📝 - 技术栈：Netty+SPI扩展
25-09-02.15:41:02.694 [main            ] INFO  Step2PrecisionExecutorClient -    📝 - 目标：中间件设计能力
25-09-02.15:41:02.694 [main            ] INFO  Step2PrecisionExecutorClient -    📝 ## 二、阶段匹配建议
25-09-02.15:41:02.694 [main            ] INFO  Step2PrecisionExecutorClient -    📝 | 学习者阶段 | 核心目标           | 推荐项目                 |
25-09-02.15:41:02.694 [main            ] INFO  Step2PrecisionExecutorClient -    📝 |------------|--------------------|--------------------------|
25-09-02.15:41:02.695 [main            ] INFO  Step2PrecisionExecutorClient -    📝 | 入门(0-1年)| 语法+设计模式      | 数据结构+设计模式课程    |
25-09-02.15:41:02.695 [main            ] INFO  Step2PrecisionExecutorClient -    📝 | 进阶(1-3年)| 分布式架构         | Lottery DDD抽奖系统      |
25-09-02.15:41:02.695 [main            ] INFO  Step2PrecisionExecutorClient -    📝 ```
25-09-02.15:41:02.695 [main            ] INFO  Step3PrecisionExecutorClient - === 动态多轮执行第1步 Step3 ====
25-09-02.15:41:02.695 [main            ] INFO  Step3PrecisionExecutorClient - 
🔍 阶段3: 质量监督检查
25-09-02.15:41:14.710 [main            ] INFO  Step3PrecisionExecutorClient - 
🔍 === 第 1 步监督结果 ===
25-09-02.15:41:14.710 [main            ] INFO  Step3PrecisionExecutorClient - 
📊 质量评估:
25-09-02.15:41:14.710 [main            ] INFO  Step3PrecisionExecutorClient -    📋 1. **准确性**: 执行结果准确反映了项目列表和技术栈，数据清洗和去重处理得当，技术栈描述与官方文档一致，验证了准确性。
25-09-02.15:41:14.710 [main            ] INFO  Step3PrecisionExecutorClient -    📋 2. **完整性**: 所有5类项目（基础/组件/架构/系统/智能）均已覆盖，项目列表完整，学习目标和阶段映射清晰。
25-09-02.15:41:14.710 [main            ] INFO  Step3PrecisionExecutorClient -    📋 3. **相关性**: 结果符合用户需求，提供了不同阶段的学习路径建议，关联性高。
25-09-02.15:41:14.710 [main            ] INFO  Step3PrecisionExecutorClient -    📋 4. **可用性**: Markdown表格格式规范，字段对齐，可读性强，便于用户理解和参考。
25-09-02.15:41:14.710 [main            ] INFO  Step3PrecisionExecutorClient - 
⚠️ 问题识别:
25-09-02.15:41:14.710 [main            ] INFO  Step3PrecisionExecutorClient -    ⚠️ 1. **项目详细说明文档缺失**: 虽然项目列表完整，但每个项目的详细说明文档尚未补充，可能会影响用户对项目的深入理解。
25-09-02.15:41:14.710 [main            ] INFO  Step3PrecisionExecutorClient -    ⚠️ 2. **学习路径描述不够细化**: 当前阶段映射（如“入门(0-1年)”）较为笼统，缺乏更具体的细分（如“0-6个月”和“6-12个月”）。
25-09-02.15:41:14.710 [main            ] INFO  Step3PrecisionExecutorClient -    ⚠️ 3. **技术栈描述深度不足**: 技术栈仅列出名称，未提供具体技术点或学习重点，可能影响用户的学习针对性。
25-09-02.15:41:14.710 [main            ] INFO  Step3PrecisionExecutorClient -    ⚠️ 4. **难度星级标准未公开**: 难度星级的判定标准未明确说明，可能导致用户对难度的理解存在偏差。
25-09-02.15:41:14.710 [main            ] INFO  Step3PrecisionExecutorClient - 
💡 改进建议:
25-09-02.15:41:14.710 [main            ] INFO  Step3PrecisionExecutorClient -    💡 1. **补充项目详细说明文档**: 为每个项目添加详细的说明文档，包括项目背景、核心功能、技术难点、学习收益等，帮助用户全面了解项目。
25-09-02.15:41:14.710 [main            ] INFO  Step3PrecisionExecutorClient -    💡 2. **细化学习路径描述**: 将学习阶段进一步细分（如“0-6个月”、“6-12个月”、“1-3年”等），并提供每个阶段的具体学习目标和项目推荐。
25-09-02.15:41:14.710 [main            ] INFO  Step3PrecisionExecutorClient -    💡 3. **深化技术栈描述**: 在技术栈中补充关键技术的具体学习点（如“Netty的核心组件：Channel、EventLoop”），提升学习的针对性。
25-09-02.15:41:14.710 [main            ] INFO  Step3PrecisionExecutorClient -    💡 4. **公开难度星级标准**: 明确难度星级的判定标准（如“★★★：需掌握分布式基础”），增强透明度和用户信任。
25-09-02.15:41:14.710 [main            ] INFO  Step3PrecisionExecutorClient -    💡 5. **增加学习资源链接**: 为每个项目附加相关的学习资源链接（如GitHub仓库、技术博客等），方便用户深入学习。
25-09-02.15:41:14.710 [main            ] INFO  Step3PrecisionExecutorClient - 
📊 质量评分: ** 8.5/10
25-09-02.15:41:14.710 [main            ] INFO  Step3PrecisionExecutorClient - 
🔧 检查结果: 需要优化
25-09-02.15:41:14.712 [main            ] INFO  Step3PrecisionExecutorClient - ✅ 质量检查通过
25-09-02.15:41:14.712 [main            ] INFO  Step4QualitySupervisorClient - === 动态多轮执行第2步 Step4 ====
25-09-02.15:41:14.712 [main            ] INFO  Step4QualitySupervisorClient - 
📊 阶段4: 执行总结分析
25-09-02.15:41:14.712 [main            ] INFO  Step4QualitySupervisorClient - 
📊 === 动态多轮执行总结 ====
25-09-02.15:41:14.712 [main            ] INFO  Step4QualitySupervisorClient - 📈 总执行步数: 2 步
25-09-02.15:41:14.712 [main            ] INFO  Step4QualitySupervisorClient - ✅ 任务完成状态: 已完成
25-09-02.15:41:14.726 [main            ] INFO  Step4QualitySupervisorClient - 📊 执行效率: 100.0%
25-09-02.15:41:14.726 [main            ] INFO  Step4QualitySupervisorClient - 
🏁 === 动态多轮执行测试结束 ====
25-09-02.15:41:14.726 [main            ] INFO  AutoAgentTest          - 测试结果:AI动态多轮执行结束
25-09-02.15:41:14.737 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MainHikariPool - Shutdown initiated...
25-09-02.15:41:14.749 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MainHikariPool - Shutdown completed.
25-09-02.21:22:54.488 [main            ] INFO  AiAgentTest            - Starting AiAgentTest using Java 17.0.12 with PID 34412 (started by 30562 in D:\ACode\project\ai-agent\ai-agent-app)
25-09-02.21:22:54.490 [main            ] INFO  AiAgentTest            - The following 1 profile is active: "dev"
25-09-02.21:22:57.472 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_openai. Is empty: false
25-09-02.21:22:57.480 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_openai in schema: public
25-09-02.21:22:57.480 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-09-02.21:22:57.836 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-09-02.21:22:58.374 [main            ] INFO  AiAgentTest            - Started AiAgentTest in 4.464 seconds (process running for 5.979)
25-09-02.21:22:58.379 [main            ] INFO  AgentAutoLoadConfig    - 开始自动加载Agent
25-09-02.21:22:58.379 [main            ] INFO  AgentAutoLoadConfig    - 开始自动装配AI客户端，客户端ID列表: [3101, 3102, 3103, 3104]
25-09-02.21:22:58.382 [main            ] INFO  RootNode               - AIClient装配链---RootNode---开始查询配置信息
25-09-02.21:22:58.383 [pool-2-thread-1 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_api) [3101, 3102, 3103, 3104]
25-09-02.21:22:58.385 [pool-2-thread-2 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_model) [3101, 3102, 3103, 3104]
25-09-02.21:22:58.385 [pool-2-thread-3 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_tool_mcp) [3101, 3102, 3103, 3104]
25-09-02.21:22:58.385 [pool-2-thread-4 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_system_prompt) [3101, 3102, 3103, 3104]
25-09-02.21:22:58.385 [pool-2-thread-5 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_advisor) [3101, 3102, 3103, 3104]
25-09-02.21:22:58.386 [pool-2-thread-6 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client) [3101, 3102, 3103, 3104]
25-09-02.21:22:58.410 [pool-2-thread-2 ] INFO  HikariDataSource       - MainHikariPool - Starting...
25-09-02.21:22:58.673 [pool-2-thread-2 ] INFO  HikariPool             - MainHikariPool - Added connection com.mysql.cj.jdbc.ConnectionImpl@428e86fb
25-09-02.21:22:58.677 [pool-2-thread-2 ] INFO  HikariDataSource       - MainHikariPool - Start completed.
25-09-02.21:22:58.972 [main            ] INFO  RootNode               - AIClient装配链---RootNode---查询配置信息完毕
25-09-02.21:22:58.972 [main            ] INFO  AiClientApiNode        - AIClient装配链---AiClientApiNode---开始装配API
25-09-02.21:22:58.980 [main            ] INFO  AiClientMcpNode        - AIClient装配链---AiClientMcpNode---开始装配MCP
25-09-02.21:22:59.543 [HttpClient-10-Worker-0] INFO  McpAsyncClient         - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[completions=null, experimental={}, logging=null, prompts=PromptCapabilities[listChanged=false], resources=ResourceCapabilities[subscribe=false, listChanged=false], tools=ToolCapabilities[listChanged=false]], Info: Implementation[name=AIsearch, version=1.4.1] and Instructions null
25-09-02.21:22:59.617 [main            ] INFO  AiClientMcpNode        - sse方式注册mcp: baidu-search 成功
25-09-02.21:22:59.617 [main            ] INFO  AiClientModelNode      - AIClient装配链---AiClientModelNode---开始装配Model
25-09-02.21:22:59.617 [main            ] INFO  AiClientModelNode      - 注册模型-modelId:2001, modelName:deepseek-v3-成功
25-09-02.21:22:59.617 [main            ] INFO  AiClientAdvisorNode    - AIClient装配链---AiClientAdvisorNode---开始装配顾问
25-09-02.21:22:59.630 [main            ] INFO  AiClientAdvisorNode    - 注册顾问---advisorId:4001, advisorName:记忆---成功
25-09-02.21:22:59.630 [main            ] INFO  AiClientNode           - AIClient装配链---AiClientNode---开始装配Client
25-09-02.21:22:59.743 [main            ] INFO  AiClientNode           - 注册Client---clientId:3101, clientName:任务分析和状态判断---成功
25-09-02.21:22:59.827 [main            ] INFO  AiClientNode           - 注册Client---clientId:3102, clientName:具体任务执行---成功
25-09-02.21:22:59.898 [main            ] INFO  AiClientNode           - 注册Client---clientId:3103, clientName:质量检查和优化---成功
25-09-02.21:22:59.898 [main            ] INFO  AgentAutoLoadConfig    - 自动装配AI客户端完成
25-09-02.21:23:00.815 [HttpClient-13-Worker-1] INFO  McpAsyncClient         - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[completions=null, experimental={}, logging=null, prompts=PromptCapabilities[listChanged=false], resources=ResourceCapabilities[subscribe=false, listChanged=false], tools=ToolCapabilities[listChanged=false]], Info: Implementation[name=AIsearch, version=1.4.1] and Instructions null
25-09-02.21:23:36.779 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MainHikariPool - Shutdown initiated...
25-09-02.21:23:36.793 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MainHikariPool - Shutdown completed.
25-09-02.21:26:42.948 [main            ] INFO  AiAgentTest            - Starting AiAgentTest using Java 17.0.12 with PID 36060 (started by 30562 in D:\ACode\project\ai-agent\ai-agent-app)
25-09-02.21:26:42.950 [main            ] INFO  AiAgentTest            - The following 1 profile is active: "dev"
25-09-02.21:26:45.815 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_openai. Is empty: false
25-09-02.21:26:45.821 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_openai in schema: public
25-09-02.21:26:45.821 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-09-02.21:26:46.156 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-09-02.21:26:46.719 [main            ] INFO  AiAgentTest            - Started AiAgentTest in 4.329 seconds (process running for 5.67)
25-09-02.21:26:46.724 [main            ] INFO  AgentAutoLoadConfig    - 开始自动加载Agent
25-09-02.21:26:46.724 [main            ] INFO  AgentAutoLoadConfig    - 开始自动装配AI客户端，客户端ID列表: [3101, 3102, 3103, 3104]
25-09-02.21:26:46.728 [main            ] INFO  RootNode               - AIClient装配链---RootNode---开始查询配置信息
25-09-02.21:26:46.746 [pool-2-thread-1 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_api) [3101, 3102, 3103, 3104]
25-09-02.21:26:46.746 [pool-2-thread-2 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_model) [3101, 3102, 3103, 3104]
25-09-02.21:26:46.747 [pool-2-thread-3 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_tool_mcp) [3101, 3102, 3103, 3104]
25-09-02.21:26:46.747 [pool-2-thread-4 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_system_prompt) [3101, 3102, 3103, 3104]
25-09-02.21:26:46.747 [pool-2-thread-5 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_advisor) [3101, 3102, 3103, 3104]
25-09-02.21:26:46.749 [pool-2-thread-6 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client) [3101, 3102, 3103, 3104]
25-09-02.21:26:46.774 [pool-2-thread-6 ] INFO  HikariDataSource       - MainHikariPool - Starting...
25-09-02.21:26:47.022 [pool-2-thread-6 ] INFO  HikariPool             - MainHikariPool - Added connection com.mysql.cj.jdbc.ConnectionImpl@1db400d0
25-09-02.21:26:47.023 [pool-2-thread-6 ] INFO  HikariDataSource       - MainHikariPool - Start completed.
25-09-02.21:26:47.276 [main            ] INFO  RootNode               - AIClient装配链---RootNode---查询配置信息完毕
25-09-02.21:26:47.276 [main            ] INFO  AiClientApiNode        - AIClient装配链---AiClientApiNode---开始装配API
25-09-02.21:26:47.283 [main            ] INFO  AiClientMcpNode        - AIClient装配链---AiClientMcpNode---开始装配MCP
25-09-02.21:26:47.636 [HttpClient-10-Worker-0] INFO  McpAsyncClient         - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[completions=null, experimental={}, logging=null, prompts=PromptCapabilities[listChanged=false], resources=ResourceCapabilities[subscribe=false, listChanged=false], tools=ToolCapabilities[listChanged=false]], Info: Implementation[name=AIsearch, version=1.4.1] and Instructions null
25-09-02.21:26:47.723 [main            ] INFO  AiClientMcpNode        - sse方式注册mcp: baidu-search 成功
25-09-02.21:26:47.723 [main            ] INFO  AiClientModelNode      - AIClient装配链---AiClientModelNode---开始装配Model
25-09-02.21:26:47.723 [main            ] INFO  AiClientModelNode      - 注册模型-modelId:2001, modelName:deepseek-v3-成功
25-09-02.21:26:47.723 [main            ] INFO  AiClientAdvisorNode    - AIClient装配链---AiClientAdvisorNode---开始装配顾问
25-09-02.21:26:47.736 [main            ] INFO  AiClientAdvisorNode    - 注册顾问---advisorId:4001, advisorName:记忆---成功
25-09-02.21:26:47.736 [main            ] INFO  AiClientNode           - AIClient装配链---AiClientNode---开始装配Client
25-09-02.21:26:47.837 [main            ] INFO  AiClientNode           - 注册Client---clientId:3101, clientName:任务分析和状态判断---成功
25-09-02.21:26:47.914 [main            ] INFO  AiClientNode           - 注册Client---clientId:3102, clientName:具体任务执行---成功
25-09-02.21:26:47.980 [main            ] INFO  AiClientNode           - 注册Client---clientId:3103, clientName:质量检查和优化---成功
25-09-02.21:26:47.980 [main            ] INFO  AgentAutoLoadConfig    - 自动装配AI客户端完成
25-09-02.21:26:48.854 [HttpClient-13-Worker-0] INFO  McpAsyncClient         - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[completions=null, experimental={}, logging=null, prompts=PromptCapabilities[listChanged=false], resources=ResourceCapabilities[subscribe=false, listChanged=false], tools=ToolCapabilities[listChanged=false]], Info: Implementation[name=AIsearch, version=1.4.1] and Instructions null
25-09-02.21:28:36.204 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MainHikariPool - Shutdown initiated...
25-09-02.21:28:36.218 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MainHikariPool - Shutdown completed.
25-09-02.21:29:59.008 [main            ] INFO  AiAgentTest            - Starting AiAgentTest using Java 17.0.12 with PID 36848 (started by 30562 in D:\ACode\project\ai-agent\ai-agent-app)
25-09-02.21:29:59.011 [main            ] INFO  AiAgentTest            - The following 1 profile is active: "dev"
25-09-02.21:30:01.866 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_openai. Is empty: false
25-09-02.21:30:01.873 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_openai in schema: public
25-09-02.21:30:01.873 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-09-02.21:30:02.190 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-09-02.21:30:02.797 [main            ] INFO  AiAgentTest            - Started AiAgentTest in 4.386 seconds (process running for 5.748)
25-09-02.21:30:02.801 [main            ] INFO  AgentAutoLoadConfig    - 开始自动加载Agent
25-09-02.21:30:02.801 [main            ] INFO  AgentAutoLoadConfig    - 开始自动装配AI客户端，客户端ID列表: [3101, 3102, 3103, 3104]
25-09-02.21:30:02.804 [main            ] INFO  RootNode               - AIClient装配链---RootNode---开始查询配置信息
25-09-02.21:30:02.805 [pool-2-thread-1 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_api) [3101, 3102, 3103, 3104]
25-09-02.21:30:02.805 [pool-2-thread-2 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_model) [3101, 3102, 3103, 3104]
25-09-02.21:30:02.805 [pool-2-thread-3 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_tool_mcp) [3101, 3102, 3103, 3104]
25-09-02.21:30:02.805 [pool-2-thread-4 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_system_prompt) [3101, 3102, 3103, 3104]
25-09-02.21:30:02.807 [pool-2-thread-5 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_advisor) [3101, 3102, 3103, 3104]
25-09-02.21:30:02.807 [pool-2-thread-6 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client) [3101, 3102, 3103, 3104]
25-09-02.21:30:02.832 [pool-2-thread-5 ] INFO  HikariDataSource       - MainHikariPool - Starting...
25-09-02.21:30:03.082 [pool-2-thread-5 ] INFO  HikariPool             - MainHikariPool - Added connection com.mysql.cj.jdbc.ConnectionImpl@70d1be11
25-09-02.21:30:03.083 [pool-2-thread-5 ] INFO  HikariDataSource       - MainHikariPool - Start completed.
25-09-02.21:30:03.355 [main            ] INFO  RootNode               - AIClient装配链---RootNode---查询配置信息完毕
25-09-02.21:30:03.356 [main            ] INFO  AiClientApiNode        - AIClient装配链---AiClientApiNode---开始装配API
25-09-02.21:30:03.363 [main            ] INFO  AiClientMcpNode        - AIClient装配链---AiClientMcpNode---开始装配MCP
25-09-02.21:30:03.662 [HttpClient-10-Worker-0] INFO  McpAsyncClient         - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[completions=null, experimental={}, logging=null, prompts=PromptCapabilities[listChanged=false], resources=ResourceCapabilities[subscribe=false, listChanged=false], tools=ToolCapabilities[listChanged=false]], Info: Implementation[name=AIsearch, version=1.4.1] and Instructions null
25-09-02.21:30:03.710 [main            ] INFO  AiClientMcpNode        - sse方式注册mcp: baidu-search 成功
25-09-02.21:30:03.710 [main            ] INFO  AiClientModelNode      - AIClient装配链---AiClientModelNode---开始装配Model
25-09-02.21:30:03.712 [main            ] INFO  AiClientModelNode      - 注册模型-modelId:2001, modelName:deepseek-v3-成功
25-09-02.21:30:03.712 [main            ] INFO  AiClientAdvisorNode    - AIClient装配链---AiClientAdvisorNode---开始装配顾问
25-09-02.21:30:03.723 [main            ] INFO  AiClientAdvisorNode    - 注册顾问---advisorId:4001, advisorName:记忆---成功
25-09-02.21:30:03.724 [main            ] INFO  AiClientNode           - AIClient装配链---AiClientNode---开始装配Client
25-09-02.21:30:03.795 [main            ] INFO  AiClientNode           - 注册Client---clientId:3101, clientName:任务分析和状态判断---成功
25-09-02.21:30:03.833 [main            ] INFO  AiClientNode           - 注册Client---clientId:3102, clientName:具体任务执行---成功
25-09-02.21:30:03.891 [main            ] INFO  AiClientNode           - 注册Client---clientId:3103, clientName:质量检查和优化---成功
25-09-02.21:30:03.891 [main            ] INFO  AgentAutoLoadConfig    - 自动装配AI客户端完成
25-09-02.21:30:04.743 [HttpClient-13-Worker-2] INFO  McpAsyncClient         - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[completions=null, experimental={}, logging=null, prompts=PromptCapabilities[listChanged=false], resources=ResourceCapabilities[subscribe=false, listChanged=false], tools=ToolCapabilities[listChanged=false]], Info: Implementation[name=AIsearch, version=1.4.1] and Instructions null
25-09-02.21:31:20.315 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MainHikariPool - Shutdown initiated...
25-09-02.21:31:20.345 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MainHikariPool - Shutdown completed.
25-09-02.21:32:39.750 [main            ] INFO  AiAgentTest            - Starting AiAgentTest using Java 17.0.12 with PID 37796 (started by 30562 in D:\ACode\project\ai-agent\ai-agent-app)
25-09-02.21:32:39.751 [main            ] INFO  AiAgentTest            - The following 1 profile is active: "dev"
25-09-02.21:32:42.485 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_openai. Is empty: false
25-09-02.21:32:42.489 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_openai in schema: public
25-09-02.21:32:42.490 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-09-02.21:32:42.797 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-09-02.21:32:43.358 [main            ] INFO  AiAgentTest            - Started AiAgentTest in 4.231 seconds (process running for 5.589)
25-09-02.21:32:43.363 [main            ] INFO  AgentAutoLoadConfig    - 开始自动加载Agent
25-09-02.21:32:43.364 [main            ] INFO  AgentAutoLoadConfig    - 开始自动装配AI客户端，客户端ID列表: [3101, 3102, 3103, 3104]
25-09-02.21:32:43.367 [main            ] INFO  RootNode               - AIClient装配链---RootNode---开始查询配置信息
25-09-02.21:32:43.368 [pool-2-thread-1 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_api) [3101, 3102, 3103, 3104]
25-09-02.21:32:43.368 [pool-2-thread-2 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_model) [3101, 3102, 3103, 3104]
25-09-02.21:32:43.368 [pool-2-thread-3 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_tool_mcp) [3101, 3102, 3103, 3104]
25-09-02.21:32:43.368 [pool-2-thread-4 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_system_prompt) [3101, 3102, 3103, 3104]
25-09-02.21:32:43.368 [pool-2-thread-5 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_advisor) [3101, 3102, 3103, 3104]
25-09-02.21:32:43.370 [pool-2-thread-6 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client) [3101, 3102, 3103, 3104]
25-09-02.21:32:43.397 [pool-2-thread-3 ] INFO  HikariDataSource       - MainHikariPool - Starting...
25-09-02.21:32:43.659 [pool-2-thread-3 ] INFO  HikariPool             - MainHikariPool - Added connection com.mysql.cj.jdbc.ConnectionImpl@511eda7a
25-09-02.21:32:43.660 [pool-2-thread-3 ] INFO  HikariDataSource       - MainHikariPool - Start completed.
25-09-02.21:32:43.924 [main            ] INFO  RootNode               - AIClient装配链---RootNode---查询配置信息完毕
25-09-02.21:32:43.924 [main            ] INFO  AiClientApiNode        - AIClient装配链---AiClientApiNode---开始装配API
25-09-02.21:32:43.932 [main            ] INFO  AiClientMcpNode        - AIClient装配链---AiClientMcpNode---开始装配MCP
25-09-02.21:32:44.241 [HttpClient-10-Worker-0] INFO  McpAsyncClient         - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[completions=null, experimental={}, logging=null, prompts=PromptCapabilities[listChanged=false], resources=ResourceCapabilities[subscribe=false, listChanged=false], tools=ToolCapabilities[listChanged=false]], Info: Implementation[name=AIsearch, version=1.4.1] and Instructions null
25-09-02.21:32:44.317 [main            ] INFO  AiClientMcpNode        - sse方式注册mcp: baidu-search 成功
25-09-02.21:32:44.318 [main            ] INFO  AiClientModelNode      - AIClient装配链---AiClientModelNode---开始装配Model
25-09-02.21:32:44.318 [main            ] INFO  AiClientModelNode      - 注册模型-modelId:2001, modelName:deepseek-v3-成功
25-09-02.21:32:44.318 [main            ] INFO  AiClientAdvisorNode    - AIClient装配链---AiClientAdvisorNode---开始装配顾问
25-09-02.21:32:44.330 [main            ] INFO  AiClientAdvisorNode    - 注册顾问---advisorId:4001, advisorName:记忆---成功
25-09-02.21:32:44.330 [main            ] INFO  AiClientNode           - AIClient装配链---AiClientNode---开始装配Client
25-09-02.21:32:44.439 [main            ] INFO  AiClientNode           - 注册Client---clientId:3101, clientName:任务分析和状态判断---成功
25-09-02.21:32:44.517 [main            ] INFO  AiClientNode           - 注册Client---clientId:3102, clientName:具体任务执行---成功
25-09-02.21:32:44.593 [main            ] INFO  AiClientNode           - 注册Client---clientId:3103, clientName:质量检查和优化---成功
25-09-02.21:32:44.593 [main            ] INFO  AgentAutoLoadConfig    - 自动装配AI客户端完成
25-09-02.21:32:45.440 [HttpClient-13-Worker-0] INFO  McpAsyncClient         - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[completions=null, experimental={}, logging=null, prompts=PromptCapabilities[listChanged=false], resources=ResourceCapabilities[subscribe=false, listChanged=false], tools=ToolCapabilities[listChanged=false]], Info: Implementation[name=AIsearch, version=1.4.1] and Instructions null
25-09-02.21:34:04.896 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MainHikariPool - Shutdown initiated...
25-09-02.21:34:04.913 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MainHikariPool - Shutdown completed.
