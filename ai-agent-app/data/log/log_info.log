25-09-07.15:00:50.348 [main            ] INFO  StreamAutoAgentTest    - Starting StreamAutoAgentTest using Java 17.0.12 with PID 40844 (started by 30562 in D:\ACode\project\ai-agent\ai-agent-app)
25-09-07.15:00:50.351 [main            ] INFO  StreamAutoAgentTest    - The following 1 profile is active: "dev"
25-09-07.15:00:53.491 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_openai. Is empty: false
25-09-07.15:00:53.500 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_openai in schema: public
25-09-07.15:00:53.500 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-09-07.15:00:53.510 [main            ] WARN  GenericWebApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'aiAgentController': Injection of resource dependencies failed
25-09-07.15:00:53.570 [main            ] INFO  ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
25-09-07.15:00:53.599 [main            ] ERROR LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

A component required a bean of type 'com.flyxy.ai.domain.agent.service.exec.IExecuteStrategy' that could not be found.


Action:

Consider defining a bean of type 'com.flyxy.ai.domain.agent.service.exec.IExecuteStrategy' in your configuration.

25-09-07.15:00:53.606 [main            ] WARN  TestContextManager     - Caught exception while allowing TestExecutionListener [org.springframework.test.context.web.ServletTestExecutionListener] to prepare test instance [com.flyxy.ai.test.ai.StreamAutoAgentTest@755fd7de]
java.lang.IllegalStateException: Failed to load ApplicationContext for [WebMergedContextConfiguration@2bea52d9 testClass = com.flyxy.ai.test.ai.StreamAutoAgentTest, locations = [], classes = [com.flyxy.ai.Application], contextInitializerClasses = [], activeProfiles = [], propertySourceDescriptors = [], propertySourceProperties = ["org.springframework.boot.test.context.SpringBootTestContextBootstrapper=true"], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@59309333, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@6b81ce95, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.web.client.TestRestTemplateContextCustomizer@223d2c72, org.springframework.boot.test.web.reactive.server.WebTestClientContextCustomizer@158d2680, org.springframework.boot.test.web.reactor.netty.DisableReactorResourceFactoryGlobalResourcesContextCustomizerFactory$DisableReactorResourceFactoryGlobalResourcesContextCustomizerCustomizer@67c27493, org.springframework.boot.test.autoconfigure.OnFailureConditionReportContextCustomizerFactory$OnFailureConditionReportContextCustomizer@5b8dfcc1, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@1f, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizer@7b2bbc3, org.springframework.test.context.support.DynamicPropertiesContextCustomizer@0, org.springframework.boot.test.context.SpringBootTestAnnotation@2b8d28c9], resourceBasePath = "src/main/webapp", contextLoader = org.springframework.boot.test.context.SpringBootContextLoader, parent = null]
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:180)
	at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
	at org.springframework.test.context.web.ServletTestExecutionListener.setUpRequestContextIfNecessary(ServletTestExecutionListener.java:200)
	at org.springframework.test.context.web.ServletTestExecutionListener.prepareTestInstance(ServletTestExecutionListener.java:139)
	at org.springframework.test.context.TestContextManager.prepareTestInstance(TestContextManager.java:260)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.createTest(SpringJUnit4ClassRunner.java:228)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner$1.runReflectiveCall(SpringJUnit4ClassRunner.java:289)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.methodBlock(SpringJUnit4ClassRunner.java:291)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:247)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:97)
	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at org.springframework.test.context.junit4.statements.RunBeforeTestClassCallbacks.evaluate(RunBeforeTestClassCallbacks.java:61)
	at org.springframework.test.context.junit4.statements.RunAfterTestClassCallbacks.evaluate(RunAfterTestClassCallbacks.java:70)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.run(SpringJUnit4ClassRunner.java:191)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
	at com.intellij.junit4.JUnit4IdeaTestRunner.startRunnerWithArgs(JUnit4IdeaTestRunner.java:69)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater$1.execute(IdeaTestRunner.java:38)
	at com.intellij.rt.execution.junit.TestsRepeater.repeat(TestsRepeater.java:11)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:35)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:231)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:55)
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'aiAgentController': Injection of resource dependencies failed
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:372)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1445)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:600)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:346)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.instantiateSingleton(DefaultListableBeanFactory.java:1155)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingleton(DefaultListableBeanFactory.java:1121)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:1056)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:987)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.springframework.boot.test.context.SpringBootContextLoader.lambda$loadContext$3(SpringBootContextLoader.java:137)
	at org.springframework.util.function.ThrowingSupplier.get(ThrowingSupplier.java:58)
	at org.springframework.util.function.ThrowingSupplier.get(ThrowingSupplier.java:46)
	at org.springframework.boot.SpringApplication.withHook(SpringApplication.java:1461)
	at org.springframework.boot.test.context.SpringBootContextLoader$ContextLoaderHook.run(SpringBootContextLoader.java:553)
	at org.springframework.boot.test.context.SpringBootContextLoader.loadContext(SpringBootContextLoader.java:137)
	at org.springframework.boot.test.context.SpringBootContextLoader.loadContext(SpringBootContextLoader.java:108)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContextInternal(DefaultCacheAwareContextLoaderDelegate.java:225)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:152)
	... 27 common frames omitted
Caused by: org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'com.flyxy.ai.domain.agent.service.exec.IExecuteStrategy' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {@jakarta.annotation.Resource(shareable=true, lookup="", name="", description="", authenticationType=CONTAINER, type=java.lang.Object.class, mappedName="")}
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.raiseNoMatchingBeanFound(DefaultListableBeanFactory.java:2177)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1627)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1552)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:599)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:577)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:739)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:272)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:369)
	... 51 common frames omitted
25-09-07.15:02:07.369 [main            ] INFO  StreamAutoAgentTest    - Starting StreamAutoAgentTest using Java 17.0.12 with PID 30676 (started by 30562 in D:\ACode\project\ai-agent\ai-agent-app)
25-09-07.15:02:07.372 [main            ] INFO  StreamAutoAgentTest    - The following 1 profile is active: "dev"
25-09-07.15:02:10.031 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_openai. Is empty: false
25-09-07.15:02:10.037 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_openai in schema: public
25-09-07.15:02:10.038 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-09-07.15:02:10.368 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-09-07.15:02:10.915 [main            ] INFO  StreamAutoAgentTest    - Started StreamAutoAgentTest in 4.074 seconds (process running for 5.331)
25-09-07.15:02:10.921 [main            ] INFO  AgentAutoLoadConfig    - 开始自动加载Agent
25-09-07.15:02:10.921 [main            ] INFO  AgentAutoLoadConfig    - 开始自动装配AI客户端，客户端ID列表: [3101, 3102, 3103, 3104]
25-09-07.15:02:10.925 [main            ] INFO  RootNode               - AIClient装配链---RootNode---开始查询配置信息
25-09-07.15:02:10.927 [pool-2-thread-1 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_api) [3101, 3102, 3103, 3104]
25-09-07.15:02:10.928 [pool-2-thread-2 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_model) [3101, 3102, 3103, 3104]
25-09-07.15:02:10.928 [pool-2-thread-3 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_tool_mcp) [3101, 3102, 3103, 3104]
25-09-07.15:02:10.928 [pool-2-thread-4 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_system_prompt) [3101, 3102, 3103, 3104]
25-09-07.15:02:10.928 [pool-2-thread-5 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_advisor) [3101, 3102, 3103, 3104]
25-09-07.15:02:10.929 [pool-2-thread-6 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client) [3101, 3102, 3103, 3104]
25-09-07.15:02:10.961 [pool-2-thread-1 ] INFO  HikariDataSource       - MainHikariPool - Starting...
25-09-07.15:05:30.735 [main            ] INFO  StreamAutoAgentTest    - Starting StreamAutoAgentTest using Java 17.0.12 with PID 16396 (started by 30562 in D:\ACode\project\ai-agent\ai-agent-app)
25-09-07.15:05:30.736 [main            ] INFO  StreamAutoAgentTest    - The following 1 profile is active: "dev"
25-09-07.15:05:33.413 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_openai. Is empty: false
25-09-07.15:05:33.417 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_openai in schema: public
25-09-07.15:05:33.417 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-09-07.15:05:33.725 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-09-07.15:05:34.248 [main            ] INFO  StreamAutoAgentTest    - Started StreamAutoAgentTest in 4.071 seconds (process running for 5.328)
25-09-07.15:05:34.253 [main            ] INFO  AgentAutoLoadConfig    - 开始自动加载Agent
25-09-07.15:05:34.253 [main            ] INFO  AgentAutoLoadConfig    - 开始自动装配AI客户端，客户端ID列表: [3101, 3102, 3103, 3104]
25-09-07.15:05:34.256 [main            ] INFO  RootNode               - AIClient装配链---RootNode---开始查询配置信息
25-09-07.15:05:34.257 [pool-2-thread-1 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_api) [3101, 3102, 3103, 3104]
25-09-07.15:05:34.257 [pool-2-thread-2 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_model) [3101, 3102, 3103, 3104]
25-09-07.15:05:34.257 [pool-2-thread-3 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_tool_mcp) [3101, 3102, 3103, 3104]
25-09-07.15:05:34.272 [pool-2-thread-4 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_system_prompt) [3101, 3102, 3103, 3104]
25-09-07.15:05:34.272 [pool-2-thread-5 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_advisor) [3101, 3102, 3103, 3104]
25-09-07.15:05:34.273 [pool-2-thread-6 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client) [3101, 3102, 3103, 3104]
25-09-07.15:05:34.295 [pool-2-thread-3 ] INFO  HikariDataSource       - MainHikariPool - Starting...
25-09-07.15:05:34.600 [pool-2-thread-3 ] INFO  HikariPool             - MainHikariPool - Added connection com.mysql.cj.jdbc.ConnectionImpl@3dbaafee
25-09-07.15:05:34.601 [pool-2-thread-3 ] INFO  HikariDataSource       - MainHikariPool - Start completed.
25-09-07.15:05:34.881 [main            ] INFO  RootNode               - AIClient装配链---RootNode---查询配置信息完毕
25-09-07.15:05:34.881 [main            ] INFO  AiClientApiNode        - AIClient装配链---AiClientApiNode---开始装配API
25-09-07.15:05:34.888 [main            ] INFO  AiClientMcpNode        - AIClient装配链---AiClientMcpNode---开始装配MCP
25-09-07.15:05:34.962 [ForkJoinPool.commonPool-worker-1] ERROR HttpClientSseClientTransport - SSE connection error
java.util.concurrent.CompletionException: java.net.ConnectException
	at java.base/java.util.concurrent.CompletableFuture.encodeRelay(CompletableFuture.java:368)
	at java.base/java.util.concurrent.CompletableFuture.uniComposeStage(CompletableFuture.java:1189)
	at java.base/java.util.concurrent.CompletableFuture.thenCompose(CompletableFuture.java:2309)
	at java.net.http/jdk.internal.net.http.MultiExchange.responseAsyncImpl(MultiExchange.java:453)
	at java.net.http/jdk.internal.net.http.MultiExchange.lambda$responseAsyncImpl$7(MultiExchange.java:449)
	at java.base/java.util.concurrent.CompletableFuture.uniHandle(CompletableFuture.java:934)
	at java.base/java.util.concurrent.CompletableFuture.uniHandleStage(CompletableFuture.java:950)
	at java.base/java.util.concurrent.CompletableFuture.handle(CompletableFuture.java:2340)
	at java.net.http/jdk.internal.net.http.MultiExchange.responseAsyncImpl(MultiExchange.java:439)
	at java.net.http/jdk.internal.net.http.MultiExchange.lambda$responseAsync0$2(MultiExchange.java:341)
	at java.base/java.util.concurrent.CompletableFuture$UniCompose.tryFire(CompletableFuture.java:1150)
	at java.base/java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:510)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run$$$capture(CompletableFuture.java:1773)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.net.ConnectException: null
	at java.net.http/jdk.internal.net.http.common.Utils.toConnectException(Utils.java:1055)
	at java.net.http/jdk.internal.net.http.PlainHttpConnection.connectAsync(PlainHttpConnection.java:198)
	at java.net.http/jdk.internal.net.http.Http1Exchange.sendHeadersAsync(Http1Exchange.java:239)
	at java.net.http/jdk.internal.net.http.Exchange.lambda$responseAsyncImpl0$9(Exchange.java:525)
	at java.net.http/jdk.internal.net.http.Exchange.checkFor407(Exchange.java:405)
	at java.net.http/jdk.internal.net.http.Exchange.lambda$responseAsyncImpl0$10(Exchange.java:529)
	at java.base/java.util.concurrent.CompletableFuture.uniHandle(CompletableFuture.java:934)
	at java.base/java.util.concurrent.CompletableFuture.uniHandleStage(CompletableFuture.java:950)
	at java.base/java.util.concurrent.CompletableFuture.handle(CompletableFuture.java:2340)
	at java.net.http/jdk.internal.net.http.Exchange.responseAsyncImpl0(Exchange.java:529)
	at java.net.http/jdk.internal.net.http.Exchange.responseAsyncImpl(Exchange.java:381)
	at java.net.http/jdk.internal.net.http.Exchange.responseAsync(Exchange.java:373)
	at java.net.http/jdk.internal.net.http.MultiExchange.responseAsyncImpl(MultiExchange.java:408)
	... 13 common frames omitted
Caused by: java.nio.channels.UnresolvedAddressException: null
	at java.base/sun.nio.ch.Net.checkAddress(Net.java:149)
	at java.base/sun.nio.ch.Net.checkAddress(Net.java:157)
	at java.base/sun.nio.ch.SocketChannelImpl.checkRemote(SocketChannelImpl.java:816)
	at java.base/sun.nio.ch.SocketChannelImpl.connect(SocketChannelImpl.java:839)
	at java.net.http/jdk.internal.net.http.PlainHttpConnection.lambda$connectAsync$0(PlainHttpConnection.java:183)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:569)
	at java.net.http/jdk.internal.net.http.PlainHttpConnection.connectAsync(PlainHttpConnection.java:185)
	... 24 common frames omitted
25-09-07.15:05:34.968 [ForkJoinPool.commonPool-worker-1] ERROR Operators              - Operator called default onErrorDropped
reactor.core.Exceptions$ErrorCallbackNotImplemented: java.net.ConnectException
Caused by: java.net.ConnectException: null
	at java.net.http/jdk.internal.net.http.common.Utils.toConnectException(Utils.java:1055)
	at java.net.http/jdk.internal.net.http.PlainHttpConnection.connectAsync(PlainHttpConnection.java:198)
	at java.net.http/jdk.internal.net.http.Http1Exchange.sendHeadersAsync(Http1Exchange.java:239)
	at java.net.http/jdk.internal.net.http.Exchange.lambda$responseAsyncImpl0$9(Exchange.java:525)
	at java.net.http/jdk.internal.net.http.Exchange.checkFor407(Exchange.java:405)
	at java.net.http/jdk.internal.net.http.Exchange.lambda$responseAsyncImpl0$10(Exchange.java:529)
	at java.base/java.util.concurrent.CompletableFuture.uniHandle(CompletableFuture.java:934)
	at java.base/java.util.concurrent.CompletableFuture.uniHandleStage(CompletableFuture.java:950)
	at java.base/java.util.concurrent.CompletableFuture.handle(CompletableFuture.java:2340)
	at java.net.http/jdk.internal.net.http.Exchange.responseAsyncImpl0(Exchange.java:529)
	at java.net.http/jdk.internal.net.http.Exchange.responseAsyncImpl(Exchange.java:381)
	at java.net.http/jdk.internal.net.http.Exchange.responseAsync(Exchange.java:373)
	at java.net.http/jdk.internal.net.http.MultiExchange.responseAsyncImpl(MultiExchange.java:408)
	at java.net.http/jdk.internal.net.http.MultiExchange.lambda$responseAsyncImpl$7(MultiExchange.java:449)
	at java.base/java.util.concurrent.CompletableFuture.uniHandle(CompletableFuture.java:934)
	at java.base/java.util.concurrent.CompletableFuture.uniHandleStage(CompletableFuture.java:950)
	at java.base/java.util.concurrent.CompletableFuture.handle(CompletableFuture.java:2340)
	at java.net.http/jdk.internal.net.http.MultiExchange.responseAsyncImpl(MultiExchange.java:439)
	at java.net.http/jdk.internal.net.http.MultiExchange.lambda$responseAsync0$2(MultiExchange.java:341)
	at java.base/java.util.concurrent.CompletableFuture$UniCompose.tryFire(CompletableFuture.java:1150)
	at java.base/java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:510)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run$$$capture(CompletableFuture.java:1773)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.nio.channels.UnresolvedAddressException: null
	at java.base/sun.nio.ch.Net.checkAddress(Net.java:149)
	at java.base/sun.nio.ch.Net.checkAddress(Net.java:157)
	at java.base/sun.nio.ch.SocketChannelImpl.checkRemote(SocketChannelImpl.java:816)
	at java.base/sun.nio.ch.SocketChannelImpl.connect(SocketChannelImpl.java:839)
	at java.net.http/jdk.internal.net.http.PlainHttpConnection.lambda$connectAsync$0(PlainHttpConnection.java:183)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:569)
	at java.net.http/jdk.internal.net.http.PlainHttpConnection.connectAsync(PlainHttpConnection.java:185)
	... 24 common frames omitted
25-09-07.15:05:44.971 [main            ] ERROR AgentAutoLoadConfig    - 自动加载Agent异常
io.modelcontextprotocol.spec.McpError: Failed to wait for the message endpoint
	at io.modelcontextprotocol.client.transport.HttpClientSseClientTransport.sendMessage(HttpClientSseClientTransport.java:403)
	at io.modelcontextprotocol.spec.McpClientSession.lambda$sendRequest$9(McpClientSession.java:237)
	at reactor.core.publisher.MonoCreate.subscribe(MonoCreate.java:61)
	at reactor.core.publisher.MonoDeferContextual.subscribe(MonoDeferContextual.java:55)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.Mono.block(Mono.java:1778)
	at io.modelcontextprotocol.client.McpSyncClient.initialize(McpSyncClient.java:171)
	at com.flyxy.ai.domain.agent.service.armory.AiClientMcpNode.createMcpSyncClient(AiClientMcpNode.java:75)
	at com.flyxy.ai.domain.agent.service.armory.AiClientMcpNode.doApply(AiClientMcpNode.java:39)
	at com.flyxy.ai.domain.agent.service.armory.AiClientMcpNode.doApply(AiClientMcpNode.java:25)
	at cn.bugstack.wrench.design.framework.tree.AbstractMultiThreadStrategyRouter.apply(AbstractMultiThreadStrategyRouter.java:31)
	at cn.bugstack.wrench.design.framework.tree.AbstractMultiThreadStrategyRouter.router(AbstractMultiThreadStrategyRouter.java:22)
	at com.flyxy.ai.domain.agent.service.armory.AiClientApiNode.doApply(AiClientApiNode.java:47)
	at com.flyxy.ai.domain.agent.service.armory.AiClientApiNode.doApply(AiClientApiNode.java:20)
	at cn.bugstack.wrench.design.framework.tree.AbstractMultiThreadStrategyRouter.apply(AbstractMultiThreadStrategyRouter.java:31)
	at cn.bugstack.wrench.design.framework.tree.AbstractMultiThreadStrategyRouter.router(AbstractMultiThreadStrategyRouter.java:22)
	at com.flyxy.ai.domain.agent.service.armory.RootNode.doApply(RootNode.java:44)
	at com.flyxy.ai.domain.agent.service.armory.RootNode.doApply(RootNode.java:20)
	at cn.bugstack.wrench.design.framework.tree.AbstractMultiThreadStrategyRouter.apply(AbstractMultiThreadStrategyRouter.java:31)
	at com.flyxy.ai.config.AgentAutoLoadConfig.run(AgentAutoLoadConfig.java:39)
	at org.springframework.boot.SpringApplication.lambda$callRunner$5(SpringApplication.java:788)
	at org.springframework.util.function.ThrowingConsumer$1.acceptWithException(ThrowingConsumer.java:82)
	at org.springframework.util.function.ThrowingConsumer.accept(ThrowingConsumer.java:60)
	at org.springframework.util.function.ThrowingConsumer$1.accept(ThrowingConsumer.java:86)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:796)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:787)
	at org.springframework.boot.SpringApplication.lambda$callRunners$3(SpringApplication.java:772)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:183)
	at java.base/java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:357)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:510)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:150)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:173)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:596)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:772)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:325)
	at org.springframework.boot.test.context.SpringBootContextLoader.lambda$loadContext$3(SpringBootContextLoader.java:137)
	at org.springframework.util.function.ThrowingSupplier.get(ThrowingSupplier.java:58)
	at org.springframework.util.function.ThrowingSupplier.get(ThrowingSupplier.java:46)
	at org.springframework.boot.SpringApplication.withHook(SpringApplication.java:1461)
	at org.springframework.boot.test.context.SpringBootContextLoader$ContextLoaderHook.run(SpringBootContextLoader.java:553)
	at org.springframework.boot.test.context.SpringBootContextLoader.loadContext(SpringBootContextLoader.java:137)
	at org.springframework.boot.test.context.SpringBootContextLoader.loadContext(SpringBootContextLoader.java:108)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContextInternal(DefaultCacheAwareContextLoaderDelegate.java:225)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:152)
	at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
	at org.springframework.test.context.web.ServletTestExecutionListener.setUpRequestContextIfNecessary(ServletTestExecutionListener.java:200)
	at org.springframework.test.context.web.ServletTestExecutionListener.prepareTestInstance(ServletTestExecutionListener.java:139)
	at org.springframework.test.context.TestContextManager.prepareTestInstance(TestContextManager.java:260)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.createTest(SpringJUnit4ClassRunner.java:228)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner$1.runReflectiveCall(SpringJUnit4ClassRunner.java:289)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.methodBlock(SpringJUnit4ClassRunner.java:291)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:247)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:97)
	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at org.springframework.test.context.junit4.statements.RunBeforeTestClassCallbacks.evaluate(RunBeforeTestClassCallbacks.java:61)
	at org.springframework.test.context.junit4.statements.RunAfterTestClassCallbacks.evaluate(RunAfterTestClassCallbacks.java:70)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.run(SpringJUnit4ClassRunner.java:191)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
	at com.intellij.junit4.JUnit4IdeaTestRunner.startRunnerWithArgs(JUnit4IdeaTestRunner.java:69)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater$1.execute(IdeaTestRunner.java:38)
	at com.intellij.rt.execution.junit.TestsRepeater.repeat(TestsRepeater.java:11)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:35)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:231)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:55)
	Suppressed: java.lang.Exception: #block terminated with an error
		at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:104)
		at reactor.core.publisher.Mono.block(Mono.java:1779)
		... 67 common frames omitted
25-09-07.15:05:45.764 [main            ] INFO  RootNode               - AIClient装配链---RootNode---开始查询配置信息
25-09-07.15:05:45.765 [pool-2-thread-8 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_model) [3101, 3102, 3103]
25-09-07.15:05:45.765 [pool-2-thread-7 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_api) [3101, 3102, 3103]
25-09-07.15:05:45.765 [pool-2-thread-9 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_tool_mcp) [3101, 3102, 3103]
25-09-07.15:05:45.765 [pool-2-thread-10] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_system_prompt) [3101, 3102, 3103]
25-09-07.15:05:45.765 [pool-2-thread-11] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_advisor) [3101, 3102, 3103]
25-09-07.15:05:45.766 [pool-2-thread-12] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client) [3101, 3102, 3103]
25-09-07.15:05:45.778 [main            ] INFO  RootNode               - AIClient装配链---RootNode---查询配置信息完毕
25-09-07.15:05:45.778 [main            ] INFO  AiClientApiNode        - AIClient装配链---AiClientApiNode---开始装配API
25-09-07.15:05:45.785 [main            ] INFO  AiClientMcpNode        - AIClient装配链---AiClientMcpNode---开始装配MCP
25-09-07.15:05:46.222 [HttpClient-13-Worker-2] INFO  McpAsyncClient         - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[completions=null, experimental={}, logging=null, prompts=PromptCapabilities[listChanged=false], resources=ResourceCapabilities[subscribe=false, listChanged=false], tools=ToolCapabilities[listChanged=false]], Info: Implementation[name=AIsearch, version=1.4.1] and Instructions null
25-09-07.15:05:46.294 [main            ] INFO  AiClientMcpNode        - sse方式注册mcp: baidu-search 成功
25-09-07.15:05:46.294 [main            ] INFO  AiClientModelNode      - AIClient装配链---AiClientModelNode---开始装配Model
25-09-07.15:05:46.295 [main            ] INFO  AiClientModelNode      - 注册模型-modelId:2001, modelName:deepseek-v3-成功
25-09-07.15:05:46.295 [main            ] INFO  AiClientAdvisorNode    - AIClient装配链---AiClientAdvisorNode---开始装配顾问
25-09-07.15:05:46.309 [main            ] INFO  AiClientAdvisorNode    - 注册顾问---advisorId:4001, advisorName:记忆---成功
25-09-07.15:05:46.309 [main            ] INFO  AiClientNode           - AIClient装配链---AiClientNode---开始装配Client
25-09-07.15:05:46.427 [main            ] INFO  AiClientNode           - 注册Client---clientId:3101, clientName:任务分析和状态判断---成功
25-09-07.15:05:46.505 [main            ] INFO  AiClientNode           - 注册Client---clientId:3102, clientName:具体任务执行---成功
25-09-07.15:05:46.597 [main            ] INFO  AiClientNode           - 注册Client---clientId:3103, clientName:质量检查和优化---成功
25-09-07.15:05:46.597 [main            ] INFO  StreamAutoAgentTest    - 客户端构建:org.springframework.ai.chat.client.DefaultChatClient@6feb27ea
25-09-07.15:05:46.599 [main            ] INFO  StreamAutoAgentTest    - 开始测试流式AutoAgent
25-09-07.15:05:46.609 [main            ] INFO  AutoExecuteStrategy    - 开始执行AutoAgent流式任务，会话ID: stream-session-1757228746599
25-09-07.15:05:46.609 [main            ] INFO  StreamAutoAgentTest    - 收到流式内容: 🚀 === AutoAgent执行开始 ===

25-09-07.15:05:46.611 [main            ] ERROR AgentRepository        - Query ai agent client flow config failed, aiAgentId: 3
org.mybatis.spring.MyBatisSystemException: 
### Error querying database.  Cause: org.apache.ibatis.type.TypeException: Could not set parameters for mapping: ParameterMapping{property='agentId', mode=IN, javaType=class java.lang.Long, jdbcType=null, numericScale=null, resultMapId='null', jdbcTypeName='null', expression='null'}. Cause: org.apache.ibatis.type.TypeException: Error setting non null for parameter #1 with JdbcType null . Try setting a different JdbcType for this parameter or a different configuration property. Cause: java.lang.ClassCastException: class java.lang.String cannot be cast to class java.lang.Long (java.lang.String and java.lang.Long are in module java.base of loader 'bootstrap')
### The error may exist in file [D:\ACode\project\ai-agent\ai-agent-app\target\classes\mybatis\mapper\ai_agent_flow_config_mapper.xml]
### The error may involve com.flyxy.ai.infrastructure.dao.IAiAgentFlowConfigDao.queryByAgentId-Inline
### The error occurred while setting parameters
### SQL: SELECT id, agent_id, client_id, client_name, client_type, sequence, create_time         FROM ai_agent_flow_config         WHERE agent_id = ?         ORDER BY sequence
### Cause: org.apache.ibatis.type.TypeException: Could not set parameters for mapping: ParameterMapping{property='agentId', mode=IN, javaType=class java.lang.Long, jdbcType=null, numericScale=null, resultMapId='null', jdbcTypeName='null', expression='null'}. Cause: org.apache.ibatis.type.TypeException: Error setting non null for parameter #1 with JdbcType null . Try setting a different JdbcType for this parameter or a different configuration property. Cause: java.lang.ClassCastException: class java.lang.String cannot be cast to class java.lang.Long (java.lang.String and java.lang.Long are in module java.base of loader 'bootstrap')
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:99)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:347)
	at jdk.proxy2/jdk.proxy2.$Proxy79.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:194)
	at org.apache.ibatis.binding.MapperMethod.executeForMany(MapperMethod.java:147)
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:80)
	at org.apache.ibatis.binding.MapperProxy$PlainMethodInvoker.invoke(MapperProxy.java:141)
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:86)
	at jdk.proxy2/jdk.proxy2.$Proxy81.queryByAgentId(Unknown Source)
	at com.flyxy.ai.infrastructure.adapter.repository.AgentRepository.queryAiAgentClientFlowConfig(AgentRepository.java:472)
	at com.flyxy.ai.domain.agent.service.exec.auto.step.RootNode.multiThread(RootNode.java:32)
	at com.flyxy.ai.domain.agent.service.exec.auto.step.RootNode.multiThread(RootNode.java:20)
	at cn.bugstack.wrench.design.framework.tree.AbstractMultiThreadStrategyRouter.apply(AbstractMultiThreadStrategyRouter.java:29)
	at com.flyxy.ai.domain.agent.service.exec.auto.AutoExecuteStrategy.execute(AutoExecuteStrategy.java:56)
	at com.flyxy.ai.test.ai.StreamAutoAgentTest.lambda$testStreamAutoAgent$0(StreamAutoAgentTest.java:76)
	at reactor.core.publisher.FluxCreate.subscribe(FluxCreate.java:97)
	at reactor.core.publisher.Flux.subscribe(Flux.java:8891)
	at reactor.core.publisher.Flux.subscribeWith(Flux.java:9012)
	at reactor.core.publisher.Flux.subscribe(Flux.java:8856)
	at reactor.core.publisher.Flux.subscribe(Flux.java:8780)
	at reactor.core.publisher.Flux.subscribe(Flux.java:8698)
	at com.flyxy.ai.test.ai.StreamAutoAgentTest.testStreamAutoAgent(StreamAutoAgentTest.java:97)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.springframework.test.context.junit4.statements.RunBeforeTestExecutionCallbacks.evaluate(RunBeforeTestExecutionCallbacks.java:76)
	at org.springframework.test.context.junit4.statements.RunAfterTestExecutionCallbacks.evaluate(RunAfterTestExecutionCallbacks.java:84)
	at org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:26)
	at org.springframework.test.context.junit4.statements.RunBeforeTestMethodCallbacks.evaluate(RunBeforeTestMethodCallbacks.java:75)
	at org.springframework.test.context.junit4.statements.RunAfterTestMethodCallbacks.evaluate(RunAfterTestMethodCallbacks.java:86)
	at org.springframework.test.context.junit4.statements.SpringRepeat.evaluate(SpringRepeat.java:84)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:252)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:97)
	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at org.springframework.test.context.junit4.statements.RunBeforeTestClassCallbacks.evaluate(RunBeforeTestClassCallbacks.java:61)
	at org.springframework.test.context.junit4.statements.RunAfterTestClassCallbacks.evaluate(RunAfterTestClassCallbacks.java:70)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.run(SpringJUnit4ClassRunner.java:191)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
	at com.intellij.junit4.JUnit4IdeaTestRunner.startRunnerWithArgs(JUnit4IdeaTestRunner.java:69)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater$1.execute(IdeaTestRunner.java:38)
	at com.intellij.rt.execution.junit.TestsRepeater.repeat(TestsRepeater.java:11)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:35)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:231)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:55)
Caused by: org.apache.ibatis.type.TypeException: Could not set parameters for mapping: ParameterMapping{property='agentId', mode=IN, javaType=class java.lang.Long, jdbcType=null, numericScale=null, resultMapId='null', jdbcTypeName='null', expression='null'}. Cause: org.apache.ibatis.type.TypeException: Error setting non null for parameter #1 with JdbcType null . Try setting a different JdbcType for this parameter or a different configuration property. Cause: java.lang.ClassCastException: class java.lang.String cannot be cast to class java.lang.Long (java.lang.String and java.lang.Long are in module java.base of loader 'bootstrap')
	at org.apache.ibatis.scripting.defaults.DefaultParameterHandler.setParameters(DefaultParameterHandler.java:92)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.parameterize(PreparedStatementHandler.java:97)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.parameterize(RoutingStatementHandler.java:65)
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:91)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:64)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:336)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:158)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:110)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:90)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142)
	at jdk.internal.reflect.GeneratedMethodAccessor17.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:333)
	... 54 common frames omitted
Caused by: org.apache.ibatis.type.TypeException: Error setting non null for parameter #1 with JdbcType null . Try setting a different JdbcType for this parameter or a different configuration property. Cause: java.lang.ClassCastException: class java.lang.String cannot be cast to class java.lang.Long (java.lang.String and java.lang.Long are in module java.base of loader 'bootstrap')
	at org.apache.ibatis.type.BaseTypeHandler.setParameter(BaseTypeHandler.java:76)
	at org.apache.ibatis.scripting.defaults.DefaultParameterHandler.setParameters(DefaultParameterHandler.java:90)
	... 69 common frames omitted
Caused by: java.lang.ClassCastException: class java.lang.String cannot be cast to class java.lang.Long (java.lang.String and java.lang.Long are in module java.base of loader 'bootstrap')
	at org.apache.ibatis.type.LongTypeHandler.setNonNullParameter(LongTypeHandler.java:26)
	at org.apache.ibatis.type.BaseTypeHandler.setParameter(BaseTypeHandler.java:74)
	... 70 common frames omitted
25-09-07.15:05:46.612 [main            ] INFO  RootNode               - === 动态多轮执行测试开始 ====
25-09-07.15:05:46.612 [main            ] INFO  RootNode               - 用户输入: 请帮我分析一下人工智能的发展趋势，并给出学习建议
25-09-07.15:05:46.612 [main            ] INFO  RootNode               - 最大执行步数: 2
25-09-07.15:05:46.612 [main            ] INFO  RootNode               - 会话ID: stream-session-1757228746599
25-09-07.15:05:46.612 [main            ] INFO  StreamAutoAgentTest    - 收到流式内容: 📋 === 动态多轮执行开始 ===
👤 用户输入: 请帮我分析一下人工智能的发展趋势，并给出学习建议
🔢 最大执行步数: 2
🆔 会话ID: stream-session-1757228746599


25-09-07.15:05:46.613 [main            ] INFO  Step1TaskAnalyzerClient - === 动态多轮执行第1步 Step1 ====
25-09-07.15:05:46.613 [main            ] INFO  StreamAutoAgentTest    - 收到流式内容: 
🚀 === 开始执行 Step1-任务分析 (第1步) ===

25-09-07.15:05:46.613 [main            ] INFO  Step1TaskAnalyzerClient - 
📊 阶段1: 任务状态分析
25-09-07.15:05:46.615 [main            ] ERROR AutoExecuteStrategy    - AutoAgent流式任务执行异常，会话ID: stream-session-1757228746599
java.lang.NullPointerException: Cannot invoke "com.flyxy.ai.domain.agent.model.valobj.AiAgentClientFlowConfigVO.getClientId()" because "aiAgentClientFlowConfigVO" is null
	at com.flyxy.ai.domain.agent.service.exec.auto.step.Step1TaskAnalyzerClient.doApply(Step1TaskAnalyzerClient.java:59)
	at com.flyxy.ai.domain.agent.service.exec.auto.step.Step1TaskAnalyzerClient.doApply(Step1TaskAnalyzerClient.java:25)
	at cn.bugstack.wrench.design.framework.tree.AbstractMultiThreadStrategyRouter.apply(AbstractMultiThreadStrategyRouter.java:31)
	at cn.bugstack.wrench.design.framework.tree.AbstractMultiThreadStrategyRouter.router(AbstractMultiThreadStrategyRouter.java:22)
	at com.flyxy.ai.domain.agent.service.exec.auto.step.RootNode.doApply(RootNode.java:62)
	at com.flyxy.ai.domain.agent.service.exec.auto.step.RootNode.doApply(RootNode.java:20)
	at cn.bugstack.wrench.design.framework.tree.AbstractMultiThreadStrategyRouter.apply(AbstractMultiThreadStrategyRouter.java:31)
	at com.flyxy.ai.domain.agent.service.exec.auto.AutoExecuteStrategy.execute(AutoExecuteStrategy.java:56)
	at com.flyxy.ai.test.ai.StreamAutoAgentTest.lambda$testStreamAutoAgent$0(StreamAutoAgentTest.java:76)
	at reactor.core.publisher.FluxCreate.subscribe(FluxCreate.java:97)
	at reactor.core.publisher.Flux.subscribe(Flux.java:8891)
	at reactor.core.publisher.Flux.subscribeWith(Flux.java:9012)
	at reactor.core.publisher.Flux.subscribe(Flux.java:8856)
	at reactor.core.publisher.Flux.subscribe(Flux.java:8780)
	at reactor.core.publisher.Flux.subscribe(Flux.java:8698)
	at com.flyxy.ai.test.ai.StreamAutoAgentTest.testStreamAutoAgent(StreamAutoAgentTest.java:97)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.springframework.test.context.junit4.statements.RunBeforeTestExecutionCallbacks.evaluate(RunBeforeTestExecutionCallbacks.java:76)
	at org.springframework.test.context.junit4.statements.RunAfterTestExecutionCallbacks.evaluate(RunAfterTestExecutionCallbacks.java:84)
	at org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:26)
	at org.springframework.test.context.junit4.statements.RunBeforeTestMethodCallbacks.evaluate(RunBeforeTestMethodCallbacks.java:75)
	at org.springframework.test.context.junit4.statements.RunAfterTestMethodCallbacks.evaluate(RunAfterTestMethodCallbacks.java:86)
	at org.springframework.test.context.junit4.statements.SpringRepeat.evaluate(SpringRepeat.java:84)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:252)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:97)
	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at org.springframework.test.context.junit4.statements.RunBeforeTestClassCallbacks.evaluate(RunBeforeTestClassCallbacks.java:61)
	at org.springframework.test.context.junit4.statements.RunAfterTestClassCallbacks.evaluate(RunAfterTestClassCallbacks.java:70)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.run(SpringJUnit4ClassRunner.java:191)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
	at com.intellij.junit4.JUnit4IdeaTestRunner.startRunnerWithArgs(JUnit4IdeaTestRunner.java:69)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater$1.execute(IdeaTestRunner.java:38)
	at com.intellij.rt.execution.junit.TestsRepeater.repeat(TestsRepeater.java:11)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:35)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:231)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:55)
25-09-07.15:05:46.616 [main            ] ERROR StreamAutoAgentTest    - 流式执行出错
java.lang.NullPointerException: Cannot invoke "com.flyxy.ai.domain.agent.model.valobj.AiAgentClientFlowConfigVO.getClientId()" because "aiAgentClientFlowConfigVO" is null
	at com.flyxy.ai.domain.agent.service.exec.auto.step.Step1TaskAnalyzerClient.doApply(Step1TaskAnalyzerClient.java:59)
	at com.flyxy.ai.domain.agent.service.exec.auto.step.Step1TaskAnalyzerClient.doApply(Step1TaskAnalyzerClient.java:25)
	at cn.bugstack.wrench.design.framework.tree.AbstractMultiThreadStrategyRouter.apply(AbstractMultiThreadStrategyRouter.java:31)
	at cn.bugstack.wrench.design.framework.tree.AbstractMultiThreadStrategyRouter.router(AbstractMultiThreadStrategyRouter.java:22)
	at com.flyxy.ai.domain.agent.service.exec.auto.step.RootNode.doApply(RootNode.java:62)
	at com.flyxy.ai.domain.agent.service.exec.auto.step.RootNode.doApply(RootNode.java:20)
	at cn.bugstack.wrench.design.framework.tree.AbstractMultiThreadStrategyRouter.apply(AbstractMultiThreadStrategyRouter.java:31)
	at com.flyxy.ai.domain.agent.service.exec.auto.AutoExecuteStrategy.execute(AutoExecuteStrategy.java:56)
	at com.flyxy.ai.test.ai.StreamAutoAgentTest.lambda$testStreamAutoAgent$0(StreamAutoAgentTest.java:76)
	at reactor.core.publisher.FluxCreate.subscribe(FluxCreate.java:97)
	at reactor.core.publisher.Flux.subscribe(Flux.java:8891)
	at reactor.core.publisher.Flux.subscribeWith(Flux.java:9012)
	at reactor.core.publisher.Flux.subscribe(Flux.java:8856)
	at reactor.core.publisher.Flux.subscribe(Flux.java:8780)
	at reactor.core.publisher.Flux.subscribe(Flux.java:8698)
	at com.flyxy.ai.test.ai.StreamAutoAgentTest.testStreamAutoAgent(StreamAutoAgentTest.java:97)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.springframework.test.context.junit4.statements.RunBeforeTestExecutionCallbacks.evaluate(RunBeforeTestExecutionCallbacks.java:76)
	at org.springframework.test.context.junit4.statements.RunAfterTestExecutionCallbacks.evaluate(RunAfterTestExecutionCallbacks.java:84)
	at org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:26)
	at org.springframework.test.context.junit4.statements.RunBeforeTestMethodCallbacks.evaluate(RunBeforeTestMethodCallbacks.java:75)
	at org.springframework.test.context.junit4.statements.RunAfterTestMethodCallbacks.evaluate(RunAfterTestMethodCallbacks.java:86)
	at org.springframework.test.context.junit4.statements.SpringRepeat.evaluate(SpringRepeat.java:84)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:252)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:97)
	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at org.springframework.test.context.junit4.statements.RunBeforeTestClassCallbacks.evaluate(RunBeforeTestClassCallbacks.java:61)
	at org.springframework.test.context.junit4.statements.RunAfterTestClassCallbacks.evaluate(RunAfterTestClassCallbacks.java:70)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.run(SpringJUnit4ClassRunner.java:191)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
	at com.intellij.junit4.JUnit4IdeaTestRunner.startRunnerWithArgs(JUnit4IdeaTestRunner.java:69)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater$1.execute(IdeaTestRunner.java:38)
	at com.intellij.rt.execution.junit.TestsRepeater.repeat(TestsRepeater.java:11)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:35)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:231)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:55)
25-09-07.15:05:46.617 [main            ] ERROR Operators              - Operator called default onErrorDropped
reactor.core.Exceptions$ErrorCallbackNotImplemented: java.lang.NullPointerException: Cannot invoke "com.flyxy.ai.domain.agent.model.valobj.AiAgentClientFlowConfigVO.getClientId()" because "aiAgentClientFlowConfigVO" is null
Caused by: java.lang.NullPointerException: Cannot invoke "com.flyxy.ai.domain.agent.model.valobj.AiAgentClientFlowConfigVO.getClientId()" because "aiAgentClientFlowConfigVO" is null
	at com.flyxy.ai.domain.agent.service.exec.auto.step.Step1TaskAnalyzerClient.doApply(Step1TaskAnalyzerClient.java:59)
	at com.flyxy.ai.domain.agent.service.exec.auto.step.Step1TaskAnalyzerClient.doApply(Step1TaskAnalyzerClient.java:25)
	at cn.bugstack.wrench.design.framework.tree.AbstractMultiThreadStrategyRouter.apply(AbstractMultiThreadStrategyRouter.java:31)
	at cn.bugstack.wrench.design.framework.tree.AbstractMultiThreadStrategyRouter.router(AbstractMultiThreadStrategyRouter.java:22)
	at com.flyxy.ai.domain.agent.service.exec.auto.step.RootNode.doApply(RootNode.java:62)
	at com.flyxy.ai.domain.agent.service.exec.auto.step.RootNode.doApply(RootNode.java:20)
	at cn.bugstack.wrench.design.framework.tree.AbstractMultiThreadStrategyRouter.apply(AbstractMultiThreadStrategyRouter.java:31)
	at com.flyxy.ai.domain.agent.service.exec.auto.AutoExecuteStrategy.execute(AutoExecuteStrategy.java:56)
	at com.flyxy.ai.test.ai.StreamAutoAgentTest.lambda$testStreamAutoAgent$0(StreamAutoAgentTest.java:76)
	at reactor.core.publisher.FluxCreate.subscribe(FluxCreate.java:97)
	at reactor.core.publisher.Flux.subscribe(Flux.java:8891)
	at reactor.core.publisher.Flux.subscribeWith(Flux.java:9012)
	at reactor.core.publisher.Flux.subscribe(Flux.java:8856)
	at reactor.core.publisher.Flux.subscribe(Flux.java:8780)
	at reactor.core.publisher.Flux.subscribe(Flux.java:8698)
	at com.flyxy.ai.test.ai.StreamAutoAgentTest.testStreamAutoAgent(StreamAutoAgentTest.java:97)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.springframework.test.context.junit4.statements.RunBeforeTestExecutionCallbacks.evaluate(RunBeforeTestExecutionCallbacks.java:76)
	at org.springframework.test.context.junit4.statements.RunAfterTestExecutionCallbacks.evaluate(RunAfterTestExecutionCallbacks.java:84)
	at org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:26)
	at org.springframework.test.context.junit4.statements.RunBeforeTestMethodCallbacks.evaluate(RunBeforeTestMethodCallbacks.java:75)
	at org.springframework.test.context.junit4.statements.RunAfterTestMethodCallbacks.evaluate(RunAfterTestMethodCallbacks.java:86)
	at org.springframework.test.context.junit4.statements.SpringRepeat.evaluate(SpringRepeat.java:84)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:252)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:97)
	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at org.springframework.test.context.junit4.statements.RunBeforeTestClassCallbacks.evaluate(RunBeforeTestClassCallbacks.java:61)
	at org.springframework.test.context.junit4.statements.RunAfterTestClassCallbacks.evaluate(RunAfterTestClassCallbacks.java:70)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.run(SpringJUnit4ClassRunner.java:191)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
	at com.intellij.junit4.JUnit4IdeaTestRunner.startRunnerWithArgs(JUnit4IdeaTestRunner.java:69)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater$1.execute(IdeaTestRunner.java:38)
	at com.intellij.rt.execution.junit.TestsRepeater.repeat(TestsRepeater.java:11)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:35)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:231)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:55)
25-09-07.15:05:46.617 [main            ] INFO  StreamAutoAgentTest    - 测试完成，总结果长度: 165
25-09-07.15:05:46.617 [main            ] INFO  StreamAutoAgentTest    - 完整结果: 
🚀 === AutoAgent执行开始 ===
📋 === 动态多轮执行开始 ===
👤 用户输入: 请帮我分析一下人工智能的发展趋势，并给出学习建议
🔢 最大执行步数: 2
🆔 会话ID: stream-session-1757228746599


🚀 === 开始执行 Step1-任务分析 (第1步) ===

25-09-07.15:05:46.630 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MainHikariPool - Shutdown initiated...
25-09-07.15:05:46.644 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MainHikariPool - Shutdown completed.
25-09-07.15:10:56.034 [main            ] INFO  StreamAutoAgentTest    - Starting StreamAutoAgentTest using Java 17.0.12 with PID 37208 (started by 30562 in D:\ACode\project\ai-agent\ai-agent-app)
25-09-07.15:10:56.035 [main            ] INFO  StreamAutoAgentTest    - The following 1 profile is active: "dev"
25-09-07.15:10:58.781 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_openai. Is empty: false
25-09-07.15:10:58.787 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_openai in schema: public
25-09-07.15:10:58.788 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-09-07.15:10:59.098 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-09-07.15:10:59.614 [main            ] INFO  StreamAutoAgentTest    - Started StreamAutoAgentTest in 4.152 seconds (process running for 5.472)
25-09-07.15:10:59.619 [main            ] INFO  AgentAutoLoadConfig    - 开始自动加载Agent
25-09-07.15:10:59.619 [main            ] INFO  AgentAutoLoadConfig    - 开始自动装配AI客户端，客户端ID列表: [3101, 3102, 3103, 3104]
25-09-07.15:10:59.623 [main            ] INFO  RootNode               - AIClient装配链---RootNode---开始查询配置信息
25-09-07.15:10:59.624 [pool-2-thread-1 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_api) [3101, 3102, 3103, 3104]
25-09-07.15:10:59.625 [pool-2-thread-2 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_model) [3101, 3102, 3103, 3104]
25-09-07.15:10:59.626 [pool-2-thread-3 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_tool_mcp) [3101, 3102, 3103, 3104]
25-09-07.15:10:59.627 [pool-2-thread-4 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_system_prompt) [3101, 3102, 3103, 3104]
25-09-07.15:10:59.627 [pool-2-thread-5 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_advisor) [3101, 3102, 3103, 3104]
25-09-07.15:10:59.628 [pool-2-thread-6 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client) [3101, 3102, 3103, 3104]
25-09-07.15:10:59.653 [pool-2-thread-4 ] INFO  HikariDataSource       - MainHikariPool - Starting...
25-09-07.15:10:59.918 [pool-2-thread-4 ] INFO  HikariPool             - MainHikariPool - Added connection com.mysql.cj.jdbc.ConnectionImpl@3464f39c
25-09-07.15:10:59.919 [pool-2-thread-4 ] INFO  HikariDataSource       - MainHikariPool - Start completed.
25-09-07.15:11:00.177 [main            ] INFO  RootNode               - AIClient装配链---RootNode---查询配置信息完毕
25-09-07.15:11:00.178 [main            ] INFO  AiClientApiNode        - AIClient装配链---AiClientApiNode---开始装配API
25-09-07.15:11:00.185 [main            ] INFO  AiClientMcpNode        - AIClient装配链---AiClientMcpNode---开始装配MCP
25-09-07.15:11:00.574 [HttpClient-10-Worker-0] INFO  McpAsyncClient         - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[completions=null, experimental={}, logging=null, prompts=PromptCapabilities[listChanged=false], resources=ResourceCapabilities[subscribe=false, listChanged=false], tools=ToolCapabilities[listChanged=false]], Info: Implementation[name=AIsearch, version=1.4.1] and Instructions null
25-09-07.15:11:00.657 [main            ] INFO  AiClientMcpNode        - sse方式注册mcp: baidu-search 成功
25-09-07.15:11:00.657 [main            ] INFO  AiClientModelNode      - AIClient装配链---AiClientModelNode---开始装配Model
25-09-07.15:11:00.659 [main            ] INFO  AiClientModelNode      - 注册模型-modelId:2001, modelName:deepseek-v3-成功
25-09-07.15:11:00.659 [main            ] INFO  AiClientAdvisorNode    - AIClient装配链---AiClientAdvisorNode---开始装配顾问
25-09-07.15:11:00.670 [main            ] INFO  AiClientAdvisorNode    - 注册顾问---advisorId:4001, advisorName:记忆---成功
25-09-07.15:11:00.671 [main            ] INFO  AiClientNode           - AIClient装配链---AiClientNode---开始装配Client
25-09-07.15:11:00.773 [main            ] INFO  AiClientNode           - 注册Client---clientId:3101, clientName:任务分析和状态判断---成功
25-09-07.15:11:00.858 [main            ] INFO  AiClientNode           - 注册Client---clientId:3102, clientName:具体任务执行---成功
25-09-07.15:11:00.924 [main            ] INFO  AiClientNode           - 注册Client---clientId:3103, clientName:质量检查和优化---成功
25-09-07.15:11:00.924 [main            ] INFO  AgentAutoLoadConfig    - 自动装配AI客户端完成
25-09-07.15:11:01.556 [main            ] INFO  RootNode               - AIClient装配链---RootNode---开始查询配置信息
25-09-07.15:11:01.557 [pool-2-thread-7 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_api) [3101, 3102, 3103]
25-09-07.15:11:01.557 [pool-2-thread-8 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_model) [3101, 3102, 3103]
25-09-07.15:11:01.557 [pool-2-thread-9 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_tool_mcp) [3101, 3102, 3103]
25-09-07.15:11:01.557 [pool-2-thread-10] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_system_prompt) [3101, 3102, 3103]
25-09-07.15:11:01.557 [pool-2-thread-11] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_advisor) [3101, 3102, 3103]
25-09-07.15:11:01.557 [pool-2-thread-12] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client) [3101, 3102, 3103]
25-09-07.15:11:01.571 [main            ] INFO  RootNode               - AIClient装配链---RootNode---查询配置信息完毕
25-09-07.15:11:01.571 [main            ] INFO  AiClientApiNode        - AIClient装配链---AiClientApiNode---开始装配API
25-09-07.15:11:01.577 [main            ] INFO  AiClientMcpNode        - AIClient装配链---AiClientMcpNode---开始装配MCP
25-09-07.15:11:01.765 [HttpClient-13-Worker-2] INFO  McpAsyncClient         - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[completions=null, experimental={}, logging=null, prompts=PromptCapabilities[listChanged=false], resources=ResourceCapabilities[subscribe=false, listChanged=false], tools=ToolCapabilities[listChanged=false]], Info: Implementation[name=AIsearch, version=1.4.1] and Instructions null
25-09-07.15:11:01.852 [main            ] INFO  AiClientMcpNode        - sse方式注册mcp: baidu-search 成功
25-09-07.15:11:01.852 [main            ] INFO  AiClientModelNode      - AIClient装配链---AiClientModelNode---开始装配Model
25-09-07.15:11:01.853 [main            ] INFO  AiClientModelNode      - 注册模型-modelId:2001, modelName:deepseek-v3-成功
25-09-07.15:11:01.853 [main            ] INFO  AiClientAdvisorNode    - AIClient装配链---AiClientAdvisorNode---开始装配顾问
25-09-07.15:11:01.853 [main            ] INFO  AiClientAdvisorNode    - 注册顾问---advisorId:4001, advisorName:记忆---成功
25-09-07.15:11:01.853 [main            ] INFO  AiClientNode           - AIClient装配链---AiClientNode---开始装配Client
25-09-07.15:11:01.918 [main            ] INFO  AiClientNode           - 注册Client---clientId:3101, clientName:任务分析和状态判断---成功
25-09-07.15:11:01.987 [main            ] INFO  AiClientNode           - 注册Client---clientId:3102, clientName:具体任务执行---成功
25-09-07.15:11:02.063 [main            ] INFO  AiClientNode           - 注册Client---clientId:3103, clientName:质量检查和优化---成功
25-09-07.15:11:02.063 [main            ] INFO  StreamAutoAgentTest    - 客户端构建:org.springframework.ai.chat.client.DefaultChatClient@248941db
25-09-07.15:11:02.064 [main            ] INFO  StreamAutoAgentTest    - 开始测试流式AutoAgent
25-09-07.15:11:02.075 [main            ] INFO  AutoExecuteStrategy    - 开始执行AutoAgent流式任务，会话ID: stream-session-1757229062064
25-09-07.15:11:02.075 [main            ] INFO  StreamAutoAgentTest    - 收到流式内容: 🚀 === AutoAgent执行开始 ===

25-09-07.15:11:02.077 [main            ] ERROR AgentRepository        - Query ai agent client flow config failed, aiAgentId: 3
org.mybatis.spring.MyBatisSystemException: 
### Error querying database.  Cause: org.apache.ibatis.type.TypeException: Could not set parameters for mapping: ParameterMapping{property='agentId', mode=IN, javaType=class java.lang.Long, jdbcType=null, numericScale=null, resultMapId='null', jdbcTypeName='null', expression='null'}. Cause: org.apache.ibatis.type.TypeException: Error setting non null for parameter #1 with JdbcType null . Try setting a different JdbcType for this parameter or a different configuration property. Cause: java.lang.ClassCastException: class java.lang.String cannot be cast to class java.lang.Long (java.lang.String and java.lang.Long are in module java.base of loader 'bootstrap')
### The error may exist in file [D:\ACode\project\ai-agent\ai-agent-app\target\classes\mybatis\mapper\ai_agent_flow_config_mapper.xml]
### The error may involve com.flyxy.ai.infrastructure.dao.IAiAgentFlowConfigDao.queryByAgentId-Inline
### The error occurred while setting parameters
### SQL: SELECT id, agent_id, client_id, client_name, client_type, sequence, create_time         FROM ai_agent_flow_config         WHERE agent_id = ?         ORDER BY sequence
### Cause: org.apache.ibatis.type.TypeException: Could not set parameters for mapping: ParameterMapping{property='agentId', mode=IN, javaType=class java.lang.Long, jdbcType=null, numericScale=null, resultMapId='null', jdbcTypeName='null', expression='null'}. Cause: org.apache.ibatis.type.TypeException: Error setting non null for parameter #1 with JdbcType null . Try setting a different JdbcType for this parameter or a different configuration property. Cause: java.lang.ClassCastException: class java.lang.String cannot be cast to class java.lang.Long (java.lang.String and java.lang.Long are in module java.base of loader 'bootstrap')
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:99)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:347)
	at jdk.proxy2/jdk.proxy2.$Proxy79.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:194)
	at org.apache.ibatis.binding.MapperMethod.executeForMany(MapperMethod.java:147)
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:80)
	at org.apache.ibatis.binding.MapperProxy$PlainMethodInvoker.invoke(MapperProxy.java:141)
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:86)
	at jdk.proxy2/jdk.proxy2.$Proxy81.queryByAgentId(Unknown Source)
	at com.flyxy.ai.infrastructure.adapter.repository.AgentRepository.queryAiAgentClientFlowConfig(AgentRepository.java:472)
	at com.flyxy.ai.domain.agent.service.exec.auto.step.RootNode.multiThread(RootNode.java:32)
	at com.flyxy.ai.domain.agent.service.exec.auto.step.RootNode.multiThread(RootNode.java:20)
	at cn.bugstack.wrench.design.framework.tree.AbstractMultiThreadStrategyRouter.apply(AbstractMultiThreadStrategyRouter.java:29)
	at com.flyxy.ai.domain.agent.service.exec.auto.AutoExecuteStrategy.execute(AutoExecuteStrategy.java:56)
	at com.flyxy.ai.test.ai.StreamAutoAgentTest.lambda$testStreamAutoAgent$0(StreamAutoAgentTest.java:76)
	at reactor.core.publisher.FluxCreate.subscribe(FluxCreate.java:97)
	at reactor.core.publisher.Flux.subscribe(Flux.java:8891)
	at reactor.core.publisher.Flux.subscribeWith(Flux.java:9012)
	at reactor.core.publisher.Flux.subscribe(Flux.java:8856)
	at reactor.core.publisher.Flux.subscribe(Flux.java:8780)
	at reactor.core.publisher.Flux.subscribe(Flux.java:8698)
	at com.flyxy.ai.test.ai.StreamAutoAgentTest.testStreamAutoAgent(StreamAutoAgentTest.java:97)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.springframework.test.context.junit4.statements.RunBeforeTestExecutionCallbacks.evaluate(RunBeforeTestExecutionCallbacks.java:76)
	at org.springframework.test.context.junit4.statements.RunAfterTestExecutionCallbacks.evaluate(RunAfterTestExecutionCallbacks.java:84)
	at org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:26)
	at org.springframework.test.context.junit4.statements.RunBeforeTestMethodCallbacks.evaluate(RunBeforeTestMethodCallbacks.java:75)
	at org.springframework.test.context.junit4.statements.RunAfterTestMethodCallbacks.evaluate(RunAfterTestMethodCallbacks.java:86)
	at org.springframework.test.context.junit4.statements.SpringRepeat.evaluate(SpringRepeat.java:84)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:252)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:97)
	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at org.springframework.test.context.junit4.statements.RunBeforeTestClassCallbacks.evaluate(RunBeforeTestClassCallbacks.java:61)
	at org.springframework.test.context.junit4.statements.RunAfterTestClassCallbacks.evaluate(RunAfterTestClassCallbacks.java:70)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.run(SpringJUnit4ClassRunner.java:191)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
	at com.intellij.junit4.JUnit4IdeaTestRunner.startRunnerWithArgs(JUnit4IdeaTestRunner.java:69)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater$1.execute(IdeaTestRunner.java:38)
	at com.intellij.rt.execution.junit.TestsRepeater.repeat(TestsRepeater.java:11)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:35)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:231)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:55)
Caused by: org.apache.ibatis.type.TypeException: Could not set parameters for mapping: ParameterMapping{property='agentId', mode=IN, javaType=class java.lang.Long, jdbcType=null, numericScale=null, resultMapId='null', jdbcTypeName='null', expression='null'}. Cause: org.apache.ibatis.type.TypeException: Error setting non null for parameter #1 with JdbcType null . Try setting a different JdbcType for this parameter or a different configuration property. Cause: java.lang.ClassCastException: class java.lang.String cannot be cast to class java.lang.Long (java.lang.String and java.lang.Long are in module java.base of loader 'bootstrap')
	at org.apache.ibatis.scripting.defaults.DefaultParameterHandler.setParameters(DefaultParameterHandler.java:92)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.parameterize(PreparedStatementHandler.java:97)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.parameterize(RoutingStatementHandler.java:65)
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:91)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:64)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:336)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:158)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:110)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:90)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142)
	at jdk.internal.reflect.GeneratedMethodAccessor17.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:333)
	... 54 common frames omitted
Caused by: org.apache.ibatis.type.TypeException: Error setting non null for parameter #1 with JdbcType null . Try setting a different JdbcType for this parameter or a different configuration property. Cause: java.lang.ClassCastException: class java.lang.String cannot be cast to class java.lang.Long (java.lang.String and java.lang.Long are in module java.base of loader 'bootstrap')
	at org.apache.ibatis.type.BaseTypeHandler.setParameter(BaseTypeHandler.java:76)
	at org.apache.ibatis.scripting.defaults.DefaultParameterHandler.setParameters(DefaultParameterHandler.java:90)
	... 69 common frames omitted
Caused by: java.lang.ClassCastException: class java.lang.String cannot be cast to class java.lang.Long (java.lang.String and java.lang.Long are in module java.base of loader 'bootstrap')
	at org.apache.ibatis.type.LongTypeHandler.setNonNullParameter(LongTypeHandler.java:26)
	at org.apache.ibatis.type.BaseTypeHandler.setParameter(BaseTypeHandler.java:74)
	... 70 common frames omitted
25-09-07.15:11:02.079 [main            ] INFO  RootNode               - === 动态多轮执行测试开始 ====
25-09-07.15:11:02.079 [main            ] INFO  RootNode               - 用户输入: 请帮我分析一下人工智能的发展趋势，并给出学习建议
25-09-07.15:11:02.080 [main            ] INFO  RootNode               - 最大执行步数: 2
25-09-07.15:11:02.080 [main            ] INFO  RootNode               - 会话ID: stream-session-1757229062064
25-09-07.15:11:02.080 [main            ] INFO  StreamAutoAgentTest    - 收到流式内容: 📋 === 动态多轮执行开始 ===
👤 用户输入: 请帮我分析一下人工智能的发展趋势，并给出学习建议
🔢 最大执行步数: 2
🆔 会话ID: stream-session-1757229062064


25-09-07.15:11:02.080 [main            ] INFO  Step1TaskAnalyzerClient - === 动态多轮执行第1步 Step1 ====
25-09-07.15:11:02.080 [main            ] INFO  StreamAutoAgentTest    - 收到流式内容: 
🚀 === 开始执行 Step1-任务分析 (第1步) ===

25-09-07.15:11:02.080 [main            ] INFO  Step1TaskAnalyzerClient - 
📊 阶段1: 任务状态分析
25-09-07.15:11:02.082 [main            ] ERROR AutoExecuteStrategy    - AutoAgent流式任务执行异常，会话ID: stream-session-1757229062064
java.lang.NullPointerException: Cannot invoke "com.flyxy.ai.domain.agent.model.valobj.AiAgentClientFlowConfigVO.getClientId()" because "aiAgentClientFlowConfigVO" is null
	at com.flyxy.ai.domain.agent.service.exec.auto.step.Step1TaskAnalyzerClient.doApply(Step1TaskAnalyzerClient.java:59)
	at com.flyxy.ai.domain.agent.service.exec.auto.step.Step1TaskAnalyzerClient.doApply(Step1TaskAnalyzerClient.java:25)
	at cn.bugstack.wrench.design.framework.tree.AbstractMultiThreadStrategyRouter.apply(AbstractMultiThreadStrategyRouter.java:31)
	at cn.bugstack.wrench.design.framework.tree.AbstractMultiThreadStrategyRouter.router(AbstractMultiThreadStrategyRouter.java:22)
	at com.flyxy.ai.domain.agent.service.exec.auto.step.RootNode.doApply(RootNode.java:62)
	at com.flyxy.ai.domain.agent.service.exec.auto.step.RootNode.doApply(RootNode.java:20)
	at cn.bugstack.wrench.design.framework.tree.AbstractMultiThreadStrategyRouter.apply(AbstractMultiThreadStrategyRouter.java:31)
	at com.flyxy.ai.domain.agent.service.exec.auto.AutoExecuteStrategy.execute(AutoExecuteStrategy.java:56)
	at com.flyxy.ai.test.ai.StreamAutoAgentTest.lambda$testStreamAutoAgent$0(StreamAutoAgentTest.java:76)
	at reactor.core.publisher.FluxCreate.subscribe(FluxCreate.java:97)
	at reactor.core.publisher.Flux.subscribe(Flux.java:8891)
	at reactor.core.publisher.Flux.subscribeWith(Flux.java:9012)
	at reactor.core.publisher.Flux.subscribe(Flux.java:8856)
	at reactor.core.publisher.Flux.subscribe(Flux.java:8780)
	at reactor.core.publisher.Flux.subscribe(Flux.java:8698)
	at com.flyxy.ai.test.ai.StreamAutoAgentTest.testStreamAutoAgent(StreamAutoAgentTest.java:97)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.springframework.test.context.junit4.statements.RunBeforeTestExecutionCallbacks.evaluate(RunBeforeTestExecutionCallbacks.java:76)
	at org.springframework.test.context.junit4.statements.RunAfterTestExecutionCallbacks.evaluate(RunAfterTestExecutionCallbacks.java:84)
	at org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:26)
	at org.springframework.test.context.junit4.statements.RunBeforeTestMethodCallbacks.evaluate(RunBeforeTestMethodCallbacks.java:75)
	at org.springframework.test.context.junit4.statements.RunAfterTestMethodCallbacks.evaluate(RunAfterTestMethodCallbacks.java:86)
	at org.springframework.test.context.junit4.statements.SpringRepeat.evaluate(SpringRepeat.java:84)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:252)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:97)
	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at org.springframework.test.context.junit4.statements.RunBeforeTestClassCallbacks.evaluate(RunBeforeTestClassCallbacks.java:61)
	at org.springframework.test.context.junit4.statements.RunAfterTestClassCallbacks.evaluate(RunAfterTestClassCallbacks.java:70)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.run(SpringJUnit4ClassRunner.java:191)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
	at com.intellij.junit4.JUnit4IdeaTestRunner.startRunnerWithArgs(JUnit4IdeaTestRunner.java:69)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater$1.execute(IdeaTestRunner.java:38)
	at com.intellij.rt.execution.junit.TestsRepeater.repeat(TestsRepeater.java:11)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:35)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:231)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:55)
25-09-07.15:11:02.088 [main            ] ERROR StreamAutoAgentTest    - 流式执行出错
java.lang.NullPointerException: Cannot invoke "com.flyxy.ai.domain.agent.model.valobj.AiAgentClientFlowConfigVO.getClientId()" because "aiAgentClientFlowConfigVO" is null
	at com.flyxy.ai.domain.agent.service.exec.auto.step.Step1TaskAnalyzerClient.doApply(Step1TaskAnalyzerClient.java:59)
	at com.flyxy.ai.domain.agent.service.exec.auto.step.Step1TaskAnalyzerClient.doApply(Step1TaskAnalyzerClient.java:25)
	at cn.bugstack.wrench.design.framework.tree.AbstractMultiThreadStrategyRouter.apply(AbstractMultiThreadStrategyRouter.java:31)
	at cn.bugstack.wrench.design.framework.tree.AbstractMultiThreadStrategyRouter.router(AbstractMultiThreadStrategyRouter.java:22)
	at com.flyxy.ai.domain.agent.service.exec.auto.step.RootNode.doApply(RootNode.java:62)
	at com.flyxy.ai.domain.agent.service.exec.auto.step.RootNode.doApply(RootNode.java:20)
	at cn.bugstack.wrench.design.framework.tree.AbstractMultiThreadStrategyRouter.apply(AbstractMultiThreadStrategyRouter.java:31)
	at com.flyxy.ai.domain.agent.service.exec.auto.AutoExecuteStrategy.execute(AutoExecuteStrategy.java:56)
	at com.flyxy.ai.test.ai.StreamAutoAgentTest.lambda$testStreamAutoAgent$0(StreamAutoAgentTest.java:76)
	at reactor.core.publisher.FluxCreate.subscribe(FluxCreate.java:97)
	at reactor.core.publisher.Flux.subscribe(Flux.java:8891)
	at reactor.core.publisher.Flux.subscribeWith(Flux.java:9012)
	at reactor.core.publisher.Flux.subscribe(Flux.java:8856)
	at reactor.core.publisher.Flux.subscribe(Flux.java:8780)
	at reactor.core.publisher.Flux.subscribe(Flux.java:8698)
	at com.flyxy.ai.test.ai.StreamAutoAgentTest.testStreamAutoAgent(StreamAutoAgentTest.java:97)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.springframework.test.context.junit4.statements.RunBeforeTestExecutionCallbacks.evaluate(RunBeforeTestExecutionCallbacks.java:76)
	at org.springframework.test.context.junit4.statements.RunAfterTestExecutionCallbacks.evaluate(RunAfterTestExecutionCallbacks.java:84)
	at org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:26)
	at org.springframework.test.context.junit4.statements.RunBeforeTestMethodCallbacks.evaluate(RunBeforeTestMethodCallbacks.java:75)
	at org.springframework.test.context.junit4.statements.RunAfterTestMethodCallbacks.evaluate(RunAfterTestMethodCallbacks.java:86)
	at org.springframework.test.context.junit4.statements.SpringRepeat.evaluate(SpringRepeat.java:84)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:252)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:97)
	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at org.springframework.test.context.junit4.statements.RunBeforeTestClassCallbacks.evaluate(RunBeforeTestClassCallbacks.java:61)
	at org.springframework.test.context.junit4.statements.RunAfterTestClassCallbacks.evaluate(RunAfterTestClassCallbacks.java:70)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.run(SpringJUnit4ClassRunner.java:191)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
	at com.intellij.junit4.JUnit4IdeaTestRunner.startRunnerWithArgs(JUnit4IdeaTestRunner.java:69)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater$1.execute(IdeaTestRunner.java:38)
	at com.intellij.rt.execution.junit.TestsRepeater.repeat(TestsRepeater.java:11)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:35)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:231)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:55)
25-09-07.15:11:02.088 [main            ] ERROR Operators              - Operator called default onErrorDropped
reactor.core.Exceptions$ErrorCallbackNotImplemented: java.lang.NullPointerException: Cannot invoke "com.flyxy.ai.domain.agent.model.valobj.AiAgentClientFlowConfigVO.getClientId()" because "aiAgentClientFlowConfigVO" is null
Caused by: java.lang.NullPointerException: Cannot invoke "com.flyxy.ai.domain.agent.model.valobj.AiAgentClientFlowConfigVO.getClientId()" because "aiAgentClientFlowConfigVO" is null
	at com.flyxy.ai.domain.agent.service.exec.auto.step.Step1TaskAnalyzerClient.doApply(Step1TaskAnalyzerClient.java:59)
	at com.flyxy.ai.domain.agent.service.exec.auto.step.Step1TaskAnalyzerClient.doApply(Step1TaskAnalyzerClient.java:25)
	at cn.bugstack.wrench.design.framework.tree.AbstractMultiThreadStrategyRouter.apply(AbstractMultiThreadStrategyRouter.java:31)
	at cn.bugstack.wrench.design.framework.tree.AbstractMultiThreadStrategyRouter.router(AbstractMultiThreadStrategyRouter.java:22)
	at com.flyxy.ai.domain.agent.service.exec.auto.step.RootNode.doApply(RootNode.java:62)
	at com.flyxy.ai.domain.agent.service.exec.auto.step.RootNode.doApply(RootNode.java:20)
	at cn.bugstack.wrench.design.framework.tree.AbstractMultiThreadStrategyRouter.apply(AbstractMultiThreadStrategyRouter.java:31)
	at com.flyxy.ai.domain.agent.service.exec.auto.AutoExecuteStrategy.execute(AutoExecuteStrategy.java:56)
	at com.flyxy.ai.test.ai.StreamAutoAgentTest.lambda$testStreamAutoAgent$0(StreamAutoAgentTest.java:76)
	at reactor.core.publisher.FluxCreate.subscribe(FluxCreate.java:97)
	at reactor.core.publisher.Flux.subscribe(Flux.java:8891)
	at reactor.core.publisher.Flux.subscribeWith(Flux.java:9012)
	at reactor.core.publisher.Flux.subscribe(Flux.java:8856)
	at reactor.core.publisher.Flux.subscribe(Flux.java:8780)
	at reactor.core.publisher.Flux.subscribe(Flux.java:8698)
	at com.flyxy.ai.test.ai.StreamAutoAgentTest.testStreamAutoAgent(StreamAutoAgentTest.java:97)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.springframework.test.context.junit4.statements.RunBeforeTestExecutionCallbacks.evaluate(RunBeforeTestExecutionCallbacks.java:76)
	at org.springframework.test.context.junit4.statements.RunAfterTestExecutionCallbacks.evaluate(RunAfterTestExecutionCallbacks.java:84)
	at org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:26)
	at org.springframework.test.context.junit4.statements.RunBeforeTestMethodCallbacks.evaluate(RunBeforeTestMethodCallbacks.java:75)
	at org.springframework.test.context.junit4.statements.RunAfterTestMethodCallbacks.evaluate(RunAfterTestMethodCallbacks.java:86)
	at org.springframework.test.context.junit4.statements.SpringRepeat.evaluate(SpringRepeat.java:84)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:252)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:97)
	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at org.springframework.test.context.junit4.statements.RunBeforeTestClassCallbacks.evaluate(RunBeforeTestClassCallbacks.java:61)
	at org.springframework.test.context.junit4.statements.RunAfterTestClassCallbacks.evaluate(RunAfterTestClassCallbacks.java:70)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.run(SpringJUnit4ClassRunner.java:191)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
	at com.intellij.junit4.JUnit4IdeaTestRunner.startRunnerWithArgs(JUnit4IdeaTestRunner.java:69)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater$1.execute(IdeaTestRunner.java:38)
	at com.intellij.rt.execution.junit.TestsRepeater.repeat(TestsRepeater.java:11)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:35)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:231)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:55)
25-09-07.15:11:02.089 [main            ] INFO  StreamAutoAgentTest    - 测试完成，总结果长度: 165
25-09-07.15:11:02.089 [main            ] INFO  StreamAutoAgentTest    - 完整结果: 
🚀 === AutoAgent执行开始 ===
📋 === 动态多轮执行开始 ===
👤 用户输入: 请帮我分析一下人工智能的发展趋势，并给出学习建议
🔢 最大执行步数: 2
🆔 会话ID: stream-session-1757229062064


🚀 === 开始执行 Step1-任务分析 (第1步) ===

25-09-07.15:11:02.101 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MainHikariPool - Shutdown initiated...
25-09-07.15:11:02.118 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MainHikariPool - Shutdown completed.
25-09-07.15:13:05.827 [main            ] INFO  StreamAutoAgentTest    - Starting StreamAutoAgentTest using Java 17.0.12 with PID 21476 (started by 30562 in D:\ACode\project\ai-agent\ai-agent-app)
25-09-07.15:13:05.828 [main            ] INFO  StreamAutoAgentTest    - The following 1 profile is active: "dev"
25-09-07.15:13:08.553 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_openai. Is empty: false
25-09-07.15:13:08.559 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_openai in schema: public
25-09-07.15:13:08.559 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-09-07.15:13:08.859 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-09-07.15:13:09.444 [main            ] INFO  StreamAutoAgentTest    - Started StreamAutoAgentTest in 4.156 seconds (process running for 5.396)
25-09-07.15:13:09.449 [main            ] INFO  AgentAutoLoadConfig    - 开始自动加载Agent
25-09-07.15:13:09.449 [main            ] INFO  AgentAutoLoadConfig    - 开始自动装配AI客户端，客户端ID列表: [3101, 3102, 3103, 3104]
25-09-07.15:13:09.451 [main            ] INFO  RootNode               - AIClient装配链---RootNode---开始查询配置信息
25-09-07.15:13:09.453 [pool-2-thread-1 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_api) [3101, 3102, 3103, 3104]
25-09-07.15:13:09.453 [pool-2-thread-2 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_model) [3101, 3102, 3103, 3104]
25-09-07.15:13:09.454 [pool-2-thread-3 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_tool_mcp) [3101, 3102, 3103, 3104]
25-09-07.15:13:09.454 [pool-2-thread-4 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_system_prompt) [3101, 3102, 3103, 3104]
25-09-07.15:13:09.455 [pool-2-thread-5 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_advisor) [3101, 3102, 3103, 3104]
25-09-07.15:13:09.455 [pool-2-thread-6 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client) [3101, 3102, 3103, 3104]
25-09-07.15:13:09.476 [pool-2-thread-1 ] INFO  HikariDataSource       - MainHikariPool - Starting...
25-09-07.15:13:09.707 [pool-2-thread-1 ] INFO  HikariPool             - MainHikariPool - Added connection com.mysql.cj.jdbc.ConnectionImpl@6c9afd3a
25-09-07.15:13:09.710 [pool-2-thread-1 ] INFO  HikariDataSource       - MainHikariPool - Start completed.
25-09-07.15:13:10.016 [main            ] INFO  RootNode               - AIClient装配链---RootNode---查询配置信息完毕
25-09-07.15:13:10.016 [main            ] INFO  AiClientApiNode        - AIClient装配链---AiClientApiNode---开始装配API
25-09-07.15:13:10.024 [main            ] INFO  AiClientMcpNode        - AIClient装配链---AiClientMcpNode---开始装配MCP
25-09-07.15:13:10.370 [HttpClient-10-Worker-1] INFO  McpAsyncClient         - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[completions=null, experimental={}, logging=null, prompts=PromptCapabilities[listChanged=false], resources=ResourceCapabilities[subscribe=false, listChanged=false], tools=ToolCapabilities[listChanged=false]], Info: Implementation[name=AIsearch, version=1.4.1] and Instructions null
25-09-07.15:13:10.447 [main            ] INFO  AiClientMcpNode        - sse方式注册mcp: baidu-search 成功
25-09-07.15:13:10.447 [main            ] INFO  AiClientModelNode      - AIClient装配链---AiClientModelNode---开始装配Model
25-09-07.15:13:10.448 [main            ] INFO  AiClientModelNode      - 注册模型-modelId:2001, modelName:deepseek-v3-成功
25-09-07.15:13:10.448 [main            ] INFO  AiClientAdvisorNode    - AIClient装配链---AiClientAdvisorNode---开始装配顾问
25-09-07.15:13:10.460 [main            ] INFO  AiClientAdvisorNode    - 注册顾问---advisorId:4001, advisorName:记忆---成功
25-09-07.15:13:10.461 [main            ] INFO  AiClientNode           - AIClient装配链---AiClientNode---开始装配Client
25-09-07.15:13:10.549 [main            ] INFO  AiClientNode           - 注册Client---clientId:3101, clientName:任务分析和状态判断---成功
25-09-07.15:13:10.612 [main            ] INFO  AiClientNode           - 注册Client---clientId:3102, clientName:具体任务执行---成功
25-09-07.15:13:10.699 [main            ] INFO  AiClientNode           - 注册Client---clientId:3103, clientName:质量检查和优化---成功
25-09-07.15:13:10.699 [main            ] INFO  AgentAutoLoadConfig    - 自动装配AI客户端完成
25-09-07.15:13:11.348 [main            ] INFO  RootNode               - AIClient装配链---RootNode---开始查询配置信息
25-09-07.15:13:11.348 [pool-2-thread-7 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_api) [3101, 3102, 3103]
25-09-07.15:13:11.348 [pool-2-thread-8 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_model) [3101, 3102, 3103]
25-09-07.15:13:11.349 [pool-2-thread-9 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_tool_mcp) [3101, 3102, 3103]
25-09-07.15:13:11.349 [pool-2-thread-10] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_system_prompt) [3101, 3102, 3103]
25-09-07.15:13:11.349 [pool-2-thread-11] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_advisor) [3101, 3102, 3103]
25-09-07.15:13:11.349 [pool-2-thread-12] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client) [3101, 3102, 3103]
25-09-07.15:13:11.364 [main            ] INFO  RootNode               - AIClient装配链---RootNode---查询配置信息完毕
25-09-07.15:13:11.365 [main            ] INFO  AiClientApiNode        - AIClient装配链---AiClientApiNode---开始装配API
25-09-07.15:13:11.371 [main            ] INFO  AiClientMcpNode        - AIClient装配链---AiClientMcpNode---开始装配MCP
25-09-07.15:13:11.549 [HttpClient-13-Worker-2] INFO  McpAsyncClient         - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[completions=null, experimental={}, logging=null, prompts=PromptCapabilities[listChanged=false], resources=ResourceCapabilities[subscribe=false, listChanged=false], tools=ToolCapabilities[listChanged=false]], Info: Implementation[name=AIsearch, version=1.4.1] and Instructions null
25-09-07.15:13:11.648 [main            ] INFO  AiClientMcpNode        - sse方式注册mcp: baidu-search 成功
25-09-07.15:13:11.648 [main            ] INFO  AiClientModelNode      - AIClient装配链---AiClientModelNode---开始装配Model
25-09-07.15:13:11.648 [main            ] INFO  AiClientModelNode      - 注册模型-modelId:2001, modelName:deepseek-v3-成功
25-09-07.15:13:11.649 [main            ] INFO  AiClientAdvisorNode    - AIClient装配链---AiClientAdvisorNode---开始装配顾问
25-09-07.15:13:11.649 [main            ] INFO  AiClientAdvisorNode    - 注册顾问---advisorId:4001, advisorName:记忆---成功
25-09-07.15:13:11.649 [main            ] INFO  AiClientNode           - AIClient装配链---AiClientNode---开始装配Client
25-09-07.15:13:11.718 [main            ] INFO  AiClientNode           - 注册Client---clientId:3101, clientName:任务分析和状态判断---成功
25-09-07.15:13:11.785 [main            ] INFO  AiClientNode           - 注册Client---clientId:3102, clientName:具体任务执行---成功
25-09-07.15:13:11.844 [main            ] INFO  AiClientNode           - 注册Client---clientId:3103, clientName:质量检查和优化---成功
25-09-07.15:13:11.844 [main            ] INFO  StreamAutoAgentTest    - 客户端构建:org.springframework.ai.chat.client.DefaultChatClient@10a6edb
25-09-07.15:13:11.845 [main            ] INFO  StreamAutoAgentTest    - 开始测试流式AutoAgent
25-09-07.15:13:11.856 [main            ] INFO  AutoExecuteStrategy    - 开始执行AutoAgent流式任务，会话ID: stream-session-1757229191845
25-09-07.15:13:11.856 [main            ] INFO  StreamAutoAgentTest    - 收到流式内容: 🚀 === AutoAgent执行开始 ===

25-09-07.15:13:11.858 [main            ] ERROR AgentRepository        - Query ai agent client flow config failed, aiAgentId: 3
org.mybatis.spring.MyBatisSystemException: 
### Error querying database.  Cause: org.apache.ibatis.type.TypeException: Could not set parameters for mapping: ParameterMapping{property='agentId', mode=IN, javaType=class java.lang.Long, jdbcType=null, numericScale=null, resultMapId='null', jdbcTypeName='null', expression='null'}. Cause: org.apache.ibatis.type.TypeException: Error setting non null for parameter #1 with JdbcType null . Try setting a different JdbcType for this parameter or a different configuration property. Cause: java.lang.ClassCastException: class java.lang.String cannot be cast to class java.lang.Long (java.lang.String and java.lang.Long are in module java.base of loader 'bootstrap')
### The error may exist in file [D:\ACode\project\ai-agent\ai-agent-app\target\classes\mybatis\mapper\ai_agent_flow_config_mapper.xml]
### The error may involve com.flyxy.ai.infrastructure.dao.IAiAgentFlowConfigDao.queryByAgentId-Inline
### The error occurred while setting parameters
### SQL: SELECT id, agent_id, client_id, client_name, client_type, sequence, create_time         FROM ai_agent_flow_config         WHERE agent_id = ?         ORDER BY sequence
### Cause: org.apache.ibatis.type.TypeException: Could not set parameters for mapping: ParameterMapping{property='agentId', mode=IN, javaType=class java.lang.Long, jdbcType=null, numericScale=null, resultMapId='null', jdbcTypeName='null', expression='null'}. Cause: org.apache.ibatis.type.TypeException: Error setting non null for parameter #1 with JdbcType null . Try setting a different JdbcType for this parameter or a different configuration property. Cause: java.lang.ClassCastException: class java.lang.String cannot be cast to class java.lang.Long (java.lang.String and java.lang.Long are in module java.base of loader 'bootstrap')
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:99)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:347)
	at jdk.proxy2/jdk.proxy2.$Proxy79.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:194)
	at org.apache.ibatis.binding.MapperMethod.executeForMany(MapperMethod.java:147)
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:80)
	at org.apache.ibatis.binding.MapperProxy$PlainMethodInvoker.invoke(MapperProxy.java:141)
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:86)
	at jdk.proxy2/jdk.proxy2.$Proxy81.queryByAgentId(Unknown Source)
	at com.flyxy.ai.infrastructure.adapter.repository.AgentRepository.queryAiAgentClientFlowConfig(AgentRepository.java:472)
	at com.flyxy.ai.domain.agent.service.exec.auto.step.RootNode.multiThread(RootNode.java:32)
	at com.flyxy.ai.domain.agent.service.exec.auto.step.RootNode.multiThread(RootNode.java:20)
	at cn.bugstack.wrench.design.framework.tree.AbstractMultiThreadStrategyRouter.apply(AbstractMultiThreadStrategyRouter.java:29)
	at com.flyxy.ai.domain.agent.service.exec.auto.AutoExecuteStrategy.execute(AutoExecuteStrategy.java:56)
	at com.flyxy.ai.test.ai.StreamAutoAgentTest.lambda$testStreamAutoAgent$0(StreamAutoAgentTest.java:76)
	at reactor.core.publisher.FluxCreate.subscribe(FluxCreate.java:97)
	at reactor.core.publisher.Flux.subscribe(Flux.java:8891)
	at reactor.core.publisher.Flux.subscribeWith(Flux.java:9012)
	at reactor.core.publisher.Flux.subscribe(Flux.java:8856)
	at reactor.core.publisher.Flux.subscribe(Flux.java:8780)
	at reactor.core.publisher.Flux.subscribe(Flux.java:8698)
	at com.flyxy.ai.test.ai.StreamAutoAgentTest.testStreamAutoAgent(StreamAutoAgentTest.java:97)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.springframework.test.context.junit4.statements.RunBeforeTestExecutionCallbacks.evaluate(RunBeforeTestExecutionCallbacks.java:76)
	at org.springframework.test.context.junit4.statements.RunAfterTestExecutionCallbacks.evaluate(RunAfterTestExecutionCallbacks.java:84)
	at org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:26)
	at org.springframework.test.context.junit4.statements.RunBeforeTestMethodCallbacks.evaluate(RunBeforeTestMethodCallbacks.java:75)
	at org.springframework.test.context.junit4.statements.RunAfterTestMethodCallbacks.evaluate(RunAfterTestMethodCallbacks.java:86)
	at org.springframework.test.context.junit4.statements.SpringRepeat.evaluate(SpringRepeat.java:84)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:252)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:97)
	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at org.springframework.test.context.junit4.statements.RunBeforeTestClassCallbacks.evaluate(RunBeforeTestClassCallbacks.java:61)
	at org.springframework.test.context.junit4.statements.RunAfterTestClassCallbacks.evaluate(RunAfterTestClassCallbacks.java:70)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.run(SpringJUnit4ClassRunner.java:191)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
	at com.intellij.junit4.JUnit4IdeaTestRunner.startRunnerWithArgs(JUnit4IdeaTestRunner.java:69)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater$1.execute(IdeaTestRunner.java:38)
	at com.intellij.rt.execution.junit.TestsRepeater.repeat(TestsRepeater.java:11)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:35)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:231)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:55)
Caused by: org.apache.ibatis.type.TypeException: Could not set parameters for mapping: ParameterMapping{property='agentId', mode=IN, javaType=class java.lang.Long, jdbcType=null, numericScale=null, resultMapId='null', jdbcTypeName='null', expression='null'}. Cause: org.apache.ibatis.type.TypeException: Error setting non null for parameter #1 with JdbcType null . Try setting a different JdbcType for this parameter or a different configuration property. Cause: java.lang.ClassCastException: class java.lang.String cannot be cast to class java.lang.Long (java.lang.String and java.lang.Long are in module java.base of loader 'bootstrap')
	at org.apache.ibatis.scripting.defaults.DefaultParameterHandler.setParameters(DefaultParameterHandler.java:92)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.parameterize(PreparedStatementHandler.java:97)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.parameterize(RoutingStatementHandler.java:65)
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:91)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:64)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:336)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:158)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:110)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:90)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142)
	at jdk.internal.reflect.GeneratedMethodAccessor17.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:333)
	... 54 common frames omitted
Caused by: org.apache.ibatis.type.TypeException: Error setting non null for parameter #1 with JdbcType null . Try setting a different JdbcType for this parameter or a different configuration property. Cause: java.lang.ClassCastException: class java.lang.String cannot be cast to class java.lang.Long (java.lang.String and java.lang.Long are in module java.base of loader 'bootstrap')
	at org.apache.ibatis.type.BaseTypeHandler.setParameter(BaseTypeHandler.java:76)
	at org.apache.ibatis.scripting.defaults.DefaultParameterHandler.setParameters(DefaultParameterHandler.java:90)
	... 69 common frames omitted
Caused by: java.lang.ClassCastException: class java.lang.String cannot be cast to class java.lang.Long (java.lang.String and java.lang.Long are in module java.base of loader 'bootstrap')
	at org.apache.ibatis.type.LongTypeHandler.setNonNullParameter(LongTypeHandler.java:26)
	at org.apache.ibatis.type.BaseTypeHandler.setParameter(BaseTypeHandler.java:74)
	... 70 common frames omitted
25-09-07.15:13:11.859 [main            ] INFO  RootNode               - === 动态多轮执行测试开始 ====
25-09-07.15:13:11.860 [main            ] INFO  RootNode               - 用户输入: 请帮我分析一下人工智能的发展趋势，并给出学习建议
25-09-07.15:13:11.860 [main            ] INFO  RootNode               - 最大执行步数: 2
25-09-07.15:13:11.860 [main            ] INFO  RootNode               - 会话ID: stream-session-1757229191845
25-09-07.15:13:11.860 [main            ] INFO  StreamAutoAgentTest    - 收到流式内容: 📋 === 动态多轮执行开始 ===
👤 用户输入: 请帮我分析一下人工智能的发展趋势，并给出学习建议
🔢 最大执行步数: 2
🆔 会话ID: stream-session-1757229191845


25-09-07.15:13:11.860 [main            ] INFO  Step1TaskAnalyzerClient - === 动态多轮执行第1步 Step1 ====
25-09-07.15:13:11.860 [main            ] INFO  StreamAutoAgentTest    - 收到流式内容: 
🚀 === 开始执行 Step1-任务分析 (第1步) ===

25-09-07.15:13:11.860 [main            ] INFO  Step1TaskAnalyzerClient - 
📊 阶段1: 任务状态分析
25-09-07.15:13:11.862 [main            ] ERROR AutoExecuteStrategy    - AutoAgent流式任务执行异常，会话ID: stream-session-1757229191845
java.lang.NullPointerException: Cannot invoke "com.flyxy.ai.domain.agent.model.valobj.AiAgentClientFlowConfigVO.getClientId()" because "aiAgentClientFlowConfigVO" is null
	at com.flyxy.ai.domain.agent.service.exec.auto.step.Step1TaskAnalyzerClient.doApply(Step1TaskAnalyzerClient.java:59)
	at com.flyxy.ai.domain.agent.service.exec.auto.step.Step1TaskAnalyzerClient.doApply(Step1TaskAnalyzerClient.java:25)
	at cn.bugstack.wrench.design.framework.tree.AbstractMultiThreadStrategyRouter.apply(AbstractMultiThreadStrategyRouter.java:31)
	at cn.bugstack.wrench.design.framework.tree.AbstractMultiThreadStrategyRouter.router(AbstractMultiThreadStrategyRouter.java:22)
	at com.flyxy.ai.domain.agent.service.exec.auto.step.RootNode.doApply(RootNode.java:62)
	at com.flyxy.ai.domain.agent.service.exec.auto.step.RootNode.doApply(RootNode.java:20)
	at cn.bugstack.wrench.design.framework.tree.AbstractMultiThreadStrategyRouter.apply(AbstractMultiThreadStrategyRouter.java:31)
	at com.flyxy.ai.domain.agent.service.exec.auto.AutoExecuteStrategy.execute(AutoExecuteStrategy.java:56)
	at com.flyxy.ai.test.ai.StreamAutoAgentTest.lambda$testStreamAutoAgent$0(StreamAutoAgentTest.java:76)
	at reactor.core.publisher.FluxCreate.subscribe(FluxCreate.java:97)
	at reactor.core.publisher.Flux.subscribe(Flux.java:8891)
	at reactor.core.publisher.Flux.subscribeWith(Flux.java:9012)
	at reactor.core.publisher.Flux.subscribe(Flux.java:8856)
	at reactor.core.publisher.Flux.subscribe(Flux.java:8780)
	at reactor.core.publisher.Flux.subscribe(Flux.java:8698)
	at com.flyxy.ai.test.ai.StreamAutoAgentTest.testStreamAutoAgent(StreamAutoAgentTest.java:97)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.springframework.test.context.junit4.statements.RunBeforeTestExecutionCallbacks.evaluate(RunBeforeTestExecutionCallbacks.java:76)
	at org.springframework.test.context.junit4.statements.RunAfterTestExecutionCallbacks.evaluate(RunAfterTestExecutionCallbacks.java:84)
	at org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:26)
	at org.springframework.test.context.junit4.statements.RunBeforeTestMethodCallbacks.evaluate(RunBeforeTestMethodCallbacks.java:75)
	at org.springframework.test.context.junit4.statements.RunAfterTestMethodCallbacks.evaluate(RunAfterTestMethodCallbacks.java:86)
	at org.springframework.test.context.junit4.statements.SpringRepeat.evaluate(SpringRepeat.java:84)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:252)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:97)
	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at org.springframework.test.context.junit4.statements.RunBeforeTestClassCallbacks.evaluate(RunBeforeTestClassCallbacks.java:61)
	at org.springframework.test.context.junit4.statements.RunAfterTestClassCallbacks.evaluate(RunAfterTestClassCallbacks.java:70)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.run(SpringJUnit4ClassRunner.java:191)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
	at com.intellij.junit4.JUnit4IdeaTestRunner.startRunnerWithArgs(JUnit4IdeaTestRunner.java:69)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater$1.execute(IdeaTestRunner.java:38)
	at com.intellij.rt.execution.junit.TestsRepeater.repeat(TestsRepeater.java:11)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:35)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:231)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:55)
25-09-07.15:13:11.867 [main            ] ERROR StreamAutoAgentTest    - 流式执行出错
java.lang.NullPointerException: Cannot invoke "com.flyxy.ai.domain.agent.model.valobj.AiAgentClientFlowConfigVO.getClientId()" because "aiAgentClientFlowConfigVO" is null
	at com.flyxy.ai.domain.agent.service.exec.auto.step.Step1TaskAnalyzerClient.doApply(Step1TaskAnalyzerClient.java:59)
	at com.flyxy.ai.domain.agent.service.exec.auto.step.Step1TaskAnalyzerClient.doApply(Step1TaskAnalyzerClient.java:25)
	at cn.bugstack.wrench.design.framework.tree.AbstractMultiThreadStrategyRouter.apply(AbstractMultiThreadStrategyRouter.java:31)
	at cn.bugstack.wrench.design.framework.tree.AbstractMultiThreadStrategyRouter.router(AbstractMultiThreadStrategyRouter.java:22)
	at com.flyxy.ai.domain.agent.service.exec.auto.step.RootNode.doApply(RootNode.java:62)
	at com.flyxy.ai.domain.agent.service.exec.auto.step.RootNode.doApply(RootNode.java:20)
	at cn.bugstack.wrench.design.framework.tree.AbstractMultiThreadStrategyRouter.apply(AbstractMultiThreadStrategyRouter.java:31)
	at com.flyxy.ai.domain.agent.service.exec.auto.AutoExecuteStrategy.execute(AutoExecuteStrategy.java:56)
	at com.flyxy.ai.test.ai.StreamAutoAgentTest.lambda$testStreamAutoAgent$0(StreamAutoAgentTest.java:76)
	at reactor.core.publisher.FluxCreate.subscribe(FluxCreate.java:97)
	at reactor.core.publisher.Flux.subscribe(Flux.java:8891)
	at reactor.core.publisher.Flux.subscribeWith(Flux.java:9012)
	at reactor.core.publisher.Flux.subscribe(Flux.java:8856)
	at reactor.core.publisher.Flux.subscribe(Flux.java:8780)
	at reactor.core.publisher.Flux.subscribe(Flux.java:8698)
	at com.flyxy.ai.test.ai.StreamAutoAgentTest.testStreamAutoAgent(StreamAutoAgentTest.java:97)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.springframework.test.context.junit4.statements.RunBeforeTestExecutionCallbacks.evaluate(RunBeforeTestExecutionCallbacks.java:76)
	at org.springframework.test.context.junit4.statements.RunAfterTestExecutionCallbacks.evaluate(RunAfterTestExecutionCallbacks.java:84)
	at org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:26)
	at org.springframework.test.context.junit4.statements.RunBeforeTestMethodCallbacks.evaluate(RunBeforeTestMethodCallbacks.java:75)
	at org.springframework.test.context.junit4.statements.RunAfterTestMethodCallbacks.evaluate(RunAfterTestMethodCallbacks.java:86)
	at org.springframework.test.context.junit4.statements.SpringRepeat.evaluate(SpringRepeat.java:84)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:252)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:97)
	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at org.springframework.test.context.junit4.statements.RunBeforeTestClassCallbacks.evaluate(RunBeforeTestClassCallbacks.java:61)
	at org.springframework.test.context.junit4.statements.RunAfterTestClassCallbacks.evaluate(RunAfterTestClassCallbacks.java:70)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.run(SpringJUnit4ClassRunner.java:191)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
	at com.intellij.junit4.JUnit4IdeaTestRunner.startRunnerWithArgs(JUnit4IdeaTestRunner.java:69)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater$1.execute(IdeaTestRunner.java:38)
	at com.intellij.rt.execution.junit.TestsRepeater.repeat(TestsRepeater.java:11)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:35)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:231)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:55)
25-09-07.15:13:11.868 [main            ] ERROR Operators              - Operator called default onErrorDropped
reactor.core.Exceptions$ErrorCallbackNotImplemented: java.lang.NullPointerException: Cannot invoke "com.flyxy.ai.domain.agent.model.valobj.AiAgentClientFlowConfigVO.getClientId()" because "aiAgentClientFlowConfigVO" is null
Caused by: java.lang.NullPointerException: Cannot invoke "com.flyxy.ai.domain.agent.model.valobj.AiAgentClientFlowConfigVO.getClientId()" because "aiAgentClientFlowConfigVO" is null
	at com.flyxy.ai.domain.agent.service.exec.auto.step.Step1TaskAnalyzerClient.doApply(Step1TaskAnalyzerClient.java:59)
	at com.flyxy.ai.domain.agent.service.exec.auto.step.Step1TaskAnalyzerClient.doApply(Step1TaskAnalyzerClient.java:25)
	at cn.bugstack.wrench.design.framework.tree.AbstractMultiThreadStrategyRouter.apply(AbstractMultiThreadStrategyRouter.java:31)
	at cn.bugstack.wrench.design.framework.tree.AbstractMultiThreadStrategyRouter.router(AbstractMultiThreadStrategyRouter.java:22)
	at com.flyxy.ai.domain.agent.service.exec.auto.step.RootNode.doApply(RootNode.java:62)
	at com.flyxy.ai.domain.agent.service.exec.auto.step.RootNode.doApply(RootNode.java:20)
	at cn.bugstack.wrench.design.framework.tree.AbstractMultiThreadStrategyRouter.apply(AbstractMultiThreadStrategyRouter.java:31)
	at com.flyxy.ai.domain.agent.service.exec.auto.AutoExecuteStrategy.execute(AutoExecuteStrategy.java:56)
	at com.flyxy.ai.test.ai.StreamAutoAgentTest.lambda$testStreamAutoAgent$0(StreamAutoAgentTest.java:76)
	at reactor.core.publisher.FluxCreate.subscribe(FluxCreate.java:97)
	at reactor.core.publisher.Flux.subscribe(Flux.java:8891)
	at reactor.core.publisher.Flux.subscribeWith(Flux.java:9012)
	at reactor.core.publisher.Flux.subscribe(Flux.java:8856)
	at reactor.core.publisher.Flux.subscribe(Flux.java:8780)
	at reactor.core.publisher.Flux.subscribe(Flux.java:8698)
	at com.flyxy.ai.test.ai.StreamAutoAgentTest.testStreamAutoAgent(StreamAutoAgentTest.java:97)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.springframework.test.context.junit4.statements.RunBeforeTestExecutionCallbacks.evaluate(RunBeforeTestExecutionCallbacks.java:76)
	at org.springframework.test.context.junit4.statements.RunAfterTestExecutionCallbacks.evaluate(RunAfterTestExecutionCallbacks.java:84)
	at org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:26)
	at org.springframework.test.context.junit4.statements.RunBeforeTestMethodCallbacks.evaluate(RunBeforeTestMethodCallbacks.java:75)
	at org.springframework.test.context.junit4.statements.RunAfterTestMethodCallbacks.evaluate(RunAfterTestMethodCallbacks.java:86)
	at org.springframework.test.context.junit4.statements.SpringRepeat.evaluate(SpringRepeat.java:84)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:252)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:97)
	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at org.springframework.test.context.junit4.statements.RunBeforeTestClassCallbacks.evaluate(RunBeforeTestClassCallbacks.java:61)
	at org.springframework.test.context.junit4.statements.RunAfterTestClassCallbacks.evaluate(RunAfterTestClassCallbacks.java:70)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.run(SpringJUnit4ClassRunner.java:191)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
	at com.intellij.junit4.JUnit4IdeaTestRunner.startRunnerWithArgs(JUnit4IdeaTestRunner.java:69)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater$1.execute(IdeaTestRunner.java:38)
	at com.intellij.rt.execution.junit.TestsRepeater.repeat(TestsRepeater.java:11)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:35)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:231)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:55)
25-09-07.15:13:11.868 [main            ] INFO  StreamAutoAgentTest    - 测试完成，总结果长度: 165
25-09-07.15:13:11.868 [main            ] INFO  StreamAutoAgentTest    - 完整结果: 
🚀 === AutoAgent执行开始 ===
📋 === 动态多轮执行开始 ===
👤 用户输入: 请帮我分析一下人工智能的发展趋势，并给出学习建议
🔢 最大执行步数: 2
🆔 会话ID: stream-session-1757229191845


🚀 === 开始执行 Step1-任务分析 (第1步) ===

25-09-07.15:13:11.881 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MainHikariPool - Shutdown initiated...
25-09-07.15:13:11.897 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MainHikariPool - Shutdown completed.
25-09-07.15:14:47.395 [main            ] INFO  StreamAutoAgentTest    - Starting StreamAutoAgentTest using Java 17.0.12 with PID 32432 (started by 30562 in D:\ACode\project\ai-agent\ai-agent-app)
25-09-07.15:14:47.397 [main            ] INFO  StreamAutoAgentTest    - The following 1 profile is active: "dev"
25-09-07.15:14:50.237 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_openai. Is empty: false
25-09-07.15:14:50.244 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_openai in schema: public
25-09-07.15:14:50.244 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-09-07.15:14:50.562 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-09-07.15:14:51.090 [main            ] INFO  StreamAutoAgentTest    - Started StreamAutoAgentTest in 4.221 seconds (process running for 5.442)
25-09-07.15:14:51.094 [main            ] INFO  AgentAutoLoadConfig    - 开始自动加载Agent
25-09-07.15:14:51.095 [main            ] INFO  AgentAutoLoadConfig    - 开始自动装配AI客户端，客户端ID列表: [3101, 3102, 3103, 3104]
25-09-07.15:14:51.098 [main            ] INFO  RootNode               - AIClient装配链---RootNode---开始查询配置信息
25-09-07.15:14:51.100 [pool-2-thread-1 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_api) [3101, 3102, 3103, 3104]
25-09-07.15:14:51.100 [pool-2-thread-2 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_model) [3101, 3102, 3103, 3104]
25-09-07.15:14:51.100 [pool-2-thread-3 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_tool_mcp) [3101, 3102, 3103, 3104]
25-09-07.15:14:51.101 [pool-2-thread-4 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_system_prompt) [3101, 3102, 3103, 3104]
25-09-07.15:14:51.101 [pool-2-thread-5 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_advisor) [3101, 3102, 3103, 3104]
25-09-07.15:14:51.102 [pool-2-thread-6 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client) [3101, 3102, 3103, 3104]
25-09-07.15:14:51.125 [pool-2-thread-1 ] INFO  HikariDataSource       - MainHikariPool - Starting...
25-09-07.15:14:51.384 [pool-2-thread-1 ] INFO  HikariPool             - MainHikariPool - Added connection com.mysql.cj.jdbc.ConnectionImpl@5f98a4ff
25-09-07.15:14:51.386 [pool-2-thread-1 ] INFO  HikariDataSource       - MainHikariPool - Start completed.
25-09-07.15:14:51.657 [main            ] INFO  RootNode               - AIClient装配链---RootNode---查询配置信息完毕
25-09-07.15:14:51.657 [main            ] INFO  AiClientApiNode        - AIClient装配链---AiClientApiNode---开始装配API
25-09-07.15:14:51.664 [main            ] INFO  AiClientMcpNode        - AIClient装配链---AiClientMcpNode---开始装配MCP
25-09-07.15:14:51.977 [HttpClient-10-Worker-0] INFO  McpAsyncClient         - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[completions=null, experimental={}, logging=null, prompts=PromptCapabilities[listChanged=false], resources=ResourceCapabilities[subscribe=false, listChanged=false], tools=ToolCapabilities[listChanged=false]], Info: Implementation[name=AIsearch, version=1.4.1] and Instructions null
25-09-07.15:14:52.056 [main            ] INFO  AiClientMcpNode        - sse方式注册mcp: baidu-search 成功
25-09-07.15:14:52.056 [main            ] INFO  AiClientModelNode      - AIClient装配链---AiClientModelNode---开始装配Model
25-09-07.15:14:52.056 [main            ] INFO  AiClientModelNode      - 注册模型-modelId:2001, modelName:deepseek-v3-成功
25-09-07.15:14:52.056 [main            ] INFO  AiClientAdvisorNode    - AIClient装配链---AiClientAdvisorNode---开始装配顾问
25-09-07.15:14:52.068 [main            ] INFO  AiClientAdvisorNode    - 注册顾问---advisorId:4001, advisorName:记忆---成功
25-09-07.15:14:52.068 [main            ] INFO  AiClientNode           - AIClient装配链---AiClientNode---开始装配Client
25-09-07.15:14:52.152 [main            ] INFO  AiClientNode           - 注册Client---clientId:3101, clientName:任务分析和状态判断---成功
25-09-07.15:14:52.213 [main            ] INFO  AiClientNode           - 注册Client---clientId:3102, clientName:具体任务执行---成功
25-09-07.15:14:52.273 [main            ] INFO  AiClientNode           - 注册Client---clientId:3103, clientName:质量检查和优化---成功
25-09-07.15:14:52.273 [main            ] INFO  AgentAutoLoadConfig    - 自动装配AI客户端完成
25-09-07.15:14:52.912 [main            ] INFO  RootNode               - AIClient装配链---RootNode---开始查询配置信息
25-09-07.15:14:52.912 [pool-2-thread-7 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_api) [3101, 3102, 3103]
25-09-07.15:14:52.913 [pool-2-thread-8 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_model) [3101, 3102, 3103]
25-09-07.15:14:52.913 [pool-2-thread-9 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_tool_mcp) [3101, 3102, 3103]
25-09-07.15:14:52.913 [pool-2-thread-10] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_system_prompt) [3101, 3102, 3103]
25-09-07.15:14:52.913 [pool-2-thread-11] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_advisor) [3101, 3102, 3103]
25-09-07.15:14:52.913 [pool-2-thread-12] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client) [3101, 3102, 3103]
25-09-07.15:14:52.927 [main            ] INFO  RootNode               - AIClient装配链---RootNode---查询配置信息完毕
25-09-07.15:14:52.927 [main            ] INFO  AiClientApiNode        - AIClient装配链---AiClientApiNode---开始装配API
25-09-07.15:14:52.933 [main            ] INFO  AiClientMcpNode        - AIClient装配链---AiClientMcpNode---开始装配MCP
25-09-07.15:14:53.103 [HttpClient-13-Worker-0] INFO  McpAsyncClient         - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[completions=null, experimental={}, logging=null, prompts=PromptCapabilities[listChanged=false], resources=ResourceCapabilities[subscribe=false, listChanged=false], tools=ToolCapabilities[listChanged=false]], Info: Implementation[name=AIsearch, version=1.4.1] and Instructions null
25-09-07.15:14:53.162 [main            ] INFO  AiClientMcpNode        - sse方式注册mcp: baidu-search 成功
25-09-07.15:14:53.162 [main            ] INFO  AiClientModelNode      - AIClient装配链---AiClientModelNode---开始装配Model
25-09-07.15:14:53.162 [main            ] INFO  AiClientModelNode      - 注册模型-modelId:2001, modelName:deepseek-v3-成功
25-09-07.15:14:53.162 [main            ] INFO  AiClientAdvisorNode    - AIClient装配链---AiClientAdvisorNode---开始装配顾问
25-09-07.15:14:53.163 [main            ] INFO  AiClientAdvisorNode    - 注册顾问---advisorId:4001, advisorName:记忆---成功
25-09-07.15:14:53.163 [main            ] INFO  AiClientNode           - AIClient装配链---AiClientNode---开始装配Client
25-09-07.15:14:53.229 [main            ] INFO  AiClientNode           - 注册Client---clientId:3101, clientName:任务分析和状态判断---成功
25-09-07.15:14:53.288 [main            ] INFO  AiClientNode           - 注册Client---clientId:3102, clientName:具体任务执行---成功
25-09-07.15:14:53.367 [main            ] INFO  AiClientNode           - 注册Client---clientId:3103, clientName:质量检查和优化---成功
25-09-07.15:14:53.368 [main            ] INFO  StreamAutoAgentTest    - 客户端构建:org.springframework.ai.chat.client.DefaultChatClient@5f72f623
25-09-07.15:14:53.368 [main            ] INFO  StreamAutoAgentTest    - 开始测试流式AutoAgent
25-09-07.15:14:53.379 [main            ] INFO  AutoExecuteStrategy    - 开始执行AutoAgent流式任务，会话ID: stream-session-1757229293368
25-09-07.15:14:53.379 [main            ] INFO  StreamAutoAgentTest    - 收到流式内容: 🚀 === AutoAgent执行开始 ===

25-09-07.15:14:53.386 [main            ] INFO  RootNode               - === 动态多轮执行测试开始 ====
25-09-07.15:14:53.386 [main            ] INFO  RootNode               - 用户输入: 请帮我分析一下人工智能的发展趋势，并给出学习建议
25-09-07.15:14:53.386 [main            ] INFO  RootNode               - 最大执行步数: 2
25-09-07.15:14:53.386 [main            ] INFO  RootNode               - 会话ID: stream-session-1757229293368
25-09-07.15:14:53.386 [main            ] INFO  StreamAutoAgentTest    - 收到流式内容: 📋 === 动态多轮执行开始 ===
👤 用户输入: 请帮我分析一下人工智能的发展趋势，并给出学习建议
🔢 最大执行步数: 2
🆔 会话ID: stream-session-1757229293368


25-09-07.15:14:53.386 [main            ] INFO  Step1TaskAnalyzerClient - === 动态多轮执行第1步 Step1 ====
25-09-07.15:14:53.387 [main            ] INFO  StreamAutoAgentTest    - 收到流式内容: 
🚀 === 开始执行 Step1-任务分析 (第1步) ===

25-09-07.15:14:53.387 [main            ] INFO  Step1TaskAnalyzerClient - 
📊 阶段1: 任务状态分析
25-09-07.15:14:53.503 [boundedElastic-1] ERROR MessageAggregator      - Aggregation Error
java.lang.IllegalStateException: No StreamAdvisors available to execute
	at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextStream$6(DefaultAroundAdvisorChain.java:119)
	at reactor.core.publisher.FluxDeferContextual.subscribe(FluxDeferContextual.java:49)
	at reactor.core.publisher.Flux.subscribe(Flux.java:8891)
	at reactor.core.publisher.MonoFlatMapMany$FlatMapManyMain.onNext(MonoFlatMapMany.java:196)
	at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)
	at reactor.core.publisher.FluxSubscribeOnValue$ScheduledScalar.run(FluxSubscribeOnValue.java:181)
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:68)
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:28)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
25-09-07.15:14:53.504 [boundedElastic-1] ERROR StreamContextManager   - 流片段Step1-分析流执行出错: No StreamAdvisors available to execute
java.lang.IllegalStateException: No StreamAdvisors available to execute
	at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextStream$6(DefaultAroundAdvisorChain.java:119)
	at reactor.core.publisher.FluxDeferContextual.subscribe(FluxDeferContextual.java:49)
	at reactor.core.publisher.Flux.subscribe(Flux.java:8891)
	at reactor.core.publisher.MonoFlatMapMany$FlatMapManyMain.onNext(MonoFlatMapMany.java:196)
	at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)
	at reactor.core.publisher.FluxSubscribeOnValue$ScheduledScalar.run(FluxSubscribeOnValue.java:181)
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:68)
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:28)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
25-09-07.15:14:53.505 [boundedElastic-1] ERROR StreamAutoAgentTest    - 流式执行出错
java.lang.IllegalStateException: No StreamAdvisors available to execute
	at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextStream$6(DefaultAroundAdvisorChain.java:119)
	at reactor.core.publisher.FluxDeferContextual.subscribe(FluxDeferContextual.java:49)
	at reactor.core.publisher.Flux.subscribe(Flux.java:8891)
	at reactor.core.publisher.MonoFlatMapMany$FlatMapManyMain.onNext(MonoFlatMapMany.java:196)
	at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)
	at reactor.core.publisher.FluxSubscribeOnValue$ScheduledScalar.run(FluxSubscribeOnValue.java:181)
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:68)
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:28)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
25-09-07.15:14:53.505 [boundedElastic-1] ERROR Operators              - Operator called default onErrorDropped
reactor.core.Exceptions$ErrorCallbackNotImplemented: java.lang.IllegalStateException: No StreamAdvisors available to execute
Caused by: java.lang.IllegalStateException: No StreamAdvisors available to execute
	at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextStream$6(DefaultAroundAdvisorChain.java:119)
	at reactor.core.publisher.FluxDeferContextual.subscribe(FluxDeferContextual.java:49)
	at reactor.core.publisher.Flux.subscribe(Flux.java:8891)
	at reactor.core.publisher.MonoFlatMapMany$FlatMapManyMain.onNext(MonoFlatMapMany.java:196)
	at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)
	at reactor.core.publisher.FluxSubscribeOnValue$ScheduledScalar.run(FluxSubscribeOnValue.java:181)
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:68)
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:28)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
25-09-07.15:14:53.505 [boundedElastic-1] ERROR Operators              - Operator called default onErrorDropped
reactor.core.Exceptions$ErrorCallbackNotImplemented: java.lang.IllegalStateException: No StreamAdvisors available to execute
Caused by: java.lang.IllegalStateException: No StreamAdvisors available to execute
	at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextStream$6(DefaultAroundAdvisorChain.java:119)
	at reactor.core.publisher.FluxDeferContextual.subscribe(FluxDeferContextual.java:49)
	at reactor.core.publisher.Flux.subscribe(Flux.java:8891)
	at reactor.core.publisher.MonoFlatMapMany$FlatMapManyMain.onNext(MonoFlatMapMany.java:196)
	at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)
	at reactor.core.publisher.FluxSubscribeOnValue$ScheduledScalar.run(FluxSubscribeOnValue.java:181)
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:68)
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:28)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
25-09-07.15:14:53.641 [ForkJoinPool.commonPool-worker-1] ERROR MessageAggregator      - Aggregation Error
org.springframework.web.reactive.function.client.WebClientRequestException: null
	at org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:137)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Request to POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions [DefaultWebClient]
Original Stack Trace:
		at org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:137)
		at reactor.core.publisher.MonoErrorSupplied.subscribe(MonoErrorSupplied.java:55)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onError(FluxPeek.java:222)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onError(FluxPeek.java:222)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onError(MonoIgnoreThen.java:280)
		at reactor.core.publisher.FluxMap$MapSubscriber.onError(FluxMap.java:134)
		at reactor.core.publisher.MonoCompletionStage$MonoCompletionStageSubscription.apply(MonoCompletionStage.java:115)
		at reactor.core.publisher.MonoCompletionStage$MonoCompletionStageSubscription.apply(MonoCompletionStage.java:67)
		at java.base/java.util.concurrent.CompletableFuture.uniHandle(CompletableFuture.java:934)
		at java.base/java.util.concurrent.CompletableFuture$UniHandle.tryFire(CompletableFuture.java:911)
		at java.base/java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:510)
		at java.base/java.util.concurrent.CompletableFuture.postFire(CompletableFuture.java:614)
		at java.base/java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:844)
		at java.base/java.util.concurrent.CompletableFuture$Completion.exec(CompletableFuture.java:483)
		at java.base/java.util.concurrent.ForkJoinTask.doExec$$$capture(ForkJoinTask.java:373)
		at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java)
		at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1182)
		at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1655)
		at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1622)
		at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:165)
Caused by: java.net.ConnectException: null
	at java.net.http/jdk.internal.net.http.common.Utils.toConnectException(Utils.java:1055)
	at java.net.http/jdk.internal.net.http.PlainHttpConnection.connectAsync(PlainHttpConnection.java:198)
	at java.net.http/jdk.internal.net.http.AsyncSSLConnection.connectAsync(AsyncSSLConnection.java:56)
	at java.net.http/jdk.internal.net.http.Http2Connection.createAsync(Http2Connection.java:378)
	at java.net.http/jdk.internal.net.http.Http2ClientImpl.getConnectionFor(Http2ClientImpl.java:126)
	at java.net.http/jdk.internal.net.http.ExchangeImpl.get(ExchangeImpl.java:93)
	at java.net.http/jdk.internal.net.http.Exchange.establishExchange(Exchange.java:344)
	at java.net.http/jdk.internal.net.http.Exchange.responseAsyncImpl0(Exchange.java:528)
	at java.net.http/jdk.internal.net.http.Exchange.responseAsyncImpl(Exchange.java:381)
	at java.net.http/jdk.internal.net.http.Exchange.responseAsync(Exchange.java:373)
	at java.net.http/jdk.internal.net.http.MultiExchange.responseAsyncImpl(MultiExchange.java:408)
	at java.net.http/jdk.internal.net.http.MultiExchange.lambda$responseAsyncImpl$7(MultiExchange.java:449)
	at java.base/java.util.concurrent.CompletableFuture.uniHandle(CompletableFuture.java:934)
	at java.base/java.util.concurrent.CompletableFuture.uniHandleStage(CompletableFuture.java:950)
	at java.base/java.util.concurrent.CompletableFuture.handle(CompletableFuture.java:2340)
	at java.net.http/jdk.internal.net.http.MultiExchange.responseAsyncImpl(MultiExchange.java:439)
	at java.net.http/jdk.internal.net.http.MultiExchange.lambda$responseAsync0$2(MultiExchange.java:341)
	at java.base/java.util.concurrent.CompletableFuture$UniCompose.tryFire(CompletableFuture.java:1150)
	at java.base/java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:510)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run$$$capture(CompletableFuture.java:1773)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.nio.channels.UnresolvedAddressException: null
	at java.base/sun.nio.ch.Net.checkAddress(Net.java:149)
	at java.base/sun.nio.ch.Net.checkAddress(Net.java:157)
	at java.base/sun.nio.ch.SocketChannelImpl.checkRemote(SocketChannelImpl.java:816)
	at java.base/sun.nio.ch.SocketChannelImpl.connect(SocketChannelImpl.java:839)
	at java.net.http/jdk.internal.net.http.PlainHttpConnection.lambda$connectAsync$0(PlainHttpConnection.java:183)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:569)
	at java.net.http/jdk.internal.net.http.PlainHttpConnection.connectAsync(PlainHttpConnection.java:185)
	... 22 common frames omitted
25-09-07.15:14:53.643 [main            ] ERROR AutoExecuteStrategy    - AutoAgent流式任务执行异常，会话ID: stream-session-1757229293368
org.springframework.web.reactive.function.client.WebClientRequestException: null
	at org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:137)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Request to POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions [DefaultWebClient]
Original Stack Trace:
		at org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:137)
		at reactor.core.publisher.MonoErrorSupplied.subscribe(MonoErrorSupplied.java:55)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onError(FluxPeek.java:222)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onError(FluxPeek.java:222)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onError(MonoIgnoreThen.java:280)
		at reactor.core.publisher.FluxMap$MapSubscriber.onError(FluxMap.java:134)
		at reactor.core.publisher.MonoCompletionStage$MonoCompletionStageSubscription.apply(MonoCompletionStage.java:115)
		at reactor.core.publisher.MonoCompletionStage$MonoCompletionStageSubscription.apply(MonoCompletionStage.java:67)
		at java.base/java.util.concurrent.CompletableFuture.uniHandle(CompletableFuture.java:934)
		at java.base/java.util.concurrent.CompletableFuture$UniHandle.tryFire(CompletableFuture.java:911)
		at java.base/java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:510)
		at java.base/java.util.concurrent.CompletableFuture.postFire(CompletableFuture.java:614)
		at java.base/java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:844)
		at java.base/java.util.concurrent.CompletableFuture$Completion.exec(CompletableFuture.java:483)
		at java.base/java.util.concurrent.ForkJoinTask.doExec$$$capture(ForkJoinTask.java:373)
		at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java)
		at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1182)
		at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1655)
		at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1622)
		at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:165)
	Suppressed: java.lang.Exception: #block terminated with an error
		at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:104)
		at reactor.core.publisher.Mono.block(Mono.java:1779)
		at com.flyxy.ai.domain.agent.service.exec.auto.step.Step1TaskAnalyzerClient.doApply(Step1TaskAnalyzerClient.java:84)
		at com.flyxy.ai.domain.agent.service.exec.auto.step.Step1TaskAnalyzerClient.doApply(Step1TaskAnalyzerClient.java:25)
		at cn.bugstack.wrench.design.framework.tree.AbstractMultiThreadStrategyRouter.apply(AbstractMultiThreadStrategyRouter.java:31)
		at cn.bugstack.wrench.design.framework.tree.AbstractMultiThreadStrategyRouter.router(AbstractMultiThreadStrategyRouter.java:22)
		at com.flyxy.ai.domain.agent.service.exec.auto.step.RootNode.doApply(RootNode.java:62)
		at com.flyxy.ai.domain.agent.service.exec.auto.step.RootNode.doApply(RootNode.java:20)
		at cn.bugstack.wrench.design.framework.tree.AbstractMultiThreadStrategyRouter.apply(AbstractMultiThreadStrategyRouter.java:31)
		at com.flyxy.ai.domain.agent.service.exec.auto.AutoExecuteStrategy.execute(AutoExecuteStrategy.java:56)
		at com.flyxy.ai.test.ai.StreamAutoAgentTest.lambda$testStreamAutoAgent$0(StreamAutoAgentTest.java:76)
		at reactor.core.publisher.FluxCreate.subscribe(FluxCreate.java:97)
		at reactor.core.publisher.Flux.subscribe(Flux.java:8891)
		at reactor.core.publisher.Flux.subscribeWith(Flux.java:9012)
		at reactor.core.publisher.Flux.subscribe(Flux.java:8856)
		at reactor.core.publisher.Flux.subscribe(Flux.java:8780)
		at reactor.core.publisher.Flux.subscribe(Flux.java:8698)
		at com.flyxy.ai.test.ai.StreamAutoAgentTest.testStreamAutoAgent(StreamAutoAgentTest.java:97)
		at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
		at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
		at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
		at java.base/java.lang.reflect.Method.invoke(Method.java:568)
		at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
		at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
		at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
		at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
		at org.springframework.test.context.junit4.statements.RunBeforeTestExecutionCallbacks.evaluate(RunBeforeTestExecutionCallbacks.java:76)
		at org.springframework.test.context.junit4.statements.RunAfterTestExecutionCallbacks.evaluate(RunAfterTestExecutionCallbacks.java:84)
		at org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:26)
		at org.springframework.test.context.junit4.statements.RunBeforeTestMethodCallbacks.evaluate(RunBeforeTestMethodCallbacks.java:75)
		at org.springframework.test.context.junit4.statements.RunAfterTestMethodCallbacks.evaluate(RunAfterTestMethodCallbacks.java:86)
		at org.springframework.test.context.junit4.statements.SpringRepeat.evaluate(SpringRepeat.java:84)
		at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
		at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:252)
		at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:97)
		at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
		at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
		at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
		at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
		at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
		at org.springframework.test.context.junit4.statements.RunBeforeTestClassCallbacks.evaluate(RunBeforeTestClassCallbacks.java:61)
		at org.springframework.test.context.junit4.statements.RunAfterTestClassCallbacks.evaluate(RunAfterTestClassCallbacks.java:70)
		at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
		at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
		at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.run(SpringJUnit4ClassRunner.java:191)
		at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
		at com.intellij.junit4.JUnit4IdeaTestRunner.startRunnerWithArgs(JUnit4IdeaTestRunner.java:69)
		at com.intellij.rt.junit.IdeaTestRunner$Repeater$1.execute(IdeaTestRunner.java:38)
		at com.intellij.rt.execution.junit.TestsRepeater.repeat(TestsRepeater.java:11)
		at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:35)
		at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:231)
		at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:55)
Caused by: java.net.ConnectException: null
	at java.net.http/jdk.internal.net.http.common.Utils.toConnectException(Utils.java:1055)
	at java.net.http/jdk.internal.net.http.PlainHttpConnection.connectAsync(PlainHttpConnection.java:198)
	at java.net.http/jdk.internal.net.http.AsyncSSLConnection.connectAsync(AsyncSSLConnection.java:56)
	at java.net.http/jdk.internal.net.http.Http2Connection.createAsync(Http2Connection.java:378)
	at java.net.http/jdk.internal.net.http.Http2ClientImpl.getConnectionFor(Http2ClientImpl.java:126)
	at java.net.http/jdk.internal.net.http.ExchangeImpl.get(ExchangeImpl.java:93)
	at java.net.http/jdk.internal.net.http.Exchange.establishExchange(Exchange.java:344)
	at java.net.http/jdk.internal.net.http.Exchange.responseAsyncImpl0(Exchange.java:528)
	at java.net.http/jdk.internal.net.http.Exchange.responseAsyncImpl(Exchange.java:381)
	at java.net.http/jdk.internal.net.http.Exchange.responseAsync(Exchange.java:373)
	at java.net.http/jdk.internal.net.http.MultiExchange.responseAsyncImpl(MultiExchange.java:408)
	at java.net.http/jdk.internal.net.http.MultiExchange.lambda$responseAsyncImpl$7(MultiExchange.java:449)
	at java.base/java.util.concurrent.CompletableFuture.uniHandle(CompletableFuture.java:934)
	at java.base/java.util.concurrent.CompletableFuture.uniHandleStage(CompletableFuture.java:950)
	at java.base/java.util.concurrent.CompletableFuture.handle(CompletableFuture.java:2340)
	at java.net.http/jdk.internal.net.http.MultiExchange.responseAsyncImpl(MultiExchange.java:439)
	at java.net.http/jdk.internal.net.http.MultiExchange.lambda$responseAsync0$2(MultiExchange.java:341)
	at java.base/java.util.concurrent.CompletableFuture$UniCompose.tryFire(CompletableFuture.java:1150)
	at java.base/java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:510)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run$$$capture(CompletableFuture.java:1773)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.nio.channels.UnresolvedAddressException: null
	at java.base/sun.nio.ch.Net.checkAddress(Net.java:149)
	at java.base/sun.nio.ch.Net.checkAddress(Net.java:157)
	at java.base/sun.nio.ch.SocketChannelImpl.checkRemote(SocketChannelImpl.java:816)
	at java.base/sun.nio.ch.SocketChannelImpl.connect(SocketChannelImpl.java:839)
	at java.net.http/jdk.internal.net.http.PlainHttpConnection.lambda$connectAsync$0(PlainHttpConnection.java:183)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:569)
	at java.net.http/jdk.internal.net.http.PlainHttpConnection.connectAsync(PlainHttpConnection.java:185)
	... 22 common frames omitted
25-09-07.15:14:53.644 [main            ] INFO  StreamAutoAgentTest    - 测试完成，总结果长度: 165
25-09-07.15:14:53.644 [main            ] INFO  StreamAutoAgentTest    - 完整结果: 
🚀 === AutoAgent执行开始 ===
📋 === 动态多轮执行开始 ===
👤 用户输入: 请帮我分析一下人工智能的发展趋势，并给出学习建议
🔢 最大执行步数: 2
🆔 会话ID: stream-session-1757229293368


🚀 === 开始执行 Step1-任务分析 (第1步) ===

25-09-07.15:14:53.660 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MainHikariPool - Shutdown initiated...
25-09-07.15:14:53.678 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MainHikariPool - Shutdown completed.
25-09-07.15:23:55.459 [main            ] INFO  StreamAutoAgentTest    - Starting StreamAutoAgentTest using Java 17.0.12 with PID 38624 (started by 30562 in D:\ACode\project\ai-agent\ai-agent-app)
25-09-07.15:23:55.461 [main            ] INFO  StreamAutoAgentTest    - The following 1 profile is active: "dev"
25-09-07.15:23:58.317 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_openai. Is empty: false
25-09-07.15:23:58.322 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_openai in schema: public
25-09-07.15:23:58.322 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-09-07.15:23:58.639 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-09-07.15:23:59.163 [main            ] INFO  StreamAutoAgentTest    - Started StreamAutoAgentTest in 4.252 seconds (process running for 5.579)
25-09-07.15:23:59.166 [main            ] INFO  AgentAutoLoadConfig    - 开始自动加载Agent
25-09-07.15:23:59.166 [main            ] INFO  AgentAutoLoadConfig    - 开始自动装配AI客户端，客户端ID列表: [3101, 3102, 3103]
25-09-07.15:23:59.168 [main            ] INFO  RootNode               - AIClient装配链---RootNode---开始查询配置信息
25-09-07.15:23:59.171 [pool-2-thread-1 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_api) [3101, 3102, 3103]
25-09-07.15:23:59.171 [pool-2-thread-2 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_model) [3101, 3102, 3103]
25-09-07.15:23:59.171 [pool-2-thread-3 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_tool_mcp) [3101, 3102, 3103]
25-09-07.15:23:59.172 [pool-2-thread-4 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_system_prompt) [3101, 3102, 3103]
25-09-07.15:23:59.172 [pool-2-thread-5 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_advisor) [3101, 3102, 3103]
25-09-07.15:23:59.173 [pool-2-thread-6 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client) [3101, 3102, 3103]
25-09-07.15:23:59.195 [pool-2-thread-2 ] INFO  HikariDataSource       - MainHikariPool - Starting...
25-09-07.15:23:59.458 [pool-2-thread-2 ] INFO  HikariPool             - MainHikariPool - Added connection com.mysql.cj.jdbc.ConnectionImpl@1d843b4f
25-09-07.15:23:59.460 [pool-2-thread-2 ] INFO  HikariDataSource       - MainHikariPool - Start completed.
25-09-07.15:23:59.697 [main            ] INFO  RootNode               - AIClient装配链---RootNode---查询配置信息完毕
25-09-07.15:23:59.697 [main            ] INFO  AiClientApiNode        - AIClient装配链---AiClientApiNode---开始装配API
25-09-07.15:23:59.704 [main            ] INFO  AiClientMcpNode        - AIClient装配链---AiClientMcpNode---开始装配MCP
25-09-07.15:24:00.073 [HttpClient-10-Worker-1] INFO  McpAsyncClient         - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[completions=null, experimental={}, logging=null, prompts=PromptCapabilities[listChanged=false], resources=ResourceCapabilities[subscribe=false, listChanged=false], tools=ToolCapabilities[listChanged=false]], Info: Implementation[name=AIsearch, version=1.4.1] and Instructions null
25-09-07.15:24:00.194 [main            ] INFO  AiClientMcpNode        - sse方式注册mcp: baidu-search 成功
25-09-07.15:24:00.195 [main            ] INFO  AiClientModelNode      - AIClient装配链---AiClientModelNode---开始装配Model
25-09-07.15:24:00.195 [main            ] INFO  AiClientModelNode      - 注册模型-modelId:2001, modelName:deepseek-v3-成功
25-09-07.15:24:00.195 [main            ] INFO  AiClientAdvisorNode    - AIClient装配链---AiClientAdvisorNode---开始装配顾问
25-09-07.15:24:00.207 [main            ] INFO  AiClientAdvisorNode    - 注册顾问---advisorId:4001, advisorName:记忆---成功
25-09-07.15:24:00.207 [main            ] INFO  AiClientNode           - AIClient装配链---AiClientNode---开始装配Client
25-09-07.15:24:00.281 [main            ] INFO  AiClientNode           - 注册Client---clientId:3101, clientName:任务分析和状态判断---成功
25-09-07.15:24:00.328 [main            ] INFO  AiClientNode           - 注册Client---clientId:3102, clientName:具体任务执行---成功
25-09-07.15:24:00.381 [main            ] INFO  AiClientNode           - 注册Client---clientId:3103, clientName:质量检查和优化---成功
25-09-07.15:24:00.381 [main            ] INFO  AgentAutoLoadConfig    - 自动装配AI客户端完成
25-09-07.15:24:01.011 [main            ] INFO  StreamAutoAgentTest    - 开始测试流式AutoAgent
25-09-07.15:24:01.021 [main            ] INFO  AutoExecuteStrategy    - 开始执行AutoAgent流式任务，会话ID: stream-session-1757229841011
25-09-07.15:24:01.021 [main            ] INFO  StreamAutoAgentTest    - 收到流式内容: 🚀 === AutoAgent执行开始 ===

25-09-07.15:24:01.026 [main            ] INFO  RootNode               - === 动态多轮执行测试开始 ====
25-09-07.15:24:01.027 [main            ] INFO  RootNode               - 用户输入: 请帮我分析一下人工智能的发展趋势，并给出学习建议
25-09-07.15:24:01.027 [main            ] INFO  RootNode               - 最大执行步数: 2
25-09-07.15:24:01.027 [main            ] INFO  RootNode               - 会话ID: stream-session-1757229841011
25-09-07.15:24:01.027 [main            ] INFO  StreamAutoAgentTest    - 收到流式内容: 📋 === 动态多轮执行开始 ===
👤 用户输入: 请帮我分析一下人工智能的发展趋势，并给出学习建议
🔢 最大执行步数: 2
🆔 会话ID: stream-session-1757229841011


25-09-07.15:24:01.027 [main            ] INFO  Step1TaskAnalyzerClient - === 动态多轮执行第1步 Step1 ====
25-09-07.15:24:01.028 [main            ] INFO  StreamAutoAgentTest    - 收到流式内容: 
🚀 === 开始执行 Step1-任务分析 (第1步) ===

25-09-07.15:24:01.028 [main            ] INFO  Step1TaskAnalyzerClient - 
📊 阶段1: 任务状态分析
25-09-07.15:24:01.116 [boundedElastic-1] ERROR MessageAggregator      - Aggregation Error
java.lang.IllegalStateException: No StreamAdvisors available to execute
	at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextStream$6(DefaultAroundAdvisorChain.java:119)
	at reactor.core.publisher.FluxDeferContextual.subscribe(FluxDeferContextual.java:49)
	at reactor.core.publisher.Flux.subscribe(Flux.java:8891)
	at reactor.core.publisher.MonoFlatMapMany$FlatMapManyMain.onNext(MonoFlatMapMany.java:196)
	at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)
	at reactor.core.publisher.FluxSubscribeOnValue$ScheduledScalar.run(FluxSubscribeOnValue.java:181)
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:68)
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:28)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
25-09-07.15:24:01.117 [boundedElastic-1] ERROR StreamContextManager   - 流片段Step1-分析流执行出错: No StreamAdvisors available to execute
java.lang.IllegalStateException: No StreamAdvisors available to execute
	at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextStream$6(DefaultAroundAdvisorChain.java:119)
	at reactor.core.publisher.FluxDeferContextual.subscribe(FluxDeferContextual.java:49)
	at reactor.core.publisher.Flux.subscribe(Flux.java:8891)
	at reactor.core.publisher.MonoFlatMapMany$FlatMapManyMain.onNext(MonoFlatMapMany.java:196)
	at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)
	at reactor.core.publisher.FluxSubscribeOnValue$ScheduledScalar.run(FluxSubscribeOnValue.java:181)
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:68)
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:28)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
25-09-07.15:24:01.118 [boundedElastic-1] ERROR StreamAutoAgentTest    - 流式执行出错
java.lang.IllegalStateException: No StreamAdvisors available to execute
	at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextStream$6(DefaultAroundAdvisorChain.java:119)
	at reactor.core.publisher.FluxDeferContextual.subscribe(FluxDeferContextual.java:49)
	at reactor.core.publisher.Flux.subscribe(Flux.java:8891)
	at reactor.core.publisher.MonoFlatMapMany$FlatMapManyMain.onNext(MonoFlatMapMany.java:196)
	at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)
	at reactor.core.publisher.FluxSubscribeOnValue$ScheduledScalar.run(FluxSubscribeOnValue.java:181)
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:68)
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:28)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
25-09-07.15:24:01.118 [boundedElastic-1] ERROR Operators              - Operator called default onErrorDropped
reactor.core.Exceptions$ErrorCallbackNotImplemented: java.lang.IllegalStateException: No StreamAdvisors available to execute
Caused by: java.lang.IllegalStateException: No StreamAdvisors available to execute
	at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextStream$6(DefaultAroundAdvisorChain.java:119)
	at reactor.core.publisher.FluxDeferContextual.subscribe(FluxDeferContextual.java:49)
	at reactor.core.publisher.Flux.subscribe(Flux.java:8891)
	at reactor.core.publisher.MonoFlatMapMany$FlatMapManyMain.onNext(MonoFlatMapMany.java:196)
	at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)
	at reactor.core.publisher.FluxSubscribeOnValue$ScheduledScalar.run(FluxSubscribeOnValue.java:181)
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:68)
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:28)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
25-09-07.15:24:01.118 [boundedElastic-1] ERROR Operators              - Operator called default onErrorDropped
reactor.core.Exceptions$ErrorCallbackNotImplemented: java.lang.IllegalStateException: No StreamAdvisors available to execute
Caused by: java.lang.IllegalStateException: No StreamAdvisors available to execute
	at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextStream$6(DefaultAroundAdvisorChain.java:119)
	at reactor.core.publisher.FluxDeferContextual.subscribe(FluxDeferContextual.java:49)
	at reactor.core.publisher.Flux.subscribe(Flux.java:8891)
	at reactor.core.publisher.MonoFlatMapMany$FlatMapManyMain.onNext(MonoFlatMapMany.java:196)
	at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)
	at reactor.core.publisher.FluxSubscribeOnValue$ScheduledScalar.run(FluxSubscribeOnValue.java:181)
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:68)
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:28)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
25-09-07.15:24:10.720 [main            ] INFO  Step2PrecisionExecutorClient - === 动态多轮执行第1步 Step2 ====
25-09-07.15:24:10.720 [main            ] INFO  Step2PrecisionExecutorClient - 
📊 阶段2: 精准任务执行
25-09-07.15:24:10.724 [boundedElastic-1] ERROR MessageAggregator      - Aggregation Error
java.lang.IllegalStateException: No StreamAdvisors available to execute
	at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextStream$6(DefaultAroundAdvisorChain.java:119)
	at reactor.core.publisher.FluxDeferContextual.subscribe(FluxDeferContextual.java:49)
	at reactor.core.publisher.Flux.subscribe(Flux.java:8891)
	at reactor.core.publisher.MonoFlatMapMany$FlatMapManyMain.onNext(MonoFlatMapMany.java:196)
	at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)
	at reactor.core.publisher.FluxSubscribeOnValue$ScheduledScalar.run(FluxSubscribeOnValue.java:181)
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:68)
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:28)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
25-09-07.15:24:10.724 [boundedElastic-1] ERROR StreamContextManager   - 流片段Step2-执行流执行出错: No StreamAdvisors available to execute
java.lang.IllegalStateException: No StreamAdvisors available to execute
	at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextStream$6(DefaultAroundAdvisorChain.java:119)
	at reactor.core.publisher.FluxDeferContextual.subscribe(FluxDeferContextual.java:49)
	at reactor.core.publisher.Flux.subscribe(Flux.java:8891)
	at reactor.core.publisher.MonoFlatMapMany$FlatMapManyMain.onNext(MonoFlatMapMany.java:196)
	at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)
	at reactor.core.publisher.FluxSubscribeOnValue$ScheduledScalar.run(FluxSubscribeOnValue.java:181)
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:68)
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:28)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
25-09-07.15:24:10.724 [boundedElastic-1] ERROR Operators              - Operator called default onErrorDropped
reactor.core.Exceptions$ErrorCallbackNotImplemented: java.lang.IllegalStateException: No StreamAdvisors available to execute
Caused by: java.lang.IllegalStateException: No StreamAdvisors available to execute
	at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextStream$6(DefaultAroundAdvisorChain.java:119)
	at reactor.core.publisher.FluxDeferContextual.subscribe(FluxDeferContextual.java:49)
	at reactor.core.publisher.Flux.subscribe(Flux.java:8891)
	at reactor.core.publisher.MonoFlatMapMany$FlatMapManyMain.onNext(MonoFlatMapMany.java:196)
	at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)
	at reactor.core.publisher.FluxSubscribeOnValue$ScheduledScalar.run(FluxSubscribeOnValue.java:181)
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:68)
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:28)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
25-09-07.15:24:22.710 [boundedElastic-1] ERROR MessageAggregator      - Aggregation Error
java.lang.IllegalStateException: Error calling tool: [TextContent[audience=null, priority=null, text=Error executing tool AIsearch: request_id=1018c75d-4c4b-49ab-b78e-2b9360e50e3a , http status code is 400, body is {"requestId":"1018c75d-4c4b-49ab-b78e-2b9360e50e3a","code":"InvalidParameter","message":"Invalid request body","detail":[{"field":"resource_type_filter","message":"Value error, Invalid type 'pdf'. Must be one of: image, video, web","type":"value_error"}]}]]
	at org.springframework.ai.mcp.SyncMcpToolCallback.call(SyncMcpToolCallback.java:118)
	at org.springframework.ai.mcp.SyncMcpToolCallback.call(SyncMcpToolCallback.java:126)
	at org.springframework.ai.model.tool.DefaultToolCallingManager.lambda$executeToolCall$5(DefaultToolCallingManager.java:224)
	at io.micrometer.observation.Observation.observe(Observation.java:564)
	at org.springframework.ai.model.tool.DefaultToolCallingManager.executeToolCall(DefaultToolCallingManager.java:221)
	at org.springframework.ai.model.tool.DefaultToolCallingManager.executeToolCalls(DefaultToolCallingManager.java:137)
	at org.springframework.ai.openai.OpenAiChatModel.lambda$internalStream$10(OpenAiChatModel.java:369)
	at reactor.core.publisher.FluxDefer.subscribe(FluxDefer.java:46)
	at reactor.core.publisher.FluxSubscribeOn$SubscribeOnSubscriber.run(FluxSubscribeOn.java:194)
	at reactor.core.scheduler.WorkerTask.call(WorkerTask.java:84)
	at reactor.core.scheduler.WorkerTask.call(WorkerTask.java:37)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
25-09-07.15:24:22.710 [main            ] ERROR AutoExecuteStrategy    - AutoAgent流式任务执行异常，会话ID: stream-session-1757229841011
java.lang.IllegalStateException: Error calling tool: [TextContent[audience=null, priority=null, text=Error executing tool AIsearch: request_id=1018c75d-4c4b-49ab-b78e-2b9360e50e3a , http status code is 400, body is {"requestId":"1018c75d-4c4b-49ab-b78e-2b9360e50e3a","code":"InvalidParameter","message":"Invalid request body","detail":[{"field":"resource_type_filter","message":"Value error, Invalid type 'pdf'. Must be one of: image, video, web","type":"value_error"}]}]]
	at org.springframework.ai.mcp.SyncMcpToolCallback.call(SyncMcpToolCallback.java:118)
	at org.springframework.ai.mcp.SyncMcpToolCallback.call(SyncMcpToolCallback.java:126)
	at org.springframework.ai.model.tool.DefaultToolCallingManager.lambda$executeToolCall$5(DefaultToolCallingManager.java:224)
	at io.micrometer.observation.Observation.observe(Observation.java:564)
	at org.springframework.ai.model.tool.DefaultToolCallingManager.executeToolCall(DefaultToolCallingManager.java:221)
	at org.springframework.ai.model.tool.DefaultToolCallingManager.executeToolCalls(DefaultToolCallingManager.java:137)
	at org.springframework.ai.openai.OpenAiChatModel.lambda$internalStream$10(OpenAiChatModel.java:369)
	at reactor.core.publisher.FluxDefer.subscribe(FluxDefer.java:46)
	at reactor.core.publisher.FluxSubscribeOn$SubscribeOnSubscriber.run(FluxSubscribeOn.java:194)
	at reactor.core.scheduler.WorkerTask.call(WorkerTask.java:84)
	at reactor.core.scheduler.WorkerTask.call(WorkerTask.java:37)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
	Suppressed: java.lang.Exception: #block terminated with an error
		at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:104)
		at reactor.core.publisher.Mono.block(Mono.java:1779)
		at com.flyxy.ai.domain.agent.service.exec.auto.step.Step2PrecisionExecutorClient.doApply(Step2PrecisionExecutorClient.java:94)
		at com.flyxy.ai.domain.agent.service.exec.auto.step.Step2PrecisionExecutorClient.doApply(Step2PrecisionExecutorClient.java:25)
		at cn.bugstack.wrench.design.framework.tree.AbstractMultiThreadStrategyRouter.apply(AbstractMultiThreadStrategyRouter.java:31)
		at cn.bugstack.wrench.design.framework.tree.AbstractMultiThreadStrategyRouter.router(AbstractMultiThreadStrategyRouter.java:22)
		at com.flyxy.ai.domain.agent.service.exec.auto.step.Step1TaskAnalyzerClient.doApply(Step1TaskAnalyzerClient.java:94)
		at com.flyxy.ai.domain.agent.service.exec.auto.step.Step1TaskAnalyzerClient.doApply(Step1TaskAnalyzerClient.java:25)
		at cn.bugstack.wrench.design.framework.tree.AbstractMultiThreadStrategyRouter.apply(AbstractMultiThreadStrategyRouter.java:31)
		at cn.bugstack.wrench.design.framework.tree.AbstractMultiThreadStrategyRouter.router(AbstractMultiThreadStrategyRouter.java:22)
		at com.flyxy.ai.domain.agent.service.exec.auto.step.RootNode.doApply(RootNode.java:62)
		at com.flyxy.ai.domain.agent.service.exec.auto.step.RootNode.doApply(RootNode.java:20)
		at cn.bugstack.wrench.design.framework.tree.AbstractMultiThreadStrategyRouter.apply(AbstractMultiThreadStrategyRouter.java:31)
		at com.flyxy.ai.domain.agent.service.exec.auto.AutoExecuteStrategy.execute(AutoExecuteStrategy.java:56)
		at com.flyxy.ai.test.ai.StreamAutoAgentTest.lambda$testStreamAutoAgent$0(StreamAutoAgentTest.java:67)
		at reactor.core.publisher.FluxCreate.subscribe(FluxCreate.java:97)
		at reactor.core.publisher.Flux.subscribe(Flux.java:8891)
		at reactor.core.publisher.Flux.subscribeWith(Flux.java:9012)
		at reactor.core.publisher.Flux.subscribe(Flux.java:8856)
		at reactor.core.publisher.Flux.subscribe(Flux.java:8780)
		at reactor.core.publisher.Flux.subscribe(Flux.java:8698)
		at com.flyxy.ai.test.ai.StreamAutoAgentTest.testStreamAutoAgent(StreamAutoAgentTest.java:88)
		at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
		at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
		at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
		at java.base/java.lang.reflect.Method.invoke(Method.java:568)
		at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
		at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
		at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
		at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
		at org.springframework.test.context.junit4.statements.RunBeforeTestExecutionCallbacks.evaluate(RunBeforeTestExecutionCallbacks.java:76)
		at org.springframework.test.context.junit4.statements.RunAfterTestExecutionCallbacks.evaluate(RunAfterTestExecutionCallbacks.java:84)
		at org.springframework.test.context.junit4.statements.RunBeforeTestMethodCallbacks.evaluate(RunBeforeTestMethodCallbacks.java:75)
		at org.springframework.test.context.junit4.statements.RunAfterTestMethodCallbacks.evaluate(RunAfterTestMethodCallbacks.java:86)
		at org.springframework.test.context.junit4.statements.SpringRepeat.evaluate(SpringRepeat.java:84)
		at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
		at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:252)
		at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:97)
		at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
		at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
		at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
		at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
		at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
		at org.springframework.test.context.junit4.statements.RunBeforeTestClassCallbacks.evaluate(RunBeforeTestClassCallbacks.java:61)
		at org.springframework.test.context.junit4.statements.RunAfterTestClassCallbacks.evaluate(RunAfterTestClassCallbacks.java:70)
		at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
		at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
		at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.run(SpringJUnit4ClassRunner.java:191)
		at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
		at com.intellij.junit4.JUnit4IdeaTestRunner.startRunnerWithArgs(JUnit4IdeaTestRunner.java:69)
		at com.intellij.rt.junit.IdeaTestRunner$Repeater$1.execute(IdeaTestRunner.java:38)
		at com.intellij.rt.execution.junit.TestsRepeater.repeat(TestsRepeater.java:11)
		at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:35)
		at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:231)
		at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:55)
25-09-07.15:24:22.710 [main            ] INFO  StreamAutoAgentTest    - 测试完成，总结果长度: 165
25-09-07.15:24:22.710 [main            ] INFO  StreamAutoAgentTest    - 完整结果: 
🚀 === AutoAgent执行开始 ===
📋 === 动态多轮执行开始 ===
👤 用户输入: 请帮我分析一下人工智能的发展趋势，并给出学习建议
🔢 最大执行步数: 2
🆔 会话ID: stream-session-1757229841011


🚀 === 开始执行 Step1-任务分析 (第1步) ===

25-09-07.15:24:22.725 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MainHikariPool - Shutdown initiated...
25-09-07.15:24:22.740 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MainHikariPool - Shutdown completed.
25-09-07.15:35:49.409 [main            ] INFO  StreamAutoAgentTest    - Starting StreamAutoAgentTest using Java 17.0.12 with PID 40948 (started by 30562 in D:\ACode\project\ai-agent\ai-agent-app)
25-09-07.15:35:49.410 [main            ] INFO  StreamAutoAgentTest    - The following 1 profile is active: "dev"
25-09-07.15:35:52.109 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_openai. Is empty: false
25-09-07.15:35:52.114 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_openai in schema: public
25-09-07.15:35:52.115 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-09-07.15:35:52.414 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-09-07.15:35:52.938 [main            ] INFO  StreamAutoAgentTest    - Started StreamAutoAgentTest in 4.07 seconds (process running for 5.329)
25-09-07.15:35:52.942 [main            ] INFO  AgentAutoLoadConfig    - 开始自动加载Agent
25-09-07.15:35:52.942 [main            ] INFO  AgentAutoLoadConfig    - 开始自动装配AI客户端，客户端ID列表: [3101, 3102, 3103]
25-09-07.15:35:52.945 [main            ] INFO  RootNode               - AIClient装配链---RootNode---开始查询配置信息
25-09-07.15:35:52.946 [pool-2-thread-1 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_api) [3101, 3102, 3103]
25-09-07.15:35:52.947 [pool-2-thread-2 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_model) [3101, 3102, 3103]
25-09-07.15:35:52.947 [pool-2-thread-3 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_tool_mcp) [3101, 3102, 3103]
25-09-07.15:35:52.947 [pool-2-thread-4 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_system_prompt) [3101, 3102, 3103]
25-09-07.15:35:52.960 [pool-2-thread-5 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_advisor) [3101, 3102, 3103]
25-09-07.15:35:52.961 [pool-2-thread-6 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client) [3101, 3102, 3103]
25-09-07.15:35:52.998 [pool-2-thread-5 ] INFO  HikariDataSource       - MainHikariPool - Starting...
25-09-07.15:35:53.252 [pool-2-thread-5 ] INFO  HikariPool             - MainHikariPool - Added connection com.mysql.cj.jdbc.ConnectionImpl@735d355d
25-09-07.15:35:53.253 [pool-2-thread-5 ] INFO  HikariDataSource       - MainHikariPool - Start completed.
25-09-07.15:35:53.500 [main            ] INFO  RootNode               - AIClient装配链---RootNode---查询配置信息完毕
25-09-07.15:35:53.500 [main            ] INFO  AiClientApiNode        - AIClient装配链---AiClientApiNode---开始装配API
25-09-07.15:35:53.507 [main            ] INFO  AiClientMcpNode        - AIClient装配链---AiClientMcpNode---开始装配MCP
25-09-07.15:35:54.195 [HttpClient-10-Worker-2] INFO  McpAsyncClient         - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[completions=null, experimental={}, logging=null, prompts=PromptCapabilities[listChanged=false], resources=ResourceCapabilities[subscribe=false, listChanged=false], tools=ToolCapabilities[listChanged=false]], Info: Implementation[name=AIsearch, version=1.4.1] and Instructions null
25-09-07.15:35:54.278 [main            ] INFO  AiClientMcpNode        - sse方式注册mcp: baidu-search 成功
25-09-07.15:35:54.279 [main            ] INFO  AiClientModelNode      - AIClient装配链---AiClientModelNode---开始装配Model
25-09-07.15:35:54.279 [main            ] INFO  AiClientModelNode      - 注册模型-modelId:2001, modelName:deepseek-v3-成功
25-09-07.15:35:54.279 [main            ] INFO  AiClientAdvisorNode    - AIClient装配链---AiClientAdvisorNode---开始装配顾问
25-09-07.15:35:54.292 [main            ] INFO  AiClientAdvisorNode    - 注册顾问---advisorId:4001, advisorName:记忆---成功
25-09-07.15:35:54.293 [main            ] INFO  AiClientNode           - AIClient装配链---AiClientNode---开始装配Client
25-09-07.15:35:54.382 [main            ] INFO  AiClientNode           - 注册Client---clientId:3101, clientName:任务分析和状态判断---成功
25-09-07.15:35:54.444 [main            ] INFO  AiClientNode           - 注册Client---clientId:3102, clientName:具体任务执行---成功
25-09-07.15:35:54.528 [main            ] INFO  AiClientNode           - 注册Client---clientId:3103, clientName:质量检查和优化---成功
25-09-07.15:35:54.528 [main            ] INFO  AgentAutoLoadConfig    - 自动装配AI客户端完成
25-09-07.15:35:55.167 [main            ] INFO  StreamAutoAgentTest    - 开始测试流式AutoAgent
25-09-07.15:35:55.177 [main            ] INFO  AutoExecuteStrategy    - 开始执行AutoAgent流式任务，会话ID: stream-session-1757230555167
25-09-07.15:35:55.177 [main            ] INFO  StreamAutoAgentTest    - 收到流式内容: 🚀 === AutoAgent执行开始 ===

25-09-07.15:35:55.182 [main            ] INFO  RootNode               - === 动态多轮执行测试开始 ====
25-09-07.15:35:55.182 [main            ] INFO  RootNode               - 用户输入: 请帮我分析一下人工智能的发展趋势，并给出学习建议
25-09-07.15:35:55.182 [main            ] INFO  RootNode               - 最大执行步数: 2
25-09-07.15:35:55.183 [main            ] INFO  RootNode               - 会话ID: stream-session-1757230555167
25-09-07.15:35:55.183 [main            ] INFO  StreamAutoAgentTest    - 收到流式内容: 📋 === 动态多轮执行开始 ===
👤 用户输入: 请帮我分析一下人工智能的发展趋势，并给出学习建议
🔢 最大执行步数: 2
🆔 会话ID: stream-session-1757230555167


25-09-07.15:35:55.183 [main            ] INFO  Step1TaskAnalyzerClient - === 动态多轮执行第1步 Step1 ====
25-09-07.15:35:55.183 [main            ] INFO  StreamAutoAgentTest    - 收到流式内容: 
🚀 === 开始执行 Step1-任务分析 (第1步) ===

25-09-07.15:35:55.183 [main            ] INFO  Step1TaskAnalyzerClient - 
📊 阶段1: 任务状态分析
25-09-07.15:35:55.270 [boundedElastic-1] ERROR MessageAggregator      - Aggregation Error
java.lang.IllegalStateException: No StreamAdvisors available to execute
	at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextStream$6(DefaultAroundAdvisorChain.java:119)
	at reactor.core.publisher.FluxDeferContextual.subscribe(FluxDeferContextual.java:49)
	at reactor.core.publisher.Flux.subscribe(Flux.java:8891)
	at reactor.core.publisher.MonoFlatMapMany$FlatMapManyMain.onNext(MonoFlatMapMany.java:196)
	at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)
	at reactor.core.publisher.FluxSubscribeOnValue$ScheduledScalar.run(FluxSubscribeOnValue.java:181)
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:68)
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:28)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
25-09-07.15:35:55.272 [boundedElastic-1] ERROR StreamContextManager   - 流片段Step1-分析流执行出错: No StreamAdvisors available to execute
java.lang.IllegalStateException: No StreamAdvisors available to execute
	at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextStream$6(DefaultAroundAdvisorChain.java:119)
	at reactor.core.publisher.FluxDeferContextual.subscribe(FluxDeferContextual.java:49)
	at reactor.core.publisher.Flux.subscribe(Flux.java:8891)
	at reactor.core.publisher.MonoFlatMapMany$FlatMapManyMain.onNext(MonoFlatMapMany.java:196)
	at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)
	at reactor.core.publisher.FluxSubscribeOnValue$ScheduledScalar.run(FluxSubscribeOnValue.java:181)
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:68)
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:28)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
25-09-07.15:35:55.272 [boundedElastic-1] ERROR StreamAutoAgentTest    - 流式执行出错
java.lang.IllegalStateException: No StreamAdvisors available to execute
	at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextStream$6(DefaultAroundAdvisorChain.java:119)
	at reactor.core.publisher.FluxDeferContextual.subscribe(FluxDeferContextual.java:49)
	at reactor.core.publisher.Flux.subscribe(Flux.java:8891)
	at reactor.core.publisher.MonoFlatMapMany$FlatMapManyMain.onNext(MonoFlatMapMany.java:196)
	at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)
	at reactor.core.publisher.FluxSubscribeOnValue$ScheduledScalar.run(FluxSubscribeOnValue.java:181)
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:68)
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:28)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
25-09-07.15:35:55.272 [boundedElastic-1] ERROR Operators              - Operator called default onErrorDropped
reactor.core.Exceptions$ErrorCallbackNotImplemented: java.lang.IllegalStateException: No StreamAdvisors available to execute
Caused by: java.lang.IllegalStateException: No StreamAdvisors available to execute
	at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextStream$6(DefaultAroundAdvisorChain.java:119)
	at reactor.core.publisher.FluxDeferContextual.subscribe(FluxDeferContextual.java:49)
	at reactor.core.publisher.Flux.subscribe(Flux.java:8891)
	at reactor.core.publisher.MonoFlatMapMany$FlatMapManyMain.onNext(MonoFlatMapMany.java:196)
	at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)
	at reactor.core.publisher.FluxSubscribeOnValue$ScheduledScalar.run(FluxSubscribeOnValue.java:181)
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:68)
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:28)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
25-09-07.15:35:55.273 [boundedElastic-1] ERROR Operators              - Operator called default onErrorDropped
reactor.core.Exceptions$ErrorCallbackNotImplemented: java.lang.IllegalStateException: No StreamAdvisors available to execute
Caused by: java.lang.IllegalStateException: No StreamAdvisors available to execute
	at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextStream$6(DefaultAroundAdvisorChain.java:119)
	at reactor.core.publisher.FluxDeferContextual.subscribe(FluxDeferContextual.java:49)
	at reactor.core.publisher.Flux.subscribe(Flux.java:8891)
	at reactor.core.publisher.MonoFlatMapMany$FlatMapManyMain.onNext(MonoFlatMapMany.java:196)
	at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)
	at reactor.core.publisher.FluxSubscribeOnValue$ScheduledScalar.run(FluxSubscribeOnValue.java:181)
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:68)
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:28)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
25-09-07.15:36:05.509 [main            ] INFO  Step2PrecisionExecutorClient - === 动态多轮执行第1步 Step2 ====
25-09-07.15:36:05.510 [main            ] INFO  Step2PrecisionExecutorClient - 
📊 阶段2: 精准任务执行
25-09-07.15:36:05.512 [boundedElastic-1] ERROR MessageAggregator      - Aggregation Error
java.lang.IllegalStateException: No StreamAdvisors available to execute
	at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextStream$6(DefaultAroundAdvisorChain.java:119)
	at reactor.core.publisher.FluxDeferContextual.subscribe(FluxDeferContextual.java:49)
	at reactor.core.publisher.Flux.subscribe(Flux.java:8891)
	at reactor.core.publisher.MonoFlatMapMany$FlatMapManyMain.onNext(MonoFlatMapMany.java:196)
	at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)
	at reactor.core.publisher.FluxSubscribeOnValue$ScheduledScalar.run(FluxSubscribeOnValue.java:181)
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:68)
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:28)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
25-09-07.15:36:05.513 [boundedElastic-1] ERROR StreamContextManager   - 流片段Step2-执行流执行出错: No StreamAdvisors available to execute
java.lang.IllegalStateException: No StreamAdvisors available to execute
	at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextStream$6(DefaultAroundAdvisorChain.java:119)
	at reactor.core.publisher.FluxDeferContextual.subscribe(FluxDeferContextual.java:49)
	at reactor.core.publisher.Flux.subscribe(Flux.java:8891)
	at reactor.core.publisher.MonoFlatMapMany$FlatMapManyMain.onNext(MonoFlatMapMany.java:196)
	at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)
	at reactor.core.publisher.FluxSubscribeOnValue$ScheduledScalar.run(FluxSubscribeOnValue.java:181)
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:68)
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:28)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
25-09-07.15:36:05.513 [boundedElastic-1] ERROR Operators              - Operator called default onErrorDropped
reactor.core.Exceptions$ErrorCallbackNotImplemented: java.lang.IllegalStateException: No StreamAdvisors available to execute
Caused by: java.lang.IllegalStateException: No StreamAdvisors available to execute
	at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextStream$6(DefaultAroundAdvisorChain.java:119)
	at reactor.core.publisher.FluxDeferContextual.subscribe(FluxDeferContextual.java:49)
	at reactor.core.publisher.Flux.subscribe(Flux.java:8891)
	at reactor.core.publisher.MonoFlatMapMany$FlatMapManyMain.onNext(MonoFlatMapMany.java:196)
	at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)
	at reactor.core.publisher.FluxSubscribeOnValue$ScheduledScalar.run(FluxSubscribeOnValue.java:181)
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:68)
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:28)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
25-09-07.15:37:21.702 [main            ] INFO  Step3PrecisionExecutorClient - === 动态多轮执行第1步 Step3 ====
25-09-07.15:37:21.702 [main            ] INFO  Step3PrecisionExecutorClient - 
🔍 阶段3: 质量监督检查
25-09-07.15:37:21.706 [boundedElastic-2] ERROR MessageAggregator      - Aggregation Error
java.lang.IllegalStateException: No StreamAdvisors available to execute
	at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextStream$6(DefaultAroundAdvisorChain.java:119)
	at reactor.core.publisher.FluxDeferContextual.subscribe(FluxDeferContextual.java:49)
	at reactor.core.publisher.Flux.subscribe(Flux.java:8891)
	at reactor.core.publisher.MonoFlatMapMany$FlatMapManyMain.onNext(MonoFlatMapMany.java:196)
	at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)
	at reactor.core.publisher.FluxSubscribeOnValue$ScheduledScalar.run(FluxSubscribeOnValue.java:181)
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:68)
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:28)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
25-09-07.15:37:21.706 [boundedElastic-2] ERROR StreamContextManager   - 流片段Step3-监督流执行出错: No StreamAdvisors available to execute
java.lang.IllegalStateException: No StreamAdvisors available to execute
	at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextStream$6(DefaultAroundAdvisorChain.java:119)
	at reactor.core.publisher.FluxDeferContextual.subscribe(FluxDeferContextual.java:49)
	at reactor.core.publisher.Flux.subscribe(Flux.java:8891)
	at reactor.core.publisher.MonoFlatMapMany$FlatMapManyMain.onNext(MonoFlatMapMany.java:196)
	at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)
	at reactor.core.publisher.FluxSubscribeOnValue$ScheduledScalar.run(FluxSubscribeOnValue.java:181)
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:68)
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:28)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
25-09-07.15:37:21.706 [boundedElastic-2] ERROR Operators              - Operator called default onErrorDropped
reactor.core.Exceptions$ErrorCallbackNotImplemented: java.lang.IllegalStateException: No StreamAdvisors available to execute
Caused by: java.lang.IllegalStateException: No StreamAdvisors available to execute
	at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextStream$6(DefaultAroundAdvisorChain.java:119)
	at reactor.core.publisher.FluxDeferContextual.subscribe(FluxDeferContextual.java:49)
	at reactor.core.publisher.Flux.subscribe(Flux.java:8891)
	at reactor.core.publisher.MonoFlatMapMany$FlatMapManyMain.onNext(MonoFlatMapMany.java:196)
	at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)
	at reactor.core.publisher.FluxSubscribeOnValue$ScheduledScalar.run(FluxSubscribeOnValue.java:181)
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:68)
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:28)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
25-09-07.15:37:33.694 [main            ] INFO  Step3PrecisionExecutorClient - ✅ 质量检查通过
25-09-07.15:37:33.695 [main            ] INFO  Step4QualitySupervisorClient - === 动态多轮执行第2步 Step4 ====
25-09-07.15:37:33.695 [main            ] INFO  Step4QualitySupervisorClient - 
📊 阶段4: 执行总结分析
25-09-07.15:37:33.708 [main            ] INFO  Step4QualitySupervisorClient - 
📊 === 动态多轮执行总结 ====
25-09-07.15:37:33.709 [main            ] INFO  Step4QualitySupervisorClient - 📈 总执行步数: 2 步
25-09-07.15:37:33.709 [main            ] INFO  Step4QualitySupervisorClient - ✅ 任务完成状态: 已完成
25-09-07.15:37:33.709 [main            ] INFO  Step4QualitySupervisorClient - 📊 执行效率: 100.0%
25-09-07.15:37:33.709 [main            ] INFO  Step4QualitySupervisorClient - 
🏁 === 动态多轮执行测试结束 ====
25-09-07.15:37:33.710 [main            ] INFO  StreamContextManager   - StreamContextManager完成流输出
25-09-07.15:37:33.710 [main            ] INFO  AutoExecuteStrategy    - AutoAgent流式任务执行完成，会话ID: stream-session-1757230555167
25-09-07.15:37:33.710 [main            ] INFO  StreamAutoAgentTest    - 测试完成，总结果长度: 165
25-09-07.15:37:33.710 [main            ] INFO  StreamAutoAgentTest    - 完整结果: 
🚀 === AutoAgent执行开始 ===
📋 === 动态多轮执行开始 ===
👤 用户输入: 请帮我分析一下人工智能的发展趋势，并给出学习建议
🔢 最大执行步数: 2
🆔 会话ID: stream-session-1757230555167


🚀 === 开始执行 Step1-任务分析 (第1步) ===

25-09-07.15:37:33.723 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MainHikariPool - Shutdown initiated...
25-09-07.15:37:33.736 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MainHikariPool - Shutdown completed.
