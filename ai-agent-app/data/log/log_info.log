25-09-06.15:02:51.745 [main            ] INFO  AiAgentTest            - Starting AiAgentTest using Java 17.0.12 with PID 29804 (started by 30562 in D:\ACode\project\ai-agent\ai-agent-app)
25-09-06.15:02:51.747 [main            ] INFO  AiAgentTest            - The following 1 profile is active: "dev"
25-09-06.15:02:55.029 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_openai. Is empty: false
25-09-06.15:02:55.035 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_openai in schema: public
25-09-06.15:02:55.035 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-09-06.15:02:55.394 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-09-06.15:02:55.977 [main            ] INFO  AiAgentTest            - Started AiAgentTest in 4.869 seconds (process running for 6.375)
25-09-06.15:02:55.983 [main            ] INFO  AgentAutoLoadConfig    - 开始自动加载Agent
25-09-06.15:02:55.984 [main            ] INFO  AgentAutoLoadConfig    - 开始自动装配AI客户端，客户端ID列表: [3101, 3102, 3103, 3104]
25-09-06.15:02:55.988 [main            ] INFO  RootNode               - AIClient装配链---RootNode---开始查询配置信息
25-09-06.15:02:55.991 [pool-2-thread-1 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_api) [3101, 3102, 3103, 3104]
25-09-06.15:02:55.991 [pool-2-thread-2 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_model) [3101, 3102, 3103, 3104]
25-09-06.15:02:55.991 [pool-2-thread-3 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_tool_mcp) [3101, 3102, 3103, 3104]
25-09-06.15:02:55.992 [pool-2-thread-4 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_system_prompt) [3101, 3102, 3103, 3104]
25-09-06.15:02:55.993 [pool-2-thread-5 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_advisor) [3101, 3102, 3103, 3104]
25-09-06.15:02:55.993 [pool-2-thread-6 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client) [3101, 3102, 3103, 3104]
25-09-06.15:02:56.018 [pool-2-thread-6 ] INFO  HikariDataSource       - MainHikariPool - Starting...
25-09-06.15:02:56.293 [pool-2-thread-6 ] INFO  HikariPool             - MainHikariPool - Added connection com.mysql.cj.jdbc.ConnectionImpl@17c70046
25-09-06.15:02:56.296 [pool-2-thread-6 ] INFO  HikariDataSource       - MainHikariPool - Start completed.
25-09-06.15:02:56.619 [main            ] INFO  RootNode               - AIClient装配链---RootNode---查询配置信息完毕
25-09-06.15:02:56.619 [main            ] INFO  AiClientApiNode        - AIClient装配链---AiClientApiNode---开始装配API
25-09-06.15:02:56.626 [main            ] INFO  AiClientMcpNode        - AIClient装配链---AiClientMcpNode---开始装配MCP
25-09-06.15:02:57.235 [HttpClient-10-Worker-0] INFO  McpAsyncClient         - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[completions=null, experimental={}, logging=null, prompts=PromptCapabilities[listChanged=false], resources=ResourceCapabilities[subscribe=false, listChanged=false], tools=ToolCapabilities[listChanged=false]], Info: Implementation[name=AIsearch, version=1.4.1] and Instructions null
25-09-06.15:02:57.340 [main            ] INFO  AiClientMcpNode        - sse方式注册mcp: baidu-search 成功
25-09-06.15:02:57.340 [main            ] INFO  AiClientModelNode      - AIClient装配链---AiClientModelNode---开始装配Model
25-09-06.15:02:57.340 [main            ] INFO  AiClientModelNode      - 注册模型-modelId:2001, modelName:deepseek-v3-成功
25-09-06.15:02:57.340 [main            ] INFO  AiClientAdvisorNode    - AIClient装配链---AiClientAdvisorNode---开始装配顾问
25-09-06.15:02:57.353 [main            ] INFO  AiClientAdvisorNode    - 注册顾问---advisorId:4001, advisorName:记忆---成功
25-09-06.15:02:57.353 [main            ] INFO  AiClientNode           - AIClient装配链---AiClientNode---开始装配Client
25-09-06.15:02:57.464 [main            ] INFO  AiClientNode           - 注册Client---clientId:3101, clientName:任务分析和状态判断---成功
25-09-06.15:02:57.537 [main            ] INFO  AiClientNode           - 注册Client---clientId:3102, clientName:具体任务执行---成功
25-09-06.15:02:57.598 [main            ] INFO  AiClientNode           - 注册Client---clientId:3103, clientName:质量检查和优化---成功
25-09-06.15:02:57.599 [main            ] INFO  AgentAutoLoadConfig    - 自动装配AI客户端完成
25-09-06.15:02:58.506 [HttpClient-13-Worker-2] INFO  McpAsyncClient         - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[completions=null, experimental={}, logging=null, prompts=PromptCapabilities[listChanged=false], resources=ResourceCapabilities[subscribe=false, listChanged=false], tools=ToolCapabilities[listChanged=false]], Info: Implementation[name=AIsearch, version=1.4.1] and Instructions null
25-09-06.15:03:18.318 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MainHikariPool - Shutdown initiated...
25-09-06.15:03:18.331 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MainHikariPool - Shutdown completed.
25-09-06.15:07:28.649 [main            ] INFO  AiAgentTest            - Starting AiAgentTest using Java 17.0.12 with PID 29264 (started by 30562 in D:\ACode\project\ai-agent\ai-agent-app)
25-09-06.15:07:28.651 [main            ] INFO  AiAgentTest            - The following 1 profile is active: "dev"
25-09-06.15:07:31.469 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_openai. Is empty: false
25-09-06.15:07:31.474 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_openai in schema: public
25-09-06.15:07:31.474 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-09-06.15:07:31.802 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-09-06.15:07:32.381 [main            ] INFO  AiAgentTest            - Started AiAgentTest in 4.314 seconds (process running for 5.762)
25-09-06.15:07:32.386 [main            ] INFO  AgentAutoLoadConfig    - 开始自动加载Agent
25-09-06.15:07:32.386 [main            ] INFO  AgentAutoLoadConfig    - 开始自动装配AI客户端，客户端ID列表: [3101, 3102, 3103, 3104]
25-09-06.15:07:32.389 [main            ] INFO  RootNode               - AIClient装配链---RootNode---开始查询配置信息
25-09-06.15:07:32.391 [pool-2-thread-1 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_api) [3101, 3102, 3103, 3104]
25-09-06.15:07:32.393 [pool-2-thread-2 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_model) [3101, 3102, 3103, 3104]
25-09-06.15:07:32.393 [pool-2-thread-3 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_tool_mcp) [3101, 3102, 3103, 3104]
25-09-06.15:07:32.393 [pool-2-thread-4 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_system_prompt) [3101, 3102, 3103, 3104]
25-09-06.15:07:32.393 [pool-2-thread-5 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_advisor) [3101, 3102, 3103, 3104]
25-09-06.15:07:32.394 [pool-2-thread-6 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client) [3101, 3102, 3103, 3104]
25-09-06.15:07:32.416 [pool-2-thread-2 ] INFO  HikariDataSource       - MainHikariPool - Starting...
25-09-06.15:07:32.690 [pool-2-thread-2 ] INFO  HikariPool             - MainHikariPool - Added connection com.mysql.cj.jdbc.ConnectionImpl@960cff0
25-09-06.15:07:32.692 [pool-2-thread-2 ] INFO  HikariDataSource       - MainHikariPool - Start completed.
25-09-06.15:07:32.949 [main            ] INFO  RootNode               - AIClient装配链---RootNode---查询配置信息完毕
25-09-06.15:07:32.949 [main            ] INFO  AiClientApiNode        - AIClient装配链---AiClientApiNode---开始装配API
25-09-06.15:07:32.959 [main            ] INFO  AiClientMcpNode        - AIClient装配链---AiClientMcpNode---开始装配MCP
25-09-06.15:07:33.309 [HttpClient-10-Worker-0] INFO  McpAsyncClient         - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[completions=null, experimental={}, logging=null, prompts=PromptCapabilities[listChanged=false], resources=ResourceCapabilities[subscribe=false, listChanged=false], tools=ToolCapabilities[listChanged=false]], Info: Implementation[name=AIsearch, version=1.4.1] and Instructions null
25-09-06.15:07:33.397 [main            ] INFO  AiClientMcpNode        - sse方式注册mcp: baidu-search 成功
25-09-06.15:07:33.397 [main            ] INFO  AiClientModelNode      - AIClient装配链---AiClientModelNode---开始装配Model
25-09-06.15:07:33.398 [main            ] INFO  AiClientModelNode      - 注册模型-modelId:2001, modelName:deepseek-v3-成功
25-09-06.15:07:33.398 [main            ] INFO  AiClientAdvisorNode    - AIClient装配链---AiClientAdvisorNode---开始装配顾问
25-09-06.15:07:33.409 [main            ] INFO  AiClientAdvisorNode    - 注册顾问---advisorId:4001, advisorName:记忆---成功
25-09-06.15:07:33.409 [main            ] INFO  AiClientNode           - AIClient装配链---AiClientNode---开始装配Client
25-09-06.15:07:33.504 [main            ] INFO  AiClientNode           - 注册Client---clientId:3101, clientName:任务分析和状态判断---成功
25-09-06.15:07:33.566 [main            ] INFO  AiClientNode           - 注册Client---clientId:3102, clientName:具体任务执行---成功
25-09-06.15:07:33.628 [main            ] INFO  AiClientNode           - 注册Client---clientId:3103, clientName:质量检查和优化---成功
25-09-06.15:07:33.628 [main            ] INFO  AgentAutoLoadConfig    - 自动装配AI客户端完成
25-09-06.15:07:34.432 [HttpClient-13-Worker-2] INFO  McpAsyncClient         - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[completions=null, experimental={}, logging=null, prompts=PromptCapabilities[listChanged=false], resources=ResourceCapabilities[subscribe=false, listChanged=false], tools=ToolCapabilities[listChanged=false]], Info: Implementation[name=AIsearch, version=1.4.1] and Instructions null
25-09-06.15:07:41.649 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MainHikariPool - Shutdown initiated...
25-09-06.15:07:41.663 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MainHikariPool - Shutdown completed.
25-09-06.15:11:50.475 [main            ] INFO  AiAgentTest            - Starting AiAgentTest using Java 17.0.12 with PID 14216 (started by 30562 in D:\ACode\project\ai-agent\ai-agent-app)
25-09-06.15:11:50.477 [main            ] INFO  AiAgentTest            - The following 1 profile is active: "dev"
25-09-06.15:11:53.194 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_openai. Is empty: false
25-09-06.15:11:53.200 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_openai in schema: public
25-09-06.15:11:53.200 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-09-06.15:11:53.537 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-09-06.15:11:54.075 [main            ] INFO  AiAgentTest            - Started AiAgentTest in 4.165 seconds (process running for 5.509)
25-09-06.15:11:54.079 [main            ] INFO  AgentAutoLoadConfig    - 开始自动加载Agent
25-09-06.15:11:54.079 [main            ] INFO  AgentAutoLoadConfig    - 开始自动装配AI客户端，客户端ID列表: [3101, 3102, 3103, 3104]
25-09-06.15:11:54.082 [main            ] INFO  RootNode               - AIClient装配链---RootNode---开始查询配置信息
25-09-06.15:11:54.084 [pool-2-thread-1 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_api) [3101, 3102, 3103, 3104]
25-09-06.15:11:54.084 [pool-2-thread-2 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_model) [3101, 3102, 3103, 3104]
25-09-06.15:11:54.085 [pool-2-thread-3 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_tool_mcp) [3101, 3102, 3103, 3104]
25-09-06.15:11:54.085 [pool-2-thread-4 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_system_prompt) [3101, 3102, 3103, 3104]
25-09-06.15:11:54.085 [pool-2-thread-5 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_advisor) [3101, 3102, 3103, 3104]
25-09-06.15:11:54.086 [pool-2-thread-6 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client) [3101, 3102, 3103, 3104]
25-09-06.15:11:54.114 [pool-2-thread-2 ] INFO  HikariDataSource       - MainHikariPool - Starting...
25-09-06.15:11:54.398 [pool-2-thread-2 ] INFO  HikariPool             - MainHikariPool - Added connection com.mysql.cj.jdbc.ConnectionImpl@4ea7994c
25-09-06.15:11:54.400 [pool-2-thread-2 ] INFO  HikariDataSource       - MainHikariPool - Start completed.
25-09-06.15:11:54.657 [main            ] INFO  RootNode               - AIClient装配链---RootNode---查询配置信息完毕
25-09-06.15:11:54.657 [main            ] INFO  AiClientApiNode        - AIClient装配链---AiClientApiNode---开始装配API
25-09-06.15:11:54.667 [main            ] INFO  AiClientMcpNode        - AIClient装配链---AiClientMcpNode---开始装配MCP
25-09-06.15:11:55.026 [HttpClient-10-Worker-0] INFO  McpAsyncClient         - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[completions=null, experimental={}, logging=null, prompts=PromptCapabilities[listChanged=false], resources=ResourceCapabilities[subscribe=false, listChanged=false], tools=ToolCapabilities[listChanged=false]], Info: Implementation[name=AIsearch, version=1.4.1] and Instructions null
25-09-06.15:11:55.127 [main            ] INFO  AiClientMcpNode        - sse方式注册mcp: baidu-search 成功
25-09-06.15:11:55.127 [main            ] INFO  AiClientModelNode      - AIClient装配链---AiClientModelNode---开始装配Model
25-09-06.15:11:55.128 [main            ] INFO  AiClientModelNode      - 注册模型-modelId:2001, modelName:deepseek-v3-成功
25-09-06.15:11:55.128 [main            ] INFO  AiClientAdvisorNode    - AIClient装配链---AiClientAdvisorNode---开始装配顾问
25-09-06.15:11:55.140 [main            ] INFO  AiClientAdvisorNode    - 注册顾问---advisorId:4001, advisorName:记忆---成功
25-09-06.15:11:55.140 [main            ] INFO  AiClientNode           - AIClient装配链---AiClientNode---开始装配Client
25-09-06.15:11:55.259 [main            ] INFO  AiClientNode           - 注册Client---clientId:3101, clientName:任务分析和状态判断---成功
25-09-06.15:11:55.326 [main            ] INFO  AiClientNode           - 注册Client---clientId:3102, clientName:具体任务执行---成功
25-09-06.15:11:55.400 [main            ] INFO  AiClientNode           - 注册Client---clientId:3103, clientName:质量检查和优化---成功
25-09-06.15:11:55.401 [main            ] INFO  AgentAutoLoadConfig    - 自动装配AI客户端完成
25-09-06.15:11:56.235 [HttpClient-13-Worker-0] INFO  McpAsyncClient         - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[completions=null, experimental={}, logging=null, prompts=PromptCapabilities[listChanged=false], resources=ResourceCapabilities[subscribe=false, listChanged=false], tools=ToolCapabilities[listChanged=false]], Info: Implementation[name=AIsearch, version=1.4.1] and Instructions null
25-09-06.15:12:05.349 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MainHikariPool - Shutdown initiated...
25-09-06.15:12:05.361 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MainHikariPool - Shutdown completed.
25-09-06.15:13:40.830 [main            ] INFO  AiAgentTest            - Starting AiAgentTest using Java 17.0.12 with PID 36768 (started by 30562 in D:\ACode\project\ai-agent\ai-agent-app)
25-09-06.15:13:40.833 [main            ] INFO  AiAgentTest            - The following 1 profile is active: "dev"
25-09-06.15:13:44.479 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_openai. Is empty: false
25-09-06.15:13:44.488 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_openai in schema: public
25-09-06.15:13:44.488 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-09-06.15:13:44.912 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-09-06.15:13:45.658 [main            ] INFO  AiAgentTest            - Started AiAgentTest in 5.517 seconds (process running for 7.339)
25-09-06.15:13:45.664 [main            ] INFO  AgentAutoLoadConfig    - 开始自动加载Agent
25-09-06.15:13:45.665 [main            ] INFO  AgentAutoLoadConfig    - 开始自动装配AI客户端，客户端ID列表: [3101, 3102, 3103, 3104]
25-09-06.15:13:45.669 [main            ] INFO  RootNode               - AIClient装配链---RootNode---开始查询配置信息
25-09-06.15:13:45.672 [pool-2-thread-1 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_api) [3101, 3102, 3103, 3104]
25-09-06.15:13:45.673 [pool-2-thread-2 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_model) [3101, 3102, 3103, 3104]
25-09-06.15:13:45.673 [pool-2-thread-3 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_tool_mcp) [3101, 3102, 3103, 3104]
25-09-06.15:13:45.674 [pool-2-thread-4 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_system_prompt) [3101, 3102, 3103, 3104]
25-09-06.15:13:45.675 [pool-2-thread-5 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_advisor) [3101, 3102, 3103, 3104]
25-09-06.15:13:45.677 [pool-2-thread-6 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client) [3101, 3102, 3103, 3104]
25-09-06.15:13:45.713 [pool-2-thread-2 ] INFO  HikariDataSource       - MainHikariPool - Starting...
25-09-06.15:13:46.060 [pool-2-thread-2 ] INFO  HikariPool             - MainHikariPool - Added connection com.mysql.cj.jdbc.ConnectionImpl@1875ac90
25-09-06.15:13:46.063 [pool-2-thread-2 ] INFO  HikariDataSource       - MainHikariPool - Start completed.
25-09-06.15:13:46.354 [main            ] INFO  RootNode               - AIClient装配链---RootNode---查询配置信息完毕
25-09-06.15:13:46.354 [main            ] INFO  AiClientApiNode        - AIClient装配链---AiClientApiNode---开始装配API
25-09-06.15:13:46.361 [main            ] INFO  AiClientMcpNode        - AIClient装配链---AiClientMcpNode---开始装配MCP
25-09-06.15:13:46.782 [HttpClient-10-Worker-2] INFO  McpAsyncClient         - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[completions=null, experimental={}, logging=null, prompts=PromptCapabilities[listChanged=false], resources=ResourceCapabilities[subscribe=false, listChanged=false], tools=ToolCapabilities[listChanged=false]], Info: Implementation[name=AIsearch, version=1.4.1] and Instructions null
25-09-06.15:13:46.880 [main            ] INFO  AiClientMcpNode        - sse方式注册mcp: baidu-search 成功
25-09-06.15:13:46.880 [main            ] INFO  AiClientModelNode      - AIClient装配链---AiClientModelNode---开始装配Model
25-09-06.15:13:46.881 [main            ] INFO  AiClientModelNode      - 注册模型-modelId:2001, modelName:deepseek-v3-成功
25-09-06.15:13:46.881 [main            ] INFO  AiClientAdvisorNode    - AIClient装配链---AiClientAdvisorNode---开始装配顾问
25-09-06.15:13:46.893 [main            ] INFO  AiClientAdvisorNode    - 注册顾问---advisorId:4001, advisorName:记忆---成功
25-09-06.15:13:46.894 [main            ] INFO  AiClientNode           - AIClient装配链---AiClientNode---开始装配Client
25-09-06.15:13:47.004 [main            ] INFO  AiClientNode           - 注册Client---clientId:3101, clientName:任务分析和状态判断---成功
25-09-06.15:13:47.087 [main            ] INFO  AiClientNode           - 注册Client---clientId:3102, clientName:具体任务执行---成功
25-09-06.15:13:47.158 [main            ] INFO  AiClientNode           - 注册Client---clientId:3103, clientName:质量检查和优化---成功
25-09-06.15:13:47.159 [main            ] INFO  AgentAutoLoadConfig    - 自动装配AI客户端完成
25-09-06.15:13:48.117 [HttpClient-13-Worker-1] INFO  McpAsyncClient         - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[completions=null, experimental={}, logging=null, prompts=PromptCapabilities[listChanged=false], resources=ResourceCapabilities[subscribe=false, listChanged=false], tools=ToolCapabilities[listChanged=false]], Info: Implementation[name=AIsearch, version=1.4.1] and Instructions null
25-09-06.15:13:58.592 [main            ] INFO  AiAgentTest            - 测试结果(call):{"metadata":{"empty":false,"id":"chatcmpl-66409f02-c373-4cbc-a5c2-b731a64b88cd","model":"deepseek-v3","rateLimit":{},"usage":{"promptTokens":3120,"completionTokens":370,"totalTokens":3490}},"result":{"metadata":{"contentFilters":[],"empty":true,"finishReason":"STOP"},"output":{"media":[],"messageType":"ASSISTANT","metadata":{"role":"ASSISTANT","messageType":"ASSISTANT","refusal":"","finishReason":"STOP","annotations":[],"index":0,"id":"chatcmpl-66409f02-c373-4cbc-a5c2-b731a64b88cd"},"text":"博德之门系列游戏基于《龙与地下城》的“被遗忘的国度”战役模组构建了丰富的奇幻世界观。以下是一些关键信息：\n\n1. **背景设定**：  \n   - 游戏世界观源自《龙与地下城》第2版规则，以“被遗忘的国度”（Forgotten Realms）为背景，包含经典地域如博德之门、斗篷森林等。  \n   - 玩家通常扮演一名孤儿，通过冒险逐步揭开身世之谜。  \n\n2. **系列作品**：  \n   - 初代《博德之门》于1998年发行，后续作品包括《博德之门2：安姆的阴影》《博德之门3》及衍生作品。  \n   - 2012年发布的《博德之门：增强版》登陆了多平台。  \n\n3. **核心内容**：  \n   - 剧情围绕主角与同伴（如查内姆、爱蒙）的冒险展开，融合战斗、探索和角色成长。  \n   - 《博德之门3》延续了前作的庞大设定，并进一步扩展了故事。  \n\n4. **资源推荐**：  \n   - [《博德之门3》世界观介绍](http://v5.360game.360.cn/gknow_article/content?id=64cb0e0fab5107bf99baa03e)  \n   - [百度百科：博德之门](https://baike.baidu.com/item/博德之门/23254789)  \n\n如需更详细的内容，可以访问上述链接或进一步提问！"{"$ref":"$.metadata.rateLimit.usage.result.metadata.contentFilters.output.metadata.annotations"}}},"results":[{"$ref":"$.metadata.rateLimit.usage.result"}]}
25-09-06.15:13:58.606 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MainHikariPool - Shutdown initiated...
25-09-06.15:13:58.623 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MainHikariPool - Shutdown completed.
25-09-06.15:14:30.751 [main            ] INFO  AiAgentTest            - Starting AiAgentTest using Java 17.0.12 with PID 24884 (started by 30562 in D:\ACode\project\ai-agent\ai-agent-app)
25-09-06.15:14:30.753 [main            ] INFO  AiAgentTest            - The following 1 profile is active: "dev"
25-09-06.15:14:34.228 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_openai. Is empty: false
25-09-06.15:14:34.238 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_openai in schema: public
25-09-06.15:14:34.239 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-09-06.15:14:34.673 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-09-06.15:14:35.392 [main            ] INFO  AiAgentTest            - Started AiAgentTest in 5.294 seconds (process running for 6.944)
25-09-06.15:14:35.397 [main            ] INFO  AgentAutoLoadConfig    - 开始自动加载Agent
25-09-06.15:14:35.398 [main            ] INFO  AgentAutoLoadConfig    - 开始自动装配AI客户端，客户端ID列表: [3101, 3102, 3103, 3104]
25-09-06.15:14:35.400 [main            ] INFO  RootNode               - AIClient装配链---RootNode---开始查询配置信息
25-09-06.15:14:35.403 [pool-2-thread-1 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_api) [3101, 3102, 3103, 3104]
25-09-06.15:14:35.404 [pool-2-thread-2 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_model) [3101, 3102, 3103, 3104]
25-09-06.15:14:35.405 [pool-2-thread-3 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_tool_mcp) [3101, 3102, 3103, 3104]
25-09-06.15:14:35.407 [pool-2-thread-4 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_system_prompt) [3101, 3102, 3103, 3104]
25-09-06.15:14:35.408 [pool-2-thread-5 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_advisor) [3101, 3102, 3103, 3104]
25-09-06.15:14:35.408 [pool-2-thread-6 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client) [3101, 3102, 3103, 3104]
25-09-06.15:14:35.435 [pool-2-thread-3 ] INFO  HikariDataSource       - MainHikariPool - Starting...
25-09-06.15:14:35.738 [pool-2-thread-3 ] INFO  HikariPool             - MainHikariPool - Added connection com.mysql.cj.jdbc.ConnectionImpl@10604a62
25-09-06.15:14:35.740 [pool-2-thread-3 ] INFO  HikariDataSource       - MainHikariPool - Start completed.
25-09-06.15:14:35.997 [main            ] INFO  RootNode               - AIClient装配链---RootNode---查询配置信息完毕
25-09-06.15:14:35.997 [main            ] INFO  AiClientApiNode        - AIClient装配链---AiClientApiNode---开始装配API
25-09-06.15:14:36.005 [main            ] INFO  AiClientMcpNode        - AIClient装配链---AiClientMcpNode---开始装配MCP
25-09-06.15:14:36.434 [HttpClient-10-Worker-2] INFO  McpAsyncClient         - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[completions=null, experimental={}, logging=null, prompts=PromptCapabilities[listChanged=false], resources=ResourceCapabilities[subscribe=false, listChanged=false], tools=ToolCapabilities[listChanged=false]], Info: Implementation[name=AIsearch, version=1.4.1] and Instructions null
25-09-06.15:14:36.524 [main            ] INFO  AiClientMcpNode        - sse方式注册mcp: baidu-search 成功
25-09-06.15:14:36.524 [main            ] INFO  AiClientModelNode      - AIClient装配链---AiClientModelNode---开始装配Model
25-09-06.15:14:36.526 [main            ] INFO  AiClientModelNode      - 注册模型-modelId:2001, modelName:deepseek-v3-成功
25-09-06.15:14:36.526 [main            ] INFO  AiClientAdvisorNode    - AIClient装配链---AiClientAdvisorNode---开始装配顾问
25-09-06.15:14:36.538 [main            ] INFO  AiClientAdvisorNode    - 注册顾问---advisorId:4001, advisorName:记忆---成功
25-09-06.15:14:36.538 [main            ] INFO  AiClientNode           - AIClient装配链---AiClientNode---开始装配Client
25-09-06.15:14:36.638 [main            ] INFO  AiClientNode           - 注册Client---clientId:3101, clientName:任务分析和状态判断---成功
25-09-06.15:14:36.700 [main            ] INFO  AiClientNode           - 注册Client---clientId:3102, clientName:具体任务执行---成功
25-09-06.15:14:36.769 [main            ] INFO  AiClientNode           - 注册Client---clientId:3103, clientName:质量检查和优化---成功
25-09-06.15:14:36.770 [main            ] INFO  AgentAutoLoadConfig    - 自动装配AI客户端完成
25-09-06.15:14:37.618 [HttpClient-13-Worker-1] INFO  McpAsyncClient         - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[completions=null, experimental={}, logging=null, prompts=PromptCapabilities[listChanged=false], resources=ResourceCapabilities[subscribe=false, listChanged=false], tools=ToolCapabilities[listChanged=false]], Info: Implementation[name=AIsearch, version=1.4.1] and Instructions null
25-09-06.15:15:19.200 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MainHikariPool - Shutdown initiated...
25-09-06.15:15:19.216 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MainHikariPool - Shutdown completed.
25-09-06.15:31:53.828 [main            ] INFO  AiAgentTest            - Starting AiAgentTest using Java 17.0.12 with PID 38208 (started by 30562 in D:\ACode\project\ai-agent\ai-agent-app)
25-09-06.15:31:53.832 [main            ] INFO  AiAgentTest            - The following 1 profile is active: "dev"
25-09-06.15:31:56.929 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_openai. Is empty: false
25-09-06.15:31:56.934 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_openai in schema: public
25-09-06.15:31:56.934 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-09-06.15:31:57.279 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-09-06.15:31:57.868 [main            ] INFO  AiAgentTest            - Started AiAgentTest in 4.655 seconds (process running for 6.013)
25-09-06.15:31:57.872 [main            ] INFO  AgentAutoLoadConfig    - 开始自动加载Agent
25-09-06.15:31:57.872 [main            ] INFO  AgentAutoLoadConfig    - 开始自动装配AI客户端，客户端ID列表: [3101, 3102, 3103, 3104]
25-09-06.15:31:57.878 [main            ] INFO  RootNode               - AIClient装配链---RootNode---开始查询配置信息
25-09-06.15:31:57.880 [pool-2-thread-1 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_api) [3101, 3102, 3103, 3104]
25-09-06.15:31:57.880 [pool-2-thread-2 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_model) [3101, 3102, 3103, 3104]
25-09-06.15:31:57.881 [pool-2-thread-3 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_tool_mcp) [3101, 3102, 3103, 3104]
25-09-06.15:31:57.882 [pool-2-thread-4 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_system_prompt) [3101, 3102, 3103, 3104]
25-09-06.15:31:57.882 [pool-2-thread-5 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_advisor) [3101, 3102, 3103, 3104]
25-09-06.15:31:57.883 [pool-2-thread-6 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client) [3101, 3102, 3103, 3104]
25-09-06.15:31:57.918 [pool-2-thread-2 ] INFO  HikariDataSource       - MainHikariPool - Starting...
25-09-06.15:31:58.203 [pool-2-thread-2 ] INFO  HikariPool             - MainHikariPool - Added connection com.mysql.cj.jdbc.ConnectionImpl@2aec1fd3
25-09-06.15:31:58.205 [pool-2-thread-2 ] INFO  HikariDataSource       - MainHikariPool - Start completed.
25-09-06.15:31:58.454 [main            ] INFO  RootNode               - AIClient装配链---RootNode---查询配置信息完毕
25-09-06.15:31:58.454 [main            ] INFO  AiClientApiNode        - AIClient装配链---AiClientApiNode---开始装配API
25-09-06.15:31:58.461 [main            ] INFO  AiClientMcpNode        - AIClient装配链---AiClientMcpNode---开始装配MCP
25-09-06.15:31:58.937 [HttpClient-10-Worker-0] INFO  McpAsyncClient         - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[completions=null, experimental={}, logging=null, prompts=PromptCapabilities[listChanged=false], resources=ResourceCapabilities[subscribe=false, listChanged=false], tools=ToolCapabilities[listChanged=false]], Info: Implementation[name=AIsearch, version=1.4.1] and Instructions null
25-09-06.15:31:58.999 [main            ] INFO  AiClientMcpNode        - sse方式注册mcp: baidu-search 成功
25-09-06.15:31:59.000 [main            ] INFO  AiClientModelNode      - AIClient装配链---AiClientModelNode---开始装配Model
25-09-06.15:31:59.001 [main            ] INFO  AiClientModelNode      - 注册模型-modelId:2001, modelName:deepseek-v3-成功
25-09-06.15:31:59.001 [main            ] INFO  AiClientAdvisorNode    - AIClient装配链---AiClientAdvisorNode---开始装配顾问
25-09-06.15:31:59.013 [main            ] INFO  AiClientAdvisorNode    - 注册顾问---advisorId:4001, advisorName:记忆---成功
25-09-06.15:31:59.014 [main            ] INFO  AiClientNode           - AIClient装配链---AiClientNode---开始装配Client
25-09-06.15:31:59.087 [main            ] INFO  AiClientNode           - 注册Client---clientId:3101, clientName:任务分析和状态判断---成功
25-09-06.15:31:59.137 [main            ] INFO  AiClientNode           - 注册Client---clientId:3102, clientName:具体任务执行---成功
25-09-06.15:31:59.224 [main            ] INFO  AiClientNode           - 注册Client---clientId:3103, clientName:质量检查和优化---成功
25-09-06.15:31:59.224 [main            ] INFO  AgentAutoLoadConfig    - 自动装配AI客户端完成
25-09-06.15:32:00.047 [HttpClient-13-Worker-0] INFO  McpAsyncClient         - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[completions=null, experimental={}, logging=null, prompts=PromptCapabilities[listChanged=false], resources=ResourceCapabilities[subscribe=false, listChanged=false], tools=ToolCapabilities[listChanged=false]], Info: Implementation[name=AIsearch, version=1.4.1] and Instructions null
25-09-06.15:33:27.499 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MainHikariPool - Shutdown initiated...
25-09-06.15:33:27.524 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MainHikariPool - Shutdown completed.
25-09-06.15:36:42.646 [main            ] INFO  AiAgentTest            - Starting AiAgentTest using Java 17.0.12 with PID 31056 (started by 30562 in D:\ACode\project\ai-agent\ai-agent-app)
25-09-06.15:36:42.647 [main            ] INFO  AiAgentTest            - The following 1 profile is active: "dev"
25-09-06.15:36:46.122 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_openai. Is empty: false
25-09-06.15:36:46.128 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_openai in schema: public
25-09-06.15:36:46.129 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-09-06.15:36:46.524 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-09-06.15:36:47.206 [main            ] INFO  AiAgentTest            - Started AiAgentTest in 5.156 seconds (process running for 6.733)
25-09-06.15:36:47.213 [main            ] INFO  AgentAutoLoadConfig    - 开始自动加载Agent
25-09-06.15:36:47.214 [main            ] INFO  AgentAutoLoadConfig    - 开始自动装配AI客户端，客户端ID列表: [3101, 3102, 3103, 3104]
25-09-06.15:36:47.217 [main            ] INFO  RootNode               - AIClient装配链---RootNode---开始查询配置信息
25-09-06.15:36:47.219 [pool-2-thread-1 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_api) [3101, 3102, 3103, 3104]
25-09-06.15:36:47.219 [pool-2-thread-2 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_model) [3101, 3102, 3103, 3104]
25-09-06.15:36:47.219 [pool-2-thread-3 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_tool_mcp) [3101, 3102, 3103, 3104]
25-09-06.15:36:47.219 [pool-2-thread-4 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_system_prompt) [3101, 3102, 3103, 3104]
25-09-06.15:36:47.220 [pool-2-thread-5 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client_advisor) [3101, 3102, 3103, 3104]
25-09-06.15:36:47.220 [pool-2-thread-6 ] INFO  AiClientLoadDataStrategy - 查询配置数据(ai_client) [3101, 3102, 3103, 3104]
25-09-06.15:36:47.256 [pool-2-thread-2 ] INFO  HikariDataSource       - MainHikariPool - Starting...
25-09-06.15:36:47.568 [pool-2-thread-2 ] INFO  HikariPool             - MainHikariPool - Added connection com.mysql.cj.jdbc.ConnectionImpl@56c0d51
25-09-06.15:36:47.570 [pool-2-thread-2 ] INFO  HikariDataSource       - MainHikariPool - Start completed.
25-09-06.15:36:47.844 [main            ] INFO  RootNode               - AIClient装配链---RootNode---查询配置信息完毕
25-09-06.15:36:47.844 [main            ] INFO  AiClientApiNode        - AIClient装配链---AiClientApiNode---开始装配API
25-09-06.15:36:47.851 [main            ] INFO  AiClientMcpNode        - AIClient装配链---AiClientMcpNode---开始装配MCP
25-09-06.15:36:48.217 [HttpClient-10-Worker-1] INFO  McpAsyncClient         - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[completions=null, experimental={}, logging=null, prompts=PromptCapabilities[listChanged=false], resources=ResourceCapabilities[subscribe=false, listChanged=false], tools=ToolCapabilities[listChanged=false]], Info: Implementation[name=AIsearch, version=1.4.1] and Instructions null
25-09-06.15:36:48.300 [main            ] INFO  AiClientMcpNode        - sse方式注册mcp: baidu-search 成功
25-09-06.15:36:48.301 [main            ] INFO  AiClientModelNode      - AIClient装配链---AiClientModelNode---开始装配Model
25-09-06.15:36:48.301 [main            ] INFO  AiClientModelNode      - 注册模型-modelId:2001, modelName:deepseek-v3-成功
25-09-06.15:36:48.301 [main            ] INFO  AiClientAdvisorNode    - AIClient装配链---AiClientAdvisorNode---开始装配顾问
25-09-06.15:36:48.314 [main            ] INFO  AiClientAdvisorNode    - 注册顾问---advisorId:4001, advisorName:记忆---成功
25-09-06.15:36:48.314 [main            ] INFO  AiClientNode           - AIClient装配链---AiClientNode---开始装配Client
25-09-06.15:36:48.412 [main            ] INFO  AiClientNode           - 注册Client---clientId:3101, clientName:任务分析和状态判断---成功
25-09-06.15:36:48.470 [main            ] INFO  AiClientNode           - 注册Client---clientId:3102, clientName:具体任务执行---成功
25-09-06.15:36:48.532 [main            ] INFO  AiClientNode           - 注册Client---clientId:3103, clientName:质量检查和优化---成功
25-09-06.15:36:48.532 [main            ] INFO  AgentAutoLoadConfig    - 自动装配AI客户端完成
25-09-06.15:36:49.417 [HttpClient-13-Worker-2] INFO  McpAsyncClient         - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[completions=null, experimental={}, logging=null, prompts=PromptCapabilities[listChanged=false], resources=ResourceCapabilities[subscribe=false, listChanged=false], tools=ToolCapabilities[listChanged=false]], Info: Implementation[name=AIsearch, version=1.4.1] and Instructions null
25-09-06.15:37:05.128 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MainHikariPool - Shutdown initiated...
25-09-06.15:37:05.145 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MainHikariPool - Shutdown completed.
