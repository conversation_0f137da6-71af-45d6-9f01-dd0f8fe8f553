package com.flyxy.ai.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * @Description:
 * @Author: flyxy
 * @Date: 2025/9/2
 */
@Data
@Component
@ConfigurationProperties(prefix = "agent.auto-config")
public class AgentAutoConfigProperties {

    /**
     * 是否启用AI Agent自动装配
     */
    private boolean enabled = false;

    /**
     * 需要自动装配的客户端ID列表
     */
    private List<String> clientIds;

    // 自定义setter处理字符串到List的转换
    public void setClientIds(String clientIds) {
        if (clientIds != null && !clientIds.isEmpty()) {
            this.clientIds = Arrays.asList(clientIds.split(","));
        }
    }

}
