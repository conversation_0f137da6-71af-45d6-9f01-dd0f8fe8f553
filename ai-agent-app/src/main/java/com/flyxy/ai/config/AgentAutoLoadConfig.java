package com.flyxy.ai.config;

import com.flyxy.ai.domain.agent.model.entity.ArmoryCommandEntity;
import com.flyxy.ai.domain.agent.model.valobj.enums.AiAgentEnumVO;
import com.flyxy.ai.domain.agent.service.armory.factory.DefaultArmoryStrategyFactory;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * @Description:
 * @Author: flyxy
 * @Date: 2025/9/2
 */
@Slf4j
@Configuration
public class AgentAutoLoadConfig implements CommandLineRunner {

    @Resource
    private AgentAutoConfigProperties agentAutoConfigProperties;

    @Resource
    private DefaultArmoryStrategyFactory defaultArmoryStrategyFactory;

    @Override
    public void run(String... args) throws Exception {
        try {
            if (agentAutoConfigProperties.isEnabled()) {
                log.info("开始自动加载Agent");

                List<String> clientIds = agentAutoConfigProperties.getClientIds();

                log.info("开始自动装配AI客户端，客户端ID列表: {}", clientIds);

                String apply = defaultArmoryStrategyFactory.armoryStrategyHandler()
                        .apply(ArmoryCommandEntity.builder()
                                        .commandType(AiAgentEnumVO.AI_CLIENT.getCode())
                                        .commandIdList(clientIds)
                                        .build(),
                                new DefaultArmoryStrategyFactory.DynamicContext());

                log.info("自动装配AI客户端完成");
            }
        } catch (Exception e) {
            log.error("自动加载Agent异常", e);
        }
    }
}
