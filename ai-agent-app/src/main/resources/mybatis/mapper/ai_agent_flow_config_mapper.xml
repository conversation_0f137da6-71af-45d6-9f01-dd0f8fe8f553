<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.flyxy.ai.infrastructure.dao.IAiAgentFlowConfigDao">

    <resultMap id="AiAgentFlowConfigMap" type="com.flyxy.ai.infrastructure.dao.po.AiAgentFlowConfig">
        <id column="id" property="id"/>
        <result column="agent_id" property="agentId"/>
        <result column="client_id" property="clientId"/>
        <result column="client_name" property="clientName"/>
        <result column="client_type" property="clientType"/>
        <result column="sequence" property="sequence"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <insert id="insert" parameterType="com.flyxy.ai.infrastructure.dao.po.AiAgentFlowConfig" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO ai_agent_flow_config (agent_id, client_id, client_name, client_type, sequence, create_time)
        VALUES (#{agentId}, #{clientId}, #{clientName}, #{clientType}, #{sequence}, #{createTime})
    </insert>

    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM ai_agent_flow_config WHERE id = #{id}
    </delete>

    <delete id="deleteByAgentId" parameterType="java.lang.String">
        DELETE FROM ai_agent_flow_config WHERE agent_id = #{agentId}
    </delete>

    <update id="update" parameterType="com.flyxy.ai.infrastructure.dao.po.AiAgentFlowConfig">
        UPDATE ai_agent_flow_config SET
            agent_id = #{agentId},
            client_id = #{clientId},
            client_name = #{clientName},
            client_type = #{clientType},
            sequence = #{sequence}
        WHERE id = #{id}
    </update>

    <select id="queryById" parameterType="java.lang.Long" resultMap="AiAgentFlowConfigMap">
        SELECT id, agent_id, client_id, client_name, client_type, sequence, create_time
        FROM ai_agent_flow_config
        WHERE id = #{id}
    </select>

    <select id="queryByAgentId" parameterType="java.lang.String" resultMap="AiAgentFlowConfigMap">
        SELECT id, agent_id, client_id, client_name, client_type, sequence, create_time
        FROM ai_agent_flow_config
        WHERE agent_id = #{agentId}
        ORDER BY sequence
    </select>

    <select id="queryByClientId" parameterType="java.lang.String" resultMap="AiAgentFlowConfigMap">
        SELECT id, agent_id, client_id, client_name, client_type, sequence, create_time
        FROM ai_agent_flow_config
        WHERE client_id = #{clientId}
        ORDER BY sequence
    </select>

    <select id="queryAll" resultMap="AiAgentFlowConfigMap">
        SELECT id, agent_id, client_id, client_name, client_type, sequence, create_time
        FROM ai_agent_flow_config
        ORDER BY agent_id, sequence
    </select>

</mapper>
