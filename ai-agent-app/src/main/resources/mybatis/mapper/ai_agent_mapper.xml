<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.flyxy.ai.infrastructure.dao.IAiAgentDao">

    <resultMap id="AiAgentMap" type="com.flyxy.ai.infrastructure.dao.po.AiAgent">
        <id column="id" property="id"/>
        <result column="agent_id" property="agentId"/>
        <result column="agent_name" property="agentName"/>
        <result column="description" property="description"/>
        <result column="channel" property="channel"/>
        <result column="status" property="status"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <insert id="insert" parameterType="com.flyxy.ai.infrastructure.dao.po.AiAgent" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO ai_agent (agent_id, agent_name, description, channel, status, create_time, update_time)
        VALUES (#{agentId}, #{agentName}, #{description}, #{channel}, #{status}, #{createTime}, #{updateTime})
    </insert>

    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM ai_agent WHERE id = #{id}
    </delete>

    <delete id="deleteByAgentId" parameterType="java.lang.String">
        DELETE FROM ai_agent WHERE agent_id = #{agentId}
    </delete>

    <update id="update" parameterType="com.flyxy.ai.infrastructure.dao.po.AiAgent">
        UPDATE ai_agent SET
            agent_name = #{agentName},
            description = #{description},
            channel = #{channel},
            status = #{status},
            update_time = #{updateTime}
        WHERE id = #{id}
    </update>

    <select id="queryById" parameterType="java.lang.Long" resultMap="AiAgentMap">
        SELECT id, agent_id, agent_name, description, channel, status, create_time, update_time
        FROM ai_agent
        WHERE id = #{id}
    </select>

    <select id="queryByAgentId" parameterType="java.lang.String" resultMap="AiAgentMap">
        SELECT id, agent_id, agent_name, description, channel, status, create_time, update_time
        FROM ai_agent
        WHERE agent_id = #{agentId}
    </select>

    <select id="queryAll" resultMap="AiAgentMap">
        SELECT id, agent_id, agent_name, description, channel, status, create_time, update_time
        FROM ai_agent
        ORDER BY id
    </select>

    <select id="queryByStatus" parameterType="java.lang.Integer" resultMap="AiAgentMap">
        SELECT id, agent_id, agent_name, description, channel, status, create_time, update_time
        FROM ai_agent
        WHERE status = #{status}
        ORDER BY id
    </select>

</mapper>
