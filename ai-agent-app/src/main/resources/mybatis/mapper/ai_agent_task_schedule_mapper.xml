<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.flyxy.ai.infrastructure.dao.IAiAgentTaskScheduleDao">

    <resultMap id="AiAgentTaskScheduleMap" type="com.flyxy.ai.infrastructure.dao.po.AiAgentTaskSchedule">
        <id column="id" property="id"/>
        <result column="agent_id" property="agentId"/>
        <result column="task_name" property="taskName"/>
        <result column="description" property="description"/>
        <result column="cron_expression" property="cronExpression"/>
        <result column="task_param" property="taskParam"/>
        <result column="status" property="status"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <insert id="insert" parameterType="com.flyxy.ai.infrastructure.dao.po.AiAgentTaskSchedule" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO ai_agent_task_schedule (agent_id, task_name, description, cron_expression, task_param, status, create_time, update_time)
        VALUES (#{agentId}, #{taskName}, #{description}, #{cronExpression}, #{taskParam}, #{status}, #{createTime}, #{updateTime})
    </insert>

    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM ai_agent_task_schedule WHERE id = #{id}
    </delete>

    <delete id="deleteByAgentId" parameterType="java.lang.Long">
        DELETE FROM ai_agent_task_schedule WHERE agent_id = #{agentId}
    </delete>

    <update id="update" parameterType="com.flyxy.ai.infrastructure.dao.po.AiAgentTaskSchedule">
        UPDATE ai_agent_task_schedule SET
            task_name = #{taskName},
            description = #{description},
            cron_expression = #{cronExpression},
            task_param = #{taskParam},
            status = #{status},
            update_time = #{updateTime}
        WHERE id = #{id}
    </update>

    <select id="queryById" parameterType="java.lang.Long" resultMap="AiAgentTaskScheduleMap">
        SELECT id, agent_id, task_name, description, cron_expression, task_param, status, create_time, update_time
        FROM ai_agent_task_schedule
        WHERE id = #{id}
    </select>

    <select id="queryByAgentId" parameterType="java.lang.Long" resultMap="AiAgentTaskScheduleMap">
        SELECT id, agent_id, task_name, description, cron_expression, task_param, status, create_time, update_time
        FROM ai_agent_task_schedule
        WHERE agent_id = #{agentId}
        ORDER BY id
    </select>

    <select id="queryByStatus" parameterType="java.lang.Integer" resultMap="AiAgentTaskScheduleMap">
        SELECT id, agent_id, task_name, description, cron_expression, task_param, status, create_time, update_time
        FROM ai_agent_task_schedule
        WHERE status = #{status}
        ORDER BY id
    </select>

    <select id="queryAll" resultMap="AiAgentTaskScheduleMap">
        SELECT id, agent_id, task_name, description, cron_expression, task_param, status, create_time, update_time
        FROM ai_agent_task_schedule
        ORDER BY id
    </select>

</mapper>
