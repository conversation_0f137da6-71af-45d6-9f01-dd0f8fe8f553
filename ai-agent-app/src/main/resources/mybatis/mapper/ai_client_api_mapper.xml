<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.flyxy.ai.infrastructure.dao.IAiClientApiDao">

    <resultMap id="AiClientApiMap" type="com.flyxy.ai.infrastructure.dao.po.AiClientApi">
        <id column="id" property="id"/>
        <result column="api_id" property="apiId"/>
        <result column="base_url" property="baseUrl"/>
        <result column="api_key" property="apiKey"/>
        <result column="completions_path" property="completionsPath"/>
        <result column="embeddings_path" property="embeddingsPath"/>
        <result column="status" property="status"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <insert id="insert" parameterType="com.flyxy.ai.infrastructure.dao.po.AiClientApi" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO ai_client_api (api_id, base_url, api_key, completions_path, embeddings_path, status, create_time, update_time)
        VALUES (#{apiId}, #{baseUrl}, #{apiKey}, #{completionsPath}, #{embeddingsPath}, #{status}, #{createTime}, #{updateTime})
    </insert>

    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM ai_client_api WHERE id = #{id}
    </delete>

    <delete id="deleteByApiId" parameterType="java.lang.String">
        DELETE FROM ai_client_api WHERE api_id = #{apiId}
    </delete>

    <update id="update" parameterType="com.flyxy.ai.infrastructure.dao.po.AiClientApi">
        UPDATE ai_client_api SET
            base_url = #{baseUrl},
            api_key = #{apiKey},
            completions_path = #{completionsPath},
            embeddings_path = #{embeddingsPath},
            status = #{status},
            update_time = #{updateTime}
        WHERE id = #{id}
    </update>

    <select id="queryById" parameterType="java.lang.Long" resultMap="AiClientApiMap">
        SELECT id, api_id, base_url, api_key, completions_path, embeddings_path, status, create_time, update_time
        FROM ai_client_api
        WHERE id = #{id}
    </select>

    <select id="queryByApiId" parameterType="java.lang.String" resultMap="AiClientApiMap">
        SELECT id, api_id, base_url, api_key, completions_path, embeddings_path, status, create_time, update_time
        FROM ai_client_api
        WHERE api_id = #{apiId}
    </select>

    <select id="queryByStatus" parameterType="java.lang.Integer" resultMap="AiClientApiMap">
        SELECT id, api_id, base_url, api_key, completions_path, embeddings_path, status, create_time, update_time
        FROM ai_client_api
        WHERE status = #{status}
        ORDER BY id
    </select>

    <select id="queryAll" resultMap="AiClientApiMap">
        SELECT id, api_id, base_url, api_key, completions_path, embeddings_path, status, create_time, update_time
        FROM ai_client_api
        ORDER BY id
    </select>

</mapper>
