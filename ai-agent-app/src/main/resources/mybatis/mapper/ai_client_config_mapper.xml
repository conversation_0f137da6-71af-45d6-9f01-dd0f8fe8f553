<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.flyxy.ai.infrastructure.dao.IAiClientConfigDao">

    <resultMap id="AiClientConfigMap" type="com.flyxy.ai.infrastructure.dao.po.AiClientConfig">
        <id column="id" property="id"/>
        <result column="source_type" property="sourceType"/>
        <result column="source_id" property="sourceId"/>
        <result column="target_type" property="targetType"/>
        <result column="target_id" property="targetId"/>
        <result column="ext_param" property="extParam"/>
        <result column="status" property="status"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <insert id="insert" parameterType="com.flyxy.ai.infrastructure.dao.po.AiClientConfig" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO ai_client_config (source_type, source_id, target_type, target_id, ext_param, status, create_time, update_time)
        VALUES (#{sourceType}, #{sourceId}, #{targetType}, #{targetId}, #{extParam}, #{status}, #{createTime}, #{updateTime})
    </insert>

    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM ai_client_config WHERE id = #{id}
    </delete>

    <delete id="deleteBySourceId" parameterType="java.lang.String">
        DELETE FROM ai_client_config WHERE source_id = #{sourceId}
    </delete>

    <update id="update" parameterType="com.flyxy.ai.infrastructure.dao.po.AiClientConfig">
        UPDATE ai_client_config SET
            source_type = #{sourceType},
            source_id = #{sourceId},
            target_type = #{targetType},
            target_id = #{targetId},
            ext_param = #{extParam},
            status = #{status},
            update_time = #{updateTime}
        WHERE id = #{id}
    </update>

    <select id="queryById" parameterType="java.lang.Long" resultMap="AiClientConfigMap">
        SELECT id, source_type, source_id, target_type, target_id, ext_param, status, create_time, update_time
        FROM ai_client_config
        WHERE id = #{id}
    </select>

    <select id="queryBySourceId" parameterType="java.lang.String" resultMap="AiClientConfigMap">
        SELECT id, source_type, source_id, target_type, target_id, ext_param, status, create_time, update_time
        FROM ai_client_config
        WHERE source_id = #{sourceId}
        ORDER BY id
    </select>

    <select id="queryByTargetId" parameterType="java.lang.String" resultMap="AiClientConfigMap">
        SELECT id, source_type, source_id, target_type, target_id, ext_param, status, create_time, update_time
        FROM ai_client_config
        WHERE target_id = #{targetId}
        ORDER BY id
    </select>

    <select id="queryBySourceTypeAndId" resultMap="AiClientConfigMap">
        SELECT id, source_type, source_id, target_type, target_id, ext_param, status, create_time, update_time
        FROM ai_client_config
        WHERE source_type = #{sourceType} AND source_id = #{sourceId}
        ORDER BY id
    </select>

    <select id="queryByStatus" parameterType="java.lang.Integer" resultMap="AiClientConfigMap">
        SELECT id, source_type, source_id, target_type, target_id, ext_param, status, create_time, update_time
        FROM ai_client_config
        WHERE status = #{status}
        ORDER BY id
    </select>

    <select id="queryAll" resultMap="AiClientConfigMap">
        SELECT id, source_type, source_id, target_type, target_id, ext_param, status, create_time, update_time
        FROM ai_client_config
        ORDER BY id
    </select>

</mapper>
