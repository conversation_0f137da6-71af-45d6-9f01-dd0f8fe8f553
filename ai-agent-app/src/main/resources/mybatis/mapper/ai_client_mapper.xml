<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.flyxy.ai.infrastructure.dao.IAiClientDao">

    <resultMap id="AiClientMap" type="com.flyxy.ai.infrastructure.dao.po.AiClient">
        <id column="id" property="id"/>
        <result column="client_id" property="clientId"/>
        <result column="client_name" property="clientName"/>
        <result column="description" property="description"/>
        <result column="status" property="status"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <insert id="insert" parameterType="com.flyxy.ai.infrastructure.dao.po.AiClient" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO ai_client (client_id, client_name, description, status, create_time, update_time)
        VALUES (#{clientId}, #{clientName}, #{description}, #{status}, #{createTime}, #{updateTime})
    </insert>

    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM ai_client WHERE id = #{id}
    </delete>

    <delete id="deleteByClientId" parameterType="java.lang.String">
        DELETE FROM ai_client WHERE client_id = #{clientId}
    </delete>

    <update id="update" parameterType="com.flyxy.ai.infrastructure.dao.po.AiClient">
        UPDATE ai_client SET
            client_name = #{clientName},
            description = #{description},
            status = #{status},
            update_time = #{updateTime}
        WHERE id = #{id}
    </update>

    <select id="queryById" parameterType="java.lang.Long" resultMap="AiClientMap">
        SELECT id, client_id, client_name, description, status, create_time, update_time
        FROM ai_client
        WHERE id = #{id}
    </select>

    <select id="queryByClientId" parameterType="java.lang.String" resultMap="AiClientMap">
        SELECT id, client_id, client_name, description, status, create_time, update_time
        FROM ai_client
        WHERE client_id = #{clientId}
    </select>

    <select id="queryAll" resultMap="AiClientMap">
        SELECT id, client_id, client_name, description, status, create_time, update_time
        FROM ai_client
        ORDER BY id
    </select>

    <select id="queryByStatus" parameterType="java.lang.Integer" resultMap="AiClientMap">
        SELECT id, client_id, client_name, description, status, create_time, update_time
        FROM ai_client
        WHERE status = #{status}
        ORDER BY id
    </select>

</mapper>
