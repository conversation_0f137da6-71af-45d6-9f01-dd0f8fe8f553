<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.flyxy.ai.infrastructure.dao.IAiClientModelDao">

    <resultMap id="AiClientModelMap" type="com.flyxy.ai.infrastructure.dao.po.AiClientModel">
        <id column="id" property="id"/>
        <result column="model_id" property="modelId"/>
        <result column="api_id" property="apiId"/>
        <result column="model_name" property="modelName"/>
        <result column="model_type" property="modelType"/>
        <result column="status" property="status"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <insert id="insert" parameterType="com.flyxy.ai.infrastructure.dao.po.AiClientModel" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO ai_client_model (model_id, api_id, model_name, model_type, status, create_time, update_time)
        VALUES (#{modelId}, #{apiId}, #{modelName}, #{modelType}, #{status}, #{createTime}, #{updateTime})
    </insert>

    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM ai_client_model WHERE id = #{id}
    </delete>

    <delete id="deleteByModelId" parameterType="java.lang.String">
        DELETE FROM ai_client_model WHERE model_id = #{modelId}
    </delete>

    <update id="update" parameterType="com.flyxy.ai.infrastructure.dao.po.AiClientModel">
        UPDATE ai_client_model SET
            api_id = #{apiId},
            model_name = #{modelName},
            model_type = #{modelType},
            status = #{status},
            update_time = #{updateTime}
        WHERE id = #{id}
    </update>

    <select id="queryById" parameterType="java.lang.Long" resultMap="AiClientModelMap">
        SELECT id, model_id, api_id, model_name, model_type, status, create_time, update_time
        FROM ai_client_model
        WHERE id = #{id}
    </select>

    <select id="queryByModelId" parameterType="java.lang.String" resultMap="AiClientModelMap">
        SELECT id, model_id, api_id, model_name, model_type, status, create_time, update_time
        FROM ai_client_model
        WHERE model_id = #{modelId}
    </select>

    <select id="queryByApiId" parameterType="java.lang.String" resultMap="AiClientModelMap">
        SELECT id, model_id, api_id, model_name, model_type, status, create_time, update_time
        FROM ai_client_model
        WHERE api_id = #{apiId}
        ORDER BY id
    </select>

    <select id="queryByModelType" parameterType="java.lang.String" resultMap="AiClientModelMap">
        SELECT id, model_id, api_id, model_name, model_type, status, create_time, update_time
        FROM ai_client_model
        WHERE model_type = #{modelType}
        ORDER BY id
    </select>

    <select id="queryByStatus" parameterType="java.lang.Integer" resultMap="AiClientModelMap">
        SELECT id, model_id, api_id, model_name, model_type, status, create_time, update_time
        FROM ai_client_model
        WHERE status = #{status}
        ORDER BY id
    </select>

    <select id="queryAll" resultMap="AiClientModelMap">
        SELECT id, model_id, api_id, model_name, model_type, status, create_time, update_time
        FROM ai_client_model
        ORDER BY id
    </select>

</mapper>
