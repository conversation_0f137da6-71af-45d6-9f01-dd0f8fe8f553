<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.flyxy.ai.infrastructure.dao.IAiClientRagOrderDao">

    <resultMap id="AiClientRagOrderMap" type="com.flyxy.ai.infrastructure.dao.po.AiClientRagOrder">
        <id column="id" property="id"/>
        <result column="rag_id" property="ragId"/>
        <result column="rag_name" property="ragName"/>
        <result column="knowledge_tag" property="knowledgeTag"/>
        <result column="status" property="status"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <insert id="insert" parameterType="com.flyxy.ai.infrastructure.dao.po.AiClientRagOrder" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO ai_client_rag_order (rag_id, rag_name, knowledge_tag, status, create_time, update_time)
        VALUES (#{ragId}, #{ragName}, #{knowledgeTag}, #{status}, #{createTime}, #{updateTime})
    </insert>

    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM ai_client_rag_order WHERE id = #{id}
    </delete>

    <delete id="deleteByRagId" parameterType="java.lang.String">
        DELETE FROM ai_client_rag_order WHERE rag_id = #{ragId}
    </delete>

    <update id="update" parameterType="com.flyxy.ai.infrastructure.dao.po.AiClientRagOrder">
        UPDATE ai_client_rag_order SET
            rag_name = #{ragName},
            knowledge_tag = #{knowledgeTag},
            status = #{status},
            update_time = #{updateTime}
        WHERE id = #{id}
    </update>

    <select id="queryById" parameterType="java.lang.Long" resultMap="AiClientRagOrderMap">
        SELECT id, rag_id, rag_name, knowledge_tag, status, create_time, update_time
        FROM ai_client_rag_order
        WHERE id = #{id}
    </select>

    <select id="queryByRagId" parameterType="java.lang.String" resultMap="AiClientRagOrderMap">
        SELECT id, rag_id, rag_name, knowledge_tag, status, create_time, update_time
        FROM ai_client_rag_order
        WHERE rag_id = #{ragId}
    </select>

    <select id="queryByKnowledgeTag" parameterType="java.lang.String" resultMap="AiClientRagOrderMap">
        SELECT id, rag_id, rag_name, knowledge_tag, status, create_time, update_time
        FROM ai_client_rag_order
        WHERE knowledge_tag = #{knowledgeTag}
        ORDER BY id
    </select>

    <select id="queryByStatus" parameterType="java.lang.Integer" resultMap="AiClientRagOrderMap">
        SELECT id, rag_id, rag_name, knowledge_tag, status, create_time, update_time
        FROM ai_client_rag_order
        WHERE status = #{status}
        ORDER BY id
    </select>

    <select id="queryAll" resultMap="AiClientRagOrderMap">
        SELECT id, rag_id, rag_name, knowledge_tag, status, create_time, update_time
        FROM ai_client_rag_order
        ORDER BY id
    </select>

</mapper>
