<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.flyxy.ai.infrastructure.dao.IAiClientSystemPromptDao">

    <resultMap id="AiClientSystemPromptMap" type="com.flyxy.ai.infrastructure.dao.po.AiClientSystemPrompt">
        <id column="id" property="id"/>
        <result column="prompt_id" property="promptId"/>
        <result column="prompt_name" property="promptName"/>
        <result column="prompt_content" property="promptContent"/>
        <result column="description" property="description"/>
        <result column="status" property="status"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <insert id="insert" parameterType="com.flyxy.ai.infrastructure.dao.po.AiClientSystemPrompt" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO ai_client_system_prompt (prompt_id, prompt_name, prompt_content, description, status, create_time, update_time)
        VALUES (#{promptId}, #{promptName}, #{promptContent}, #{description}, #{status}, #{createTime}, #{updateTime})
    </insert>

    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM ai_client_system_prompt WHERE id = #{id}
    </delete>

    <delete id="deleteByPromptId" parameterType="java.lang.String">
        DELETE FROM ai_client_system_prompt WHERE prompt_id = #{promptId}
    </delete>

    <update id="update" parameterType="com.flyxy.ai.infrastructure.dao.po.AiClientSystemPrompt">
        UPDATE ai_client_system_prompt SET
            prompt_name = #{promptName},
            prompt_content = #{promptContent},
            description = #{description},
            status = #{status},
            update_time = #{updateTime}
        WHERE id = #{id}
    </update>

    <select id="queryById" parameterType="java.lang.Long" resultMap="AiClientSystemPromptMap">
        SELECT id, prompt_id, prompt_name, prompt_content, description, status, create_time, update_time
        FROM ai_client_system_prompt
        WHERE id = #{id}
    </select>

    <select id="queryByPromptId" parameterType="java.lang.String" resultMap="AiClientSystemPromptMap">
        SELECT id, prompt_id, prompt_name, prompt_content, description, status, create_time, update_time
        FROM ai_client_system_prompt
        WHERE prompt_id = #{promptId}
    </select>

    <select id="queryByPromptName" parameterType="java.lang.String" resultMap="AiClientSystemPromptMap">
        SELECT id, prompt_id, prompt_name, prompt_content, description, status, create_time, update_time
        FROM ai_client_system_prompt
        WHERE prompt_name LIKE CONCAT('%', #{promptName}, '%')
        ORDER BY id
    </select>

    <select id="queryByStatus" parameterType="java.lang.Integer" resultMap="AiClientSystemPromptMap">
        SELECT id, prompt_id, prompt_name, prompt_content, description, status, create_time, update_time
        FROM ai_client_system_prompt
        WHERE status = #{status}
        ORDER BY id
    </select>

    <select id="queryAll" resultMap="AiClientSystemPromptMap">
        SELECT id, prompt_id, prompt_name, prompt_content, description, status, create_time, update_time
        FROM ai_client_system_prompt
        ORDER BY id
    </select>

</mapper>
