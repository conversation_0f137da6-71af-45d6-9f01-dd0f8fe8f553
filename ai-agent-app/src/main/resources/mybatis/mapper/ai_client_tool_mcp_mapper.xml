<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.flyxy.ai.infrastructure.dao.IAiClientToolMcpDao">

    <resultMap id="AiClientToolMcpMap" type="com.flyxy.ai.infrastructure.dao.po.AiClientToolMcp">
        <id column="id" property="id"/>
        <result column="mcp_id" property="mcpId"/>
        <result column="mcp_name" property="mcpName"/>
        <result column="transport_type" property="transportType"/>
        <result column="transport_config" property="transportConfig"/>
        <result column="request_timeout" property="requestTimeout"/>
        <result column="status" property="status"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <insert id="insert" parameterType="com.flyxy.ai.infrastructure.dao.po.AiClientToolMcp" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO ai_client_tool_mcp (mcp_id, mcp_name, transport_type, transport_config, request_timeout, status, create_time, update_time)
        VALUES (#{mcpId}, #{mcpName}, #{transportType}, #{transportConfig}, #{requestTimeout}, #{status}, #{createTime}, #{updateTime})
    </insert>

    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM ai_client_tool_mcp WHERE id = #{id}
    </delete>

    <delete id="deleteByMcpId" parameterType="java.lang.String">
        DELETE FROM ai_client_tool_mcp WHERE mcp_id = #{mcpId}
    </delete>

    <update id="update" parameterType="com.flyxy.ai.infrastructure.dao.po.AiClientToolMcp">
        UPDATE ai_client_tool_mcp SET
            mcp_name = #{mcpName},
            transport_type = #{transportType},
            transport_config = #{transportConfig},
            request_timeout = #{requestTimeout},
            status = #{status},
            update_time = #{updateTime}
        WHERE id = #{id}
    </update>

    <select id="queryById" parameterType="java.lang.Long" resultMap="AiClientToolMcpMap">
        SELECT id, mcp_id, mcp_name, transport_type, transport_config, request_timeout, status, create_time, update_time
        FROM ai_client_tool_mcp
        WHERE id = #{id}
    </select>

    <select id="queryByMcpId" parameterType="java.lang.String" resultMap="AiClientToolMcpMap">
        SELECT id, mcp_id, mcp_name, transport_type, transport_config, request_timeout, status, create_time, update_time
        FROM ai_client_tool_mcp
        WHERE mcp_id = #{mcpId}
    </select>

    <select id="queryByTransportType" parameterType="java.lang.String" resultMap="AiClientToolMcpMap">
        SELECT id, mcp_id, mcp_name, transport_type, transport_config, request_timeout, status, create_time, update_time
        FROM ai_client_tool_mcp
        WHERE transport_type = #{transportType}
        ORDER BY id
    </select>

    <select id="queryByStatus" parameterType="java.lang.Integer" resultMap="AiClientToolMcpMap">
        SELECT id, mcp_id, mcp_name, transport_type, transport_config, request_timeout, status, create_time, update_time
        FROM ai_client_tool_mcp
        WHERE status = #{status}
        ORDER BY id
    </select>

    <select id="queryAll" resultMap="AiClientToolMcpMap">
        SELECT id, mcp_id, mcp_name, transport_type, transport_config, request_timeout, status, create_time, update_time
        FROM ai_client_tool_mcp
        ORDER BY id
    </select>

</mapper>
