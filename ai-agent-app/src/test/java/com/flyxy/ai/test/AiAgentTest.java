package com.flyxy.ai.test;


import com.alibaba.fastjson.JSON;
import com.flyxy.ai.test.advisors.RagAnswerAdvisor;
import io.modelcontextprotocol.client.McpClient;
import io.modelcontextprotocol.client.McpSyncClient;
import io.modelcontextprotocol.client.transport.HttpClientSseClientTransport;
import io.modelcontextprotocol.client.transport.ServerParameters;
import io.modelcontextprotocol.client.transport.StdioClientTransport;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.client.advisor.PromptChatMemoryAdvisor;
import org.springframework.ai.chat.client.advisor.SimpleLoggerAdvisor;
import org.springframework.ai.chat.memory.MessageWindowChatMemory;
import org.springframework.ai.chat.messages.AssistantMessage;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.mcp.SyncMcpToolCallbackProvider;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.ai.openai.api.OpenAiApi;
import org.springframework.ai.vectorstore.SearchRequest;
import org.springframework.ai.vectorstore.pgvector.PgVectorStore;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import reactor.core.publisher.Flux;

import java.time.Duration;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CountDownLatch;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class AiAgentTest {

    private ChatModel chatModel;

    private ChatClient chatClient;

    @Resource
    private PgVectorStore vectorStore;

    public static final String CHAT_MEMORY_CONVERSATION_ID_KEY = "chat_memory_conversation_id";
    public static final String CHAT_MEMORY_RETRIEVE_SIZE_KEY = "chat_memory_response_size";

    @Before
    public void init() {

        OpenAiApi openAiApi = OpenAiApi.builder()
                .baseUrl("https://dashscope.aliyuncs.com/compatible-mode")
                .apiKey("sk-8abc1e796f7e43ed8782ffc5a60ca968")
                /*.completionsPath("v1/chat/completions")
                .embeddingsPath("v1/embeddings")*/
                .build();

        chatModel = OpenAiChatModel.builder()
                .openAiApi(openAiApi)
                .defaultOptions(OpenAiChatOptions.builder()
                        .model("deepseek-v3")
                        .toolCallbacks(new SyncMcpToolCallbackProvider(sseAiSearchMcp()).getToolCallbacks())
                        .build())
                .build();

        chatClient = ChatClient.builder(chatModel)
                .defaultSystem("""
                          角色
                          你是一个专业的信息搜索员，名叫 AutoAgent Info Researcher。
                          核心职责
                          你负责搜索、整理和验证信息：
                          信息收集: 高效搜索并获取用户需要的最新、最准确的信息
                          信息筛选: 甄别可靠来源，去除冗余和不相关内容
                          信息整合: 将多渠道信息汇总、提炼，形成结构化输出
                          信息验证: 核实信息的可信度和准确性
                          评估标准
                          准确性: 信息是否来自可靠来源，是否正确
                          完整性: 是否覆盖用户需求的关键点
                          相关性: 是否紧扣用户问题，不跑题
                          可用性: 信息是否能被用户直接应用
                          输出格式:
                          [信息收集]:
                          [搜集到的核心信息摘要]
                          [信息筛选]:
                          [筛选后保留的有价值信息，说明来源可靠性]
                          [信息整合]:
                          [对信息进行整理和结构化输出，确保逻辑清晰]
                          [信息验证]:
                          [验证信息的准确性，标明是否有多方来源支持]
                          [信息评分]: [0-100]分
                          [是否推荐使用]: [YES/NO/OPTIMIZE]
                        """)
                /*.defaultToolCallbacks(new SyncMcpToolCallbackProvider(stdioMcpClient(), sseMcpClient01(), sseMcpClient02()).getToolCallbacks())
                .defaultAdvisors(
                        PromptChatMemoryAdvisor.builder(
                                        MessageWindowChatMemory.builder()
                                                .maxMessages(100)
                                                .build()
                                )
                                .build(),
                        new RagAnswerAdvisor(vectorStore, SearchRequest.builder()
                                .topK(5)
                                .filterExpression("knowledge == 'article-prompt-words'")
                                .build()),
                        SimpleLoggerAdvisor.builder()
                                .build())*/
                .build();
    }

    @Test
    public void test_chat_model_stream_01() throws InterruptedException {
        CountDownLatch countDownLatch = new CountDownLatch(1);

        Prompt prompt = Prompt.builder()
                .messages(new UserMessage(
                        """
                                以撒的结合背景故事
                                """))
                .build();

        // 非流式，chatModel.call(prompt)
        splitByNewline(
                chatClient.prompt(prompt)
                        .system(s -> s.param("current_date", LocalDate.now().toString()))
                        .stream()
                        .content()
        )
                .map(contentLine -> parseSearchResult(1, contentLine))
                .doOnNext(System.out::print)  // 消费每个流式返回的内容片段
                .doOnComplete(countDownLatch::countDown) // 流完成时释放锁
                .subscribe(); // 显式订阅触发流执行

        countDownLatch.await();
    }

    @Test
    public void test_chat_model_call() {
        Prompt prompt = Prompt.builder()
                .messages(new UserMessage(
                        """
                                我想了解一下博德之门世界观
                                """))
                .build();

        ChatResponse chatResponse = chatModel.call(prompt);

        log.info("测试结果(call):{}", JSON.toJSONString(chatResponse));
    }

    @Test
    public void test_chat_mcp_wx() {
        Prompt prompt = Prompt.builder()
                .messages(new UserMessage(
                        """
                                微信公众号通知一次，需要的参数你自由发挥即可
                                """))
                .build();

        ChatResponse chatResponse = chatModel.call(prompt);

        log.info("测试结果(call):{}", JSON.toJSONString(chatResponse));
    }

    @Test
    public void test_02() {
        String userInput = "王大瓜今年几岁";
        System.out.println("\n>>> QUESTION: " + userInput);
        System.out.println("\n>>> ASSISTANT: " + chatClient
                .prompt(userInput)
                .system(s -> s.param("current_date", LocalDate.now()
                        .toString()))
                .call()
                .content());
    }

    @Test
    public void test_client03() {
        ChatClient chatClient01 = ChatClient.builder(chatModel)
                .defaultSystem("""
                        你是一个专业的AI提示词优化专家。请帮我优化以下prompt，并按照以下格式返回：
                        
                        # Role: [角色名称]
                        
                        ## Profile
                        - language: [语言]
                        - description: [详细的角色描述]
                        - background: [角色背景]
                        - personality: [性格特征]
                        - expertise: [专业领域]
                        - target_audience: [目标用户群]
                        
                        ## Skills
                        
                        1. [核心技能类别]
                           - [具体技能]: [简要说明]
                           - [具体技能]: [简要说明]
                           - [具体技能]: [简要说明]
                           - [具体技能]: [简要说明]
                        
                        2. [辅助技能类别]
                           - [具体技能]: [简要说明]
                           - [具体技能]: [简要说明]
                           - [具体技能]: [简要说明]
                           - [具体技能]: [简要说明]
                        
                        ## Rules
                        
                        1. [基本原则]：
                           - [具体规则]: [详细说明]
                           - [具体规则]: [详细说明]
                           - [具体规则]: [详细说明]
                           - [具体规则]: [详细说明]
                        
                        2. [行为准则]：
                           - [具体规则]: [详细说明]
                           - [具体规则]: [详细说明]
                           - [具体规则]: [详细说明]
                           - [具体规则]: [详细说明]
                        
                        3. [限制条件]：
                           - [具体限制]: [详细说明]
                           - [具体限制]: [详细说明]
                           - [具体限制]: [详细说明]
                           - [具体限制]: [详细说明]
                        
                        ## Workflows
                        
                        - 目标: [明确目标]
                        - 步骤 1: [详细说明]
                        - 步骤 2: [详细说明]
                        - 步骤 3: [详细说明]
                        - 预期结果: [说明]
                        
                        
                        ## Initialization
                        作为[角色名称]，你必须遵守上述Rules，按照Workflows执行任务。
                        
                        请基于以上模板，优化并扩展以下prompt，确保内容专业、完整且结构清晰，注意不要携带任何引导词或解释，不要使用代码块包围。
                        """)
                .defaultAdvisors(
                        PromptChatMemoryAdvisor.builder(
                                        MessageWindowChatMemory.builder()
                                                .maxMessages(100)
                                                .build()
                                )
                                .build(),
                        new RagAnswerAdvisor(vectorStore, SearchRequest.builder()
                                .topK(5)
                                .filterExpression("knowledge == 'article-prompt-words'")
                                .build())
                )
                .defaultOptions(OpenAiChatOptions.builder()
                        .model("qwen-plus")
                        .build())
                .build();

        String content = chatClient01
                .prompt("生成一篇关于最新AI技术的文章")

                .system(s -> s.param("current_date", LocalDate.now()
                        .toString()))
                .advisors(a -> a
                        .param(CHAT_MEMORY_CONVERSATION_ID_KEY, "chatId-101")
                        .param(CHAT_MEMORY_RETRIEVE_SIZE_KEY, 100))
                .call()
                .content();

        System.out.println("\n>>> ASSISTANT: " + content);

        ChatClient chatClient02 = ChatClient.builder(chatModel)
                .defaultSystem("""
                        	 你是一个 AI Agent 智能体，可以根据用户输入信息生成文章，并发送到 CSDN 平台以及完成微信公众号消息通知，今天是 {current_date}。
                        
                        	 你擅长使用Planning模式，帮助用户生成质量更高的文章。
                        
                        	 你的规划应该包括以下几个方面：
                        	 1. 分析用户输入的内容，生成技术文章。
                        	 2. 提取，文章标题（需要含带技术点）、文章内容、文章标签（多个用英文逗号隔开）、文章简述（100字）将以上内容发布文章到CSDN
                        	 3. 获取发送到 CSDN 文章的 URL 地址。
                        	 4. 微信公众号消息通知，平台：CSDN、主题：为文章标题、描述：为文章简述、跳转地址：为发布文章到CSDN获取 URL地址 CSDN文章链接 https 开头的地址。
                        """)
//                .defaultTools(new SyncMcpToolCallbackProvider(sseMcpClient01(), sseMcpClient02()))
                .defaultAdvisors(
                        PromptChatMemoryAdvisor.builder(
                                        MessageWindowChatMemory.builder()
                                                .maxMessages(100)
                                                .build()
                                )
                                .build(),
                        new SimpleLoggerAdvisor()
                )
                .defaultOptions(OpenAiChatOptions.builder()
                        .model("qwen-plus")
                        .build())
                .build();

        String userInput = "生成一篇文章，要求如下 \r\n" + content;
        System.out.println("\n>>> QUESTION: " + userInput);
        System.out.println("\n>>> ASSISTANT: " + chatClient02
                .prompt(userInput)
                .system(s -> s.param("current_date", LocalDate.now()
                        .toString()))
                .advisors(a -> a
                        .param(CHAT_MEMORY_CONVERSATION_ID_KEY, "chatId-101")
                        .param(CHAT_MEMORY_RETRIEVE_SIZE_KEY, 100))
                .call()
                .content());
    }

    public McpSyncClient stdioMcpClient() {

        // based on
        // https://github.com/modelcontextprotocol/servers/tree/main/src/filesystem
        var stdioParams = ServerParameters.builder("npx")
                .args("-y", "@modelcontextprotocol/server-filesystem", "/Users/<USER>/Desktop", "/Users/<USER>/Desktop")
                .build();

        var mcpClient = McpClient.sync(new StdioClientTransport(stdioParams))
                .requestTimeout(Duration.ofSeconds(10))
                .build();

        var init = mcpClient.initialize();

        System.out.println("Stdio MCP Initialized: " + init);

        return mcpClient;

    }

    public McpSyncClient sseMcpClient01() {

        HttpClientSseClientTransport sseClientTransport = HttpClientSseClientTransport.builder("http://192.168.100.150:8102")
                .build();

        McpSyncClient mcpSyncClient = McpClient.sync(sseClientTransport)
                .requestTimeout(Duration.ofMinutes(180))
                .build();

        var init = mcpSyncClient.initialize();
        System.out.println("SSE MCP Initialized: " + init);

        return mcpSyncClient;
    }

    public McpSyncClient sseAiSearchMcp() {

        HttpClientSseClientTransport sseClientTransport = HttpClientSseClientTransport.builder("http://appbuilder.baidu.com/v2/ai_search/mcp/")
                .sseEndpoint("sse?api_key=Bearer+bce-v3/ALTAK-DWc6SG2SOf1HwQTT5LSPS/399922dbdb2f52553341b25940a5d6d42b670594")
                .build();

        McpSyncClient mcpSyncClient = McpClient.sync(sseClientTransport)
                .requestTimeout(Duration.ofMinutes(180))
                .build();

        var init = mcpSyncClient.initialize();
        System.out.println("SSE MCP Initialized: " + init);

        return mcpSyncClient;
    }

    public McpSyncClient sseMcpClient02() {

        HttpClientSseClientTransport sseClientTransport = HttpClientSseClientTransport.builder("http://192.168.100.150:8101")
                .build();

        McpSyncClient mcpSyncClient = McpClient.sync(sseClientTransport)
                .requestTimeout(Duration.ofMinutes(180))
                .build();

        var init = mcpSyncClient.initialize();
        System.out.println("SSE MCP Initialized: " + init);

        return mcpSyncClient;
    }


    private String parseSearchResult(int step, String analysisResult) {
        StringBuilder resultBuilder = new StringBuilder();
        String[] lines = analysisResult.split("\n");
        String currentSection = "";

        for (String line : lines) {
            line = line.trim();
            if (line.isEmpty()) continue;

            if (line.contains("信息收集")) {
                currentSection = "collection";
                resultBuilder.append("\n🔍 信息收集\n");
                continue;
            } else if (line.contains("信息筛选")) {
                currentSection = "filtering";
                resultBuilder.append("\n⚙️  信息筛选\n");
                continue;
            } else if (line.contains("信息整合")) {
                currentSection = "integration";
                resultBuilder.append("\n🧩 信息整合\n");
                continue;
            } else if (line.contains("信息验证")) {
                currentSection = "verification";
                resultBuilder.append("\n✅ 信息验证\n");
                continue;
            } else if (line.contains("信息评分")) {
                currentSection = "scoring";
                String score = line.substring(line.indexOf(":") + 1)
                        .trim();
                resultBuilder.append("\n📊 信息评分 ")
                        .append(score)
                        .append("\n");
                continue;
            } else if (line.contains("是否推荐使用")) {
                currentSection = "recommendation";
                String recommendation = line.substring(line.indexOf(":") + 1)
                        .trim();
                if (recommendation.equals("YES")) {
                    resultBuilder.append("\n👍 是否推荐使用: 推荐使用\n");
                } else if (recommendation.equals("NO")) {
                    resultBuilder.append("\n👎 是否推荐使用: 不推荐使用\n");
                } else if (recommendation.equals("OPTIMIZE")) {
                    resultBuilder.append("\n🔧 是否推荐使用: 建议优化后使用\n");
                }
                continue;
            }

            switch (currentSection) {
                case "collection":
                    resultBuilder.append("   📚 ")
                            .append(line)
                            .append("\n");
                    break;
                case "filtering":
                    resultBuilder.append("   🗂️  ")
                            .append(line)
                            .append("\n");
                    break;
                case "integration":
                    resultBuilder.append("   🧱 ")
                            .append(line)
                            .append("\n");
                    break;
                case "verification":
                    resultBuilder.append("   🧪 ")
                            .append(line)
                            .append("\n");
                    break;
                case "scoring":
                    // 已在上面处理
                    break;
                case "recommendation":
                    // 已在上面处理
                    break;
                default:
                    resultBuilder.append(line)
                            .append("\n");
                    break;
            }
        }

        return resultBuilder.toString();
    }

    /**
     * 将 Flux<String> 按行切分输出（保留换行符）。
     *
     * @param flux 原始字符串流（可能是碎片化的 token）
     * @return 按行切分后的 Flux<String>
     */
    public static Flux<String> splitByNewline(Flux<String> flux) {
        return flux
                // 用 StringBuilder 做累积，处理碎片化 chunk
                .scanWith(StringBuilder::new, (sb, chunk) -> {
                    sb.append(chunk);
                    return sb;
                })
                // 把 StringBuilder 转换成 “已完成的行”
                .flatMapIterable(sb -> {
                    List<String> lines = new ArrayList<>();
                    int start = 0;
                    for (int i = 0; i < sb.length(); i++) {
                        if (sb.charAt(i) == '\n') {
                            lines.add(sb.substring(start, i + 1));
                            start = i + 1;
                        }
                    }
                    if (start > 0) {
                        // 删除已输出的部分，保留未完成的行
                        sb.delete(0, start);
                    }
                    return lines;
                })
                // 过滤掉空字符串，避免多余事件
                .filter(line -> !line.isEmpty());
    }

}
