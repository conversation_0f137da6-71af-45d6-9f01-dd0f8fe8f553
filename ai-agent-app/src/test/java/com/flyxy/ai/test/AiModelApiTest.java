package com.flyxy.ai.test;

import com.alibaba.fastjson.JSON;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.ai.chat.messages.AssistantMessage;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.chat.prompt.PromptTemplate;
import org.springframework.ai.chat.prompt.SystemPromptTemplate;
import org.springframework.ai.content.Media;
import org.springframework.ai.document.Document;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.ai.reader.tika.TikaDocumentReader;
import org.springframework.ai.transformer.splitter.TokenTextSplitter;
import org.springframework.ai.vectorstore.SearchRequest;
import org.springframework.ai.vectorstore.pgvector.PgVectorStore;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.MimeType;
import org.springframework.util.MimeTypeUtils;
import reactor.core.publisher.Flux;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Author: flyxy
 * @Date: 2025/8/26
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class AiModelApiTest {

    @Value("classpath:data/dog.png")
    private org.springframework.core.io.Resource imageResource;

    @Value("classpath:data/file.txt")
    private org.springframework.core.io.Resource textResource;

    @Value("classpath:data/异世界.txt")
    private org.springframework.core.io.Resource storyResource;

    @Value("classpath:data/article-prompt-words.txt")
    private org.springframework.core.io.Resource articlePromptWordsResource;

    @Resource
    private OpenAiChatModel openAiChatModel;

    @Resource
    private PgVectorStore pgVectorStore;

    private static final TokenTextSplitter tokenTextSplitter = new TokenTextSplitter();

    @Test
    public void call_test() {

        ChatResponse chatResponse = openAiChatModel.call(new Prompt("你是谁？",
                OpenAiChatOptions.builder()
                        .model("qwen-plus")
                        .build()));

        log.info("测试结果: {}", JSON.toJSONString(chatResponse));

    }

    @Test
    public void call_stream_test() throws InterruptedException {

        CountDownLatch countDownLatch = new CountDownLatch(1);

        Flux<ChatResponse> stream = openAiChatModel.stream(new Prompt(
                "1+1",
                OpenAiChatOptions.builder()
                        .model("deepseek-v3")
                        .build()));

        stream.subscribe(
                chatResponse -> {
                    AssistantMessage output = chatResponse.getResult()
                            .getOutput();
                    log.info("测试结果(stream): {}", JSON.toJSONString(output));
                },
                Throwable::printStackTrace,
                () -> {
                    countDownLatch.countDown();
                    log.info("测试结果(stream): done!");
                }
        );

        countDownLatch.await();
    }

    @Test
    public void call_image_test() {
        UserMessage userMessage = UserMessage.builder()
                .text("描绘一下图片中的内容")
                .media(Media.builder()
                        .mimeType(MimeTypeUtils.IMAGE_PNG)
                        .data(imageResource)
                        .build())
                .build();
        ChatResponse chatResponse = openAiChatModel.call(new Prompt(userMessage, OpenAiChatOptions.builder()
                .model("qwen-vl-plus")
                .build()));
        log.info("测试结果: {}", JSON.toJSONString(chatResponse));
    }

    @Test
    public void rag_upload_test() {
        TikaDocumentReader tikaDocumentReader = new TikaDocumentReader(storyResource);

        List<Document> documents = tikaDocumentReader.get();
        List<Document> documentSplitList = tokenTextSplitter.split(documents);
        documentSplitList.forEach(document -> document.getMetadata()
                .put("knowledge", "异世界"));

        pgVectorStore.accept(documentSplitList);

        log.info("上传完成");
    }

    @Test
    public void call_with_rag_test() {
        String message = "小说的主角叫什么";

        String SYSTEM_PROMPT = """
                Use the information from the DOCUMENTS section to provide accurate answers but act as if you knew this information innately.
                If unsure, simply state that you don't know.
                Another thing you need to note is that your reply must be in Chinese!
                DOCUMENTS:
                    {documents}
                """;

        SearchRequest searchRequest = SearchRequest.builder()
                .query(message)
                .topK(5)
                .filterExpression("knowledge == '异世界'")
                .build();

        List<Document> documents = pgVectorStore.similaritySearch(searchRequest);

        String documentsCollectors = documents == null ? "" : documents.stream()
                .map(Document::getText)
                .collect(Collectors.joining("\n"));
        Message ragMessage = new SystemPromptTemplate(SYSTEM_PROMPT).createMessage(Map.of("documents", documentsCollectors));

        List<Message> finalMessages = List.of(new UserMessage(message), ragMessage);

        ChatResponse chatResponse = openAiChatModel.call(new Prompt(finalMessages, OpenAiChatOptions.builder()
                .model("qwen-plus")
                .build()));

        log.info("测试结果: {}", JSON.toJSONString(chatResponse));
    }
}
