package com.flyxy.ai.test.ai;

import cn.bugstack.wrench.design.framework.tree.StrategyHandler;
import com.flyxy.ai.domain.agent.model.entity.ArmoryCommandEntity;
import com.flyxy.ai.domain.agent.model.entity.ExecuteCommandEntity;
import com.flyxy.ai.domain.agent.model.valobj.enums.AiAgentEnumVO;
import com.flyxy.ai.domain.agent.service.armory.factory.DefaultArmoryStrategyFactory;
import com.flyxy.ai.domain.agent.service.exec.IExecuteStrategy;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.junit4.SpringRunner;
import reactor.core.publisher.Flux;

import java.time.Duration;
import java.util.Arrays;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

/**
 * @Description: 流式AutoAgent测试
 * @Author: flyxy
 * @Date: 2025/9/7
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class StreamAutoAgentTest {

    @Resource
    private DefaultArmoryStrategyFactory defaultArmoryStrategyFactory;

    @Resource
    private IExecuteStrategy autoExecuteStrategy;

    @Resource
    private ApplicationContext applicationContext;

    /*@Before
    public void init() throws Exception {
        StrategyHandler<ArmoryCommandEntity, DefaultArmoryStrategyFactory.DynamicContext, String> armoryStrategyHandler =
                defaultArmoryStrategyFactory.armoryStrategyHandler();

    }*/

    @Test
    public void testStreamAutoAgent() throws Exception {
        log.info("开始测试流式AutoAgent");

        ExecuteCommandEntity executeCommandEntity = new ExecuteCommandEntity();
        executeCommandEntity.setAiAgentId("3");
        executeCommandEntity.setMessage("请帮我分析一下人工智能的发展趋势，并给出学习建议");
        executeCommandEntity.setSessionId("stream-session-" + System.currentTimeMillis());
        executeCommandEntity.setMaxStep(2);

        CountDownLatch latch = new CountDownLatch(1);
        StringBuilder result = new StringBuilder();

        // 创建流式测试
        Flux<String> streamFlux = Flux.create(sink -> {
            try {
                autoExecuteStrategy.execute(executeCommandEntity, sink);
            } catch (Exception e) {
                log.error("执行异常", e);
                sink.error(e);
            }
        });

        // 订阅流并处理结果
        streamFlux
                .doOnNext(content -> {
                    log.info("收到流式内容: {}", content);
                    result.append(content);
                })
                .doOnError(error -> {
                    log.error("流式执行出错", error);
                    latch.countDown();
                })
                .doOnComplete(() -> {
                    log.info("流式执行完成");
                    latch.countDown();
                })
                .subscribe();

        // 等待执行完成，最多等待5分钟
        boolean completed = latch.await(5, TimeUnit.MINUTES);

        if (completed) {
            log.info("测试完成，总结果长度: {}", result.length());
            log.info("完整结果: \n{}", result.toString());
        } else {
            log.warn("测试超时");
        }
    }

    @Test
    public void testStreamAutoAgentWithTimeout() throws Exception {
        log.info("开始测试带超时的流式AutoAgent");

        ExecuteCommandEntity executeCommandEntity = new ExecuteCommandEntity();
        executeCommandEntity.setAiAgentId("3");
        executeCommandEntity.setMessage("简单测试任务");
        executeCommandEntity.setSessionId("timeout-session-" + System.currentTimeMillis());
        executeCommandEntity.setMaxStep(1);

        // 创建流式测试
        Flux<String> streamFlux = Flux.create(sink -> {
            try {
                autoExecuteStrategy.execute(executeCommandEntity, sink);
            } catch (Exception e) {
                log.error("执行异常", e);
                sink.error(e);
            }
        });

        // 使用timeout和collectList来测试
        try {
            String result = streamFlux
                    .timeout(Duration.ofMinutes(2))
                    .collectList()
                    .block()
                    .stream()
                    .reduce("", (a, b) -> a + b);

            log.info("测试完成，结果: \n{}", result);
        } catch (Exception e) {
            log.error("测试异常", e);
        }
    }
}
