package com.flyxy.ai.test.ai;

import com.flyxy.ai.domain.agent.service.exec.auto.step.StreamContextManager;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import reactor.core.publisher.Flux;
import reactor.core.publisher.FluxSink;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @Description: 流式输出功能测试 - 验证真正的流式效果
 * @Author: flyxy
 * @Date: 2025/9/7
 */
@Slf4j
public class StreamingFlowTest {

    @Test
    public void testRealStreamingOutput() throws Exception {
        log.info("开始测试真正的流式输出效果");
        
        CountDownLatch latch = new CountDownLatch(1);
        List<String> receivedContent = new ArrayList<>();
        List<Long> timestamps = new ArrayList<>();
        AtomicInteger contentCount = new AtomicInteger(0);
        
        long startTime = System.currentTimeMillis();
        
        // 创建流式测试
        Flux<String> streamFlux = Flux.create(sink -> {
            try {
                // 创建StreamContextManager
                StreamContextManager streamManager = new StreamContextManager(sink);
                
                // 模拟真正的流式输出：逐步推送内容
                streamManager.appendStepStart("任务分析", 1);
                
                // 模拟Step1的逐行输出
                String[] step1Lines = {
                    "正在分析用户任务...",
                    "识别任务类型：测试任务",
                    "评估任务复杂度：简单",
                    "制定执行策略：直接执行",
                    "任务分析完成"
                };
                for (String line : step1Lines) {
                    streamManager.appendContent(line + "\n", "Step1-分析");
                    Thread.sleep(200); // 模拟真实流式延迟
                }
                
                streamManager.appendStepStart("精准执行", 2);
                
                // 模拟Step2的逐行输出
                String[] step2Lines = {
                    "开始执行任务...",
                    "初始化执行环境",
                    "执行核心逻辑",
                    "处理业务数据",
                    "执行完成：任务已成功处理"
                };
                for (String line : step2Lines) {
                    streamManager.appendContent(line + "\n", "Step2-执行");
                    Thread.sleep(200); // 模拟真实流式延迟
                }
                
                streamManager.appendStepStart("质量监督", 3);
                
                // 模拟Step3的逐行输出
                String[] step3Lines = {
                    "正在进行质量检查...",
                    "检查执行结果完整性",
                    "验证数据准确性",
                    "评估执行效率",
                    "✅ 质量检查通过"
                };
                for (String line : step3Lines) {
                    streamManager.appendContent(line + "\n", "Step3-检查");
                    Thread.sleep(200); // 模拟真实流式延迟
                }
                
                streamManager.appendStepStart("质量总结", 4);
                
                // 模拟Step4的逐行输出
                String[] step4Lines = {
                    "生成最终总结...",
                    "汇总执行过程",
                    "分析执行效果",
                    "生成质量报告",
                    "总结完成：任务执行成功，质量良好"
                };
                for (String line : step4Lines) {
                    streamManager.appendContent(line + "\n", "Step4-总结");
                    Thread.sleep(200); // 模拟真实流式延迟
                }
                
                // 完成流
                streamManager.complete();
                
            } catch (Exception e) {
                log.error("测试异常", e);
                sink.error(e);
            }
        });
        
        // 订阅流并记录接收时间
        streamFlux
                .doOnNext(content -> {
                    long currentTime = System.currentTimeMillis();
                    timestamps.add(currentTime - startTime);
                    receivedContent.add(content);
                    int count = contentCount.incrementAndGet();
                    log.info("第{}条内容 ({}ms): {}", count, currentTime - startTime, 
                            content.replace("\n", "\\n").substring(0, Math.min(50, content.length())));
                })
                .doOnError(error -> {
                    log.error("流式执行出错", error);
                    latch.countDown();
                })
                .doOnComplete(() -> {
                    log.info("流式执行完成");
                    latch.countDown();
                })
                .subscribe();
        
        // 等待执行完成
        boolean completed = latch.await(30, TimeUnit.SECONDS);
        
        if (completed) {
            long totalTime = System.currentTimeMillis() - startTime;
            log.info("测试完成，总耗时: {}ms", totalTime);
            log.info("总共接收到 {} 条内容", receivedContent.size());
            
            // 验证流式效果：检查时间间隔
            boolean hasStreamingEffect = false;
            for (int i = 1; i < timestamps.size(); i++) {
                long interval = timestamps.get(i) - timestamps.get(i-1);
                if (interval > 100) { // 如果间隔超过100ms，说明有流式效果
                    hasStreamingEffect = true;
                    break;
                }
            }
            
            // 验证结果
            String fullResult = String.join("", receivedContent);
            assert fullResult.contains("开始执行 任务分析 (第1步)") : "缺少Step1开始标识";
            assert fullResult.contains("开始执行 精准执行 (第2步)") : "缺少Step2开始标识";
            assert fullResult.contains("开始执行 质量监督 (第3步)") : "缺少Step3开始标识";
            assert fullResult.contains("开始执行 质量总结 (第4步)") : "缺少Step4开始标识";
            assert fullResult.contains("正在分析用户任务") : "缺少Step1内容";
            assert fullResult.contains("任务执行成功，质量良好") : "缺少Step4内容";
            assert hasStreamingEffect : "没有检测到流式效果";
            assert receivedContent.size() > 10 : "接收到的内容条数太少";
            
            log.info("✅ 所有断言通过，真正的流式功能正常工作");
            log.info("✅ 检测到流式效果，内容是逐步推送的");
        } else {
            log.warn("❌ 测试超时");
            throw new RuntimeException("测试超时");
        }
    }
    
    @Test
    public void testStreamingPerformance() throws Exception {
        log.info("开始测试流式输出性能");
        
        // 创建流式测试
        Flux<String> streamFlux = Flux.create(sink -> {
            try {
                StreamContextManager streamManager = new StreamContextManager(sink);
                
                // 快速推送大量内容测试性能
                streamManager.appendStepStart("性能测试", 1);
                for (int i = 1; i <= 100; i++) {
                    streamManager.appendContent("测试内容 " + i + "\n", "性能测试");
                    Thread.sleep(10); // 很短的延迟
                }
                streamManager.complete();
                
            } catch (Exception e) {
                log.error("性能测试异常", e);
                sink.error(e);
            }
        });
        
        // 使用timeout和collectList来测试
        try {
            long startTime = System.currentTimeMillis();
            List<String> result = streamFlux
                    .timeout(Duration.ofSeconds(30))
                    .collectList()
                    .block();
            long endTime = System.currentTimeMillis();
            
            log.info("性能测试完成，耗时: {}ms", endTime - startTime);
            log.info("接收到 {} 条内容", result.size());
            
            // 验证结果
            assert result.size() > 100 : "接收到的内容数量不足";
            assert (endTime - startTime) > 1000 : "执行时间太短，可能没有真正的流式效果";
            
            log.info("✅ 性能测试通过");
        } catch (Exception e) {
            log.error("❌ 性能测试异常", e);
            throw e;
        }
    }
}
