package com.flyxy.ai.test.dao;

import com.flyxy.ai.infrastructure.dao.IAiAgentDao;
import com.flyxy.ai.infrastructure.dao.po.AiAgent;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.time.LocalDateTime;
import java.util.List;

/**
 * AI智能体配置表 DAO测试
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class AiAgentDaoTest {

    @Resource
    private IAiAgentDao aiAgentDao;

    @Test
    public void test_insert() {
        AiAgent aiAgent = AiAgent.builder()
                .agentId("test_agent_001")
                .agentName("测试智能体")
                .description("这是一个测试智能体")
                .channel("agent")
                .status(1)
                .createTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now())
                .build();

        int result = aiAgentDao.insert(aiAgent);
        log.info("插入结果: {}, 生成ID: {}", result, aiAgent.getId());
    }

    @Test
    public void test_queryById() {
        AiAgent aiAgent = aiAgentDao.queryById(1L);
        log.info("查询结果: {}", aiAgent);
    }

    @Test
    public void test_queryByAgentId() {
        AiAgent aiAgent = aiAgentDao.queryByAgentId("test_agent_001");
        log.info("查询结果: {}", aiAgent);
    }

    @Test
    public void test_queryAll() {
        List<AiAgent> aiAgents = aiAgentDao.queryAll();
        log.info("查询所有结果数量: {}", aiAgents.size());
        aiAgents.forEach(agent -> log.info("智能体: {}", agent));
    }

    @Test
    public void test_queryByStatus() {
        List<AiAgent> aiAgents = aiAgentDao.queryByStatus(1);
        log.info("查询启用状态结果数量: {}", aiAgents.size());
        aiAgents.forEach(agent -> log.info("智能体: {}", agent));
    }

    @Test
    public void test_update() {
        AiAgent aiAgent = aiAgentDao.queryByAgentId("test_agent_001");
        if (aiAgent != null) {
            aiAgent.setAgentName("更新后的测试智能体");
            aiAgent.setDescription("这是一个更新后的测试智能体");
            aiAgent.setUpdateTime(LocalDateTime.now());
            
            int result = aiAgentDao.update(aiAgent);
            log.info("更新结果: {}", result);
        }
    }

    @Test
    public void test_deleteByAgentId() {
        int result = aiAgentDao.deleteByAgentId("test_agent_001");
        log.info("删除结果: {}", result);
    }

}
