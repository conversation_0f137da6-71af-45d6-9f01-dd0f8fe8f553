package com.flyxy.ai.test.dao;

import com.flyxy.ai.infrastructure.dao.IAiClientApiDao;
import com.flyxy.ai.infrastructure.dao.po.AiClientApi;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.time.LocalDateTime;
import java.util.List;

/**
 * OpenAI API配置表 DAO测试
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class AiClientApiDaoTest {

    @Resource
    private IAiClientApiDao aiClientApiDao;

    @Test
    public void test_insert() {
        AiClientApi aiClientApi = AiClientApi.builder()
                .apiId("test_api_001")
                .baseUrl("https://api.openai.com")
                .apiKey("sk-test123456")
                .completionsPath("v1/chat/completions")
                .embeddingsPath("v1/embeddings")
                .status(1)
                .createTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now())
                .build();

        int result = aiClientApiDao.insert(aiClientApi);
        log.info("插入结果: {}, 生成ID: {}", result, aiClientApi.getId());
    }

    @Test
    public void test_queryById() {
        AiClientApi aiClientApi = aiClientApiDao.queryById(1L);
        log.info("查询结果: {}", aiClientApi);
    }

    @Test
    public void test_queryByApiId() {
        AiClientApi aiClientApi = aiClientApiDao.queryByApiId("test_api_001");
        log.info("查询结果: {}", aiClientApi);
    }

    @Test
    public void test_queryAll() {
        List<AiClientApi> aiClientApis = aiClientApiDao.queryAll();
        log.info("查询所有结果数量: {}", aiClientApis.size());
        aiClientApis.forEach(api -> log.info("API配置: {}", api));
    }

    @Test
    public void test_queryByStatus() {
        List<AiClientApi> aiClientApis = aiClientApiDao.queryByStatus(1);
        log.info("查询启用状态结果数量: {}", aiClientApis.size());
        aiClientApis.forEach(api -> log.info("API配置: {}", api));
    }

    @Test
    public void test_update() {
        AiClientApi aiClientApi = aiClientApiDao.queryByApiId("test_api_001");
        if (aiClientApi != null) {
            aiClientApi.setBaseUrl("https://api.openai.com/v2");
            aiClientApi.setUpdateTime(LocalDateTime.now());
            
            int result = aiClientApiDao.update(aiClientApi);
            log.info("更新结果: {}", result);
        }
    }

    @Test
    public void test_deleteByApiId() {
        int result = aiClientApiDao.deleteByApiId("test_api_001");
        log.info("删除结果: {}", result);
    }

}
