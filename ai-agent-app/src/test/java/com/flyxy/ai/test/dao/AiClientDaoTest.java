package com.flyxy.ai.test.dao;

import com.flyxy.ai.infrastructure.dao.IAiClientDao;
import com.flyxy.ai.infrastructure.dao.po.AiClient;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.time.LocalDateTime;
import java.util.List;

/**
 * AI客户端配置表 DAO测试
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class AiClientDaoTest {

    @Resource
    private IAiClientDao aiClientDao;

    @Test
    public void test_insert() {
        AiClient aiClient = AiClient.builder()
                .clientId("test_client_001")
                .clientName("测试客户端")
                .description("这是一个测试客户端")
                .status(1)
                .createTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now())
                .build();

        int result = aiClientDao.insert(aiClient);
        log.info("插入结果: {}, 生成ID: {}", result, aiClient.getId());
    }

    @Test
    public void test_queryById() {
        AiClient aiClient = aiClientDao.queryById(1L);
        log.info("查询结果: {}", aiClient);
    }

    @Test
    public void test_queryByClientId() {
        AiClient aiClient = aiClientDao.queryByClientId("test_client_001");
        log.info("查询结果: {}", aiClient);
    }

    @Test
    public void test_queryAll() {
        List<AiClient> aiClients = aiClientDao.queryAll();
        log.info("查询所有结果数量: {}", aiClients.size());
        aiClients.forEach(client -> log.info("客户端: {}", client));
    }

    @Test
    public void test_queryByStatus() {
        List<AiClient> aiClients = aiClientDao.queryByStatus(1);
        log.info("查询启用状态结果数量: {}", aiClients.size());
        aiClients.forEach(client -> log.info("客户端: {}", client));
    }

    @Test
    public void test_update() {
        AiClient aiClient = aiClientDao.queryByClientId("test_client_001");
        if (aiClient != null) {
            aiClient.setClientName("更新后的测试客户端");
            aiClient.setDescription("这是一个更新后的测试客户端");
            aiClient.setUpdateTime(LocalDateTime.now());
            
            int result = aiClientDao.update(aiClient);
            log.info("更新结果: {}", result);
        }
    }

    @Test
    public void test_deleteByClientId() {
        int result = aiClientDao.deleteByClientId("test_client_001");
        log.info("删除结果: {}", result);
    }

}
