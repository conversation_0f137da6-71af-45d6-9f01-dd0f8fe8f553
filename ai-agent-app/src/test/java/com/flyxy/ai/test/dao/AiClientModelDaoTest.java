package com.flyxy.ai.test.dao;

import com.flyxy.ai.infrastructure.dao.IAiClientModelDao;
import com.flyxy.ai.infrastructure.dao.po.AiClientModel;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 聊天模型配置表 DAO测试
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class AiClientModelDaoTest {

    @Resource
    private IAiClientModelDao aiClientModelDao;

    @Test
    public void test_insert() {
        AiClientModel aiClientModel = AiClientModel.builder()
                .modelId("test_model_001")
                .apiId("test_api_001")
                .modelName("gpt-4")
                .modelType("openai")
                .status(1)
                .createTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now())
                .build();

        int result = aiClientModelDao.insert(aiClientModel);
        log.info("插入结果: {}, 生成ID: {}", result, aiClientModel.getId());
    }

    @Test
    public void test_queryById() {
        AiClientModel aiClientModel = aiClientModelDao.queryById(1L);
        log.info("查询结果: {}", aiClientModel);
    }

    @Test
    public void test_queryByModelId() {
        AiClientModel aiClientModel = aiClientModelDao.queryByModelId("test_model_001");
        log.info("查询结果: {}", aiClientModel);
    }

    @Test
    public void test_queryByApiId() {
        List<AiClientModel> aiClientModels = aiClientModelDao.queryByApiId("test_api_001");
        log.info("根据API ID查询结果数量: {}", aiClientModels.size());
        aiClientModels.forEach(model -> log.info("模型配置: {}", model));
    }

    @Test
    public void test_queryByModelType() {
        List<AiClientModel> aiClientModels = aiClientModelDao.queryByModelType("openai");
        log.info("根据模型类型查询结果数量: {}", aiClientModels.size());
        aiClientModels.forEach(model -> log.info("模型配置: {}", model));
    }

    @Test
    public void test_queryAll() {
        List<AiClientModel> aiClientModels = aiClientModelDao.queryAll();
        log.info("查询所有结果数量: {}", aiClientModels.size());
        aiClientModels.forEach(model -> log.info("模型配置: {}", model));
    }

    @Test
    public void test_queryByStatus() {
        List<AiClientModel> aiClientModels = aiClientModelDao.queryByStatus(1);
        log.info("查询启用状态结果数量: {}", aiClientModels.size());
        aiClientModels.forEach(model -> log.info("模型配置: {}", model));
    }

    @Test
    public void test_update() {
        AiClientModel aiClientModel = aiClientModelDao.queryByModelId("test_model_001");
        if (aiClientModel != null) {
            aiClientModel.setModelName("gpt-4-turbo");
            aiClientModel.setUpdateTime(LocalDateTime.now());
            
            int result = aiClientModelDao.update(aiClientModel);
            log.info("更新结果: {}", result);
        }
    }

    @Test
    public void test_deleteByModelId() {
        int result = aiClientModelDao.deleteByModelId("test_model_001");
        log.info("删除结果: {}", result);
    }

}
