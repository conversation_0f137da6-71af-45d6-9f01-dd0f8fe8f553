package com.flyxy.ai.test.dao;

import com.flyxy.ai.infrastructure.dao.IAiClientSystemPromptDao;
import com.flyxy.ai.infrastructure.dao.po.AiClientSystemPrompt;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 系统提示词配置表 DAO测试
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class AiClientSystemPromptDaoTest {

    @Resource
    private IAiClientSystemPromptDao aiClientSystemPromptDao;

    @Test
    public void test_insert() {
        AiClientSystemPrompt aiClientSystemPrompt = AiClientSystemPrompt.builder()
                .promptId("test_prompt_001")
                .promptName("测试提示词")
                .promptContent("你是一个专业的AI助手，请帮助用户解决问题。")
                .description("这是一个测试提示词")
                .status(1)
                .createTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now())
                .build();

        int result = aiClientSystemPromptDao.insert(aiClientSystemPrompt);
        log.info("插入结果: {}, 生成ID: {}", result, aiClientSystemPrompt.getId());
    }

    @Test
    public void test_queryById() {
        AiClientSystemPrompt aiClientSystemPrompt = aiClientSystemPromptDao.queryById(1L);
        log.info("查询结果: {}", aiClientSystemPrompt);
    }

    @Test
    public void test_queryByPromptId() {
        AiClientSystemPrompt aiClientSystemPrompt = aiClientSystemPromptDao.queryByPromptId("test_prompt_001");
        log.info("查询结果: {}", aiClientSystemPrompt);
    }

    @Test
    public void test_queryByPromptName() {
        List<AiClientSystemPrompt> aiClientSystemPrompts = aiClientSystemPromptDao.queryByPromptName("测试");
        log.info("根据提示词名称查询结果数量: {}", aiClientSystemPrompts.size());
        aiClientSystemPrompts.forEach(prompt -> log.info("提示词配置: {}", prompt));
    }

    @Test
    public void test_queryAll() {
        List<AiClientSystemPrompt> aiClientSystemPrompts = aiClientSystemPromptDao.queryAll();
        log.info("查询所有结果数量: {}", aiClientSystemPrompts.size());
        aiClientSystemPrompts.forEach(prompt -> log.info("提示词配置: {}", prompt));
    }

    @Test
    public void test_queryByStatus() {
        List<AiClientSystemPrompt> aiClientSystemPrompts = aiClientSystemPromptDao.queryByStatus(1);
        log.info("查询启用状态结果数量: {}", aiClientSystemPrompts.size());
        aiClientSystemPrompts.forEach(prompt -> log.info("提示词配置: {}", prompt));
    }

    @Test
    public void test_update() {
        AiClientSystemPrompt aiClientSystemPrompt = aiClientSystemPromptDao.queryByPromptId("test_prompt_001");
        if (aiClientSystemPrompt != null) {
            aiClientSystemPrompt.setPromptName("更新后的测试提示词");
            aiClientSystemPrompt.setPromptContent("你是一个更专业的AI助手，请帮助用户解决复杂问题。");
            aiClientSystemPrompt.setUpdateTime(LocalDateTime.now());
            
            int result = aiClientSystemPromptDao.update(aiClientSystemPrompt);
            log.info("更新结果: {}", result);
        }
    }

    @Test
    public void test_deleteByPromptId() {
        int result = aiClientSystemPromptDao.deleteByPromptId("test_prompt_001");
        log.info("删除结果: {}", result);
    }

}
