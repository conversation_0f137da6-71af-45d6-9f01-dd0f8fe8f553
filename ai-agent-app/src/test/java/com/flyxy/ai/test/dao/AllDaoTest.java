package com.flyxy.ai.test.dao;

import com.flyxy.ai.infrastructure.dao.*;
import com.flyxy.ai.infrastructure.dao.po.*;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.time.LocalDateTime;

/**
 * 所有DAO综合测试
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class AllDaoTest {

    @Resource
    private IAiAgentDao aiAgentDao;
    @Resource
    private IAiAgentFlowConfigDao aiAgentFlowConfigDao;
    @Resource
    private IAiAgentTaskScheduleDao aiAgentTaskScheduleDao;
    @Resource
    private IAiClientDao aiClientDao;
    @Resource
    private IAiClientAdvisorDao aiClientAdvisorDao;
    @Resource
    private IAiClientApiDao aiClientApiDao;
    @Resource
    private IAiClientConfigDao aiClientConfigDao;
    @Resource
    private IAiClientModelDao aiClientModelDao;
    @Resource
    private IAiClientRagOrderDao aiClientRagOrderDao;
    @Resource
    private IAiClientSystemPromptDao aiClientSystemPromptDao;
    @Resource
    private IAiClientToolMcpDao aiClientToolMcpDao;

    @Test
    public void test_aiAgent_crud() {
        log.info("=== 测试 AiAgent CRUD ===");
        
        // 插入
        AiAgent aiAgent = AiAgent.builder()
                .agentId("test_001")
                .agentName("测试智能体")
                .description("测试描述")
                .channel("agent")
                .status(1)
                .createTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now())
                .build();
        
        int insertResult = aiAgentDao.insert(aiAgent);
        log.info("插入结果: {}, ID: {}", insertResult, aiAgent.getId());
        
        // 查询
        AiAgent queryResult = aiAgentDao.queryByAgentId("test_001");
        log.info("查询结果: {}", queryResult);
        
        // 更新
        if (queryResult != null) {
            queryResult.setAgentName("更新后的智能体");
            queryResult.setUpdateTime(LocalDateTime.now());
            int updateResult = aiAgentDao.update(queryResult);
            log.info("更新结果: {}", updateResult);
        }
        
        // 删除
        int deleteResult = aiAgentDao.deleteByAgentId("test_001");
        log.info("删除结果: {}", deleteResult);
    }

    @Test
    public void test_aiClient_crud() {
        log.info("=== 测试 AiClient CRUD ===");
        
        // 插入
        AiClient aiClient = AiClient.builder()
                .clientId("test_client_001")
                .clientName("测试客户端")
                .description("测试客户端描述")
                .status(1)
                .createTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now())
                .build();
        
        int insertResult = aiClientDao.insert(aiClient);
        log.info("插入结果: {}, ID: {}", insertResult, aiClient.getId());
        
        // 查询
        AiClient queryResult = aiClientDao.queryByClientId("test_client_001");
        log.info("查询结果: {}", queryResult);
        
        // 删除
        int deleteResult = aiClientDao.deleteByClientId("test_client_001");
        log.info("删除结果: {}", deleteResult);
    }

    @Test
    public void test_aiClientApi_crud() {
        log.info("=== 测试 AiClientApi CRUD ===");
        
        // 插入
        AiClientApi aiClientApi = AiClientApi.builder()
                .apiId("test_api_001")
                .baseUrl("https://api.test.com")
                .apiKey("sk-test123")
                .completionsPath("v1/chat/completions")
                .embeddingsPath("v1/embeddings")
                .status(1)
                .createTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now())
                .build();
        
        int insertResult = aiClientApiDao.insert(aiClientApi);
        log.info("插入结果: {}, ID: {}", insertResult, aiClientApi.getId());
        
        // 查询
        AiClientApi queryResult = aiClientApiDao.queryByApiId("test_api_001");
        log.info("查询结果: {}", queryResult);
        
        // 删除
        int deleteResult = aiClientApiDao.deleteByApiId("test_api_001");
        log.info("删除结果: {}", deleteResult);
    }

    @Test
    public void test_aiClientModel_crud() {
        log.info("=== 测试 AiClientModel CRUD ===");
        
        // 插入
        AiClientModel aiClientModel = AiClientModel.builder()
                .modelId("test_model_001")
                .apiId("test_api_001")
                .modelName("gpt-4")
                .modelType("openai")
                .status(1)
                .createTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now())
                .build();
        
        int insertResult = aiClientModelDao.insert(aiClientModel);
        log.info("插入结果: {}, ID: {}", insertResult, aiClientModel.getId());
        
        // 查询
        AiClientModel queryResult = aiClientModelDao.queryByModelId("test_model_001");
        log.info("查询结果: {}", queryResult);
        
        // 删除
        int deleteResult = aiClientModelDao.deleteByModelId("test_model_001");
        log.info("删除结果: {}", deleteResult);
    }

    @Test
    public void test_aiClientSystemPrompt_crud() {
        log.info("=== 测试 AiClientSystemPrompt CRUD ===");
        
        // 插入
        AiClientSystemPrompt prompt = AiClientSystemPrompt.builder()
                .promptId("test_prompt_001")
                .promptName("测试提示词")
                .promptContent("你是一个AI助手")
                .description("测试提示词描述")
                .status(1)
                .createTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now())
                .build();
        
        int insertResult = aiClientSystemPromptDao.insert(prompt);
        log.info("插入结果: {}, ID: {}", insertResult, prompt.getId());
        
        // 查询
        AiClientSystemPrompt queryResult = aiClientSystemPromptDao.queryByPromptId("test_prompt_001");
        log.info("查询结果: {}", queryResult);
        
        // 删除
        int deleteResult = aiClientSystemPromptDao.deleteByPromptId("test_prompt_001");
        log.info("删除结果: {}", deleteResult);
    }

    @Test
    public void test_queryAll() {
        log.info("=== 测试查询所有数据 ===");
        
        log.info("AiAgent 数量: {}", aiAgentDao.queryAll().size());
        log.info("AiClient 数量: {}", aiClientDao.queryAll().size());
        log.info("AiClientApi 数量: {}", aiClientApiDao.queryAll().size());
        log.info("AiClientModel 数量: {}", aiClientModelDao.queryAll().size());
        log.info("AiClientSystemPrompt 数量: {}", aiClientSystemPromptDao.queryAll().size());
        log.info("AiClientAdvisor 数量: {}", aiClientAdvisorDao.queryAll().size());
        log.info("AiClientConfig 数量: {}", aiClientConfigDao.queryAll().size());
        log.info("AiClientRagOrder 数量: {}", aiClientRagOrderDao.queryAll().size());
        log.info("AiClientToolMcp 数量: {}", aiClientToolMcpDao.queryAll().size());
        log.info("AiAgentFlowConfig 数量: {}", aiAgentFlowConfigDao.queryAll().size());
        log.info("AiAgentTaskSchedule 数量: {}", aiAgentTaskScheduleDao.queryAll().size());
    }

}
