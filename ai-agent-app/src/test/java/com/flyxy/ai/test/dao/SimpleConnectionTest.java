package com.flyxy.ai.test.dao;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.sql.DataSource;
import java.sql.Connection;

/**
 * 简单的数据库连接测试
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class SimpleConnectionTest {

    @Resource
    private DataSource mysqlDataSource;

    @Test
    public void test_connection() {
        try {
            Connection connection = mysqlDataSource.getConnection();
            log.info("数据库连接成功: {}", connection);
            connection.close();
        } catch (Exception e) {
            log.error("数据库连接失败", e);
        }
    }

}
