package com.flyxy.ai.domain.agent.model.entity;

import com.flyxy.ai.domain.agent.model.valobj.enums.AiAgentEnumVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Description:
 * @Author: flyxy
 * @Date: 2025/8/28
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ArmoryCommandEntity {

    /**
     * 命令类型
     */
    private String commandType;

    /**
     * 命令索引（clientId、modelId、apiId...）
     */
    private List<String> commandIdList;


    public AiAgentEnumVO getCommandType(){
        return AiAgentEnumVO.getByCode(commandType);
    }
}
