package com.flyxy.ai.domain.agent.model.valobj;

import com.flyxy.ai.domain.agent.model.valobj.enums.AiClientTypeEnumVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description:
 * @Author: flyxy
 * @Date: 2025/9/1
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AiAgentClientFlowConfigVO {
    /**
     * 智能体ID
     */
    private String agentId;

    /**
     * 客户端ID
     */
    private String clientId;

    /**
     * 客户端名称
     */
    private String clientName;

    /**
     * 客户端枚举
     */
    private AiClientTypeEnumVO clientTypeEnumVO;

    /**
     * 序列号(执行顺序)
     */
    private Integer sequence;
}
