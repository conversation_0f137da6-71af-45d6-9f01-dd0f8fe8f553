package com.flyxy.ai.domain.agent.model.valobj.enums;

import com.flyxy.ai.domain.agent.model.valobj.AiClientAdvisorVO;
import com.flyxy.ai.domain.agent.service.armory.factory.element.RagAnswerAdvisor;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.springframework.ai.chat.client.advisor.PromptChatMemoryAdvisor;
import org.springframework.ai.chat.client.advisor.api.Advisor;
import org.springframework.ai.chat.memory.MessageWindowChatMemory;
import org.springframework.ai.vectorstore.SearchRequest;
import org.springframework.ai.vectorstore.pgvector.PgVectorStore;

import java.util.HashMap;
import java.util.Map;

/**
 * @Description:
 * @Author: flyxy
 * @Date: 2025/8/30
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum AiClientAdvisorTypeEnumVO {

    RAG_ANSWER("知识库", "RagAnswer") {
        @Override
        public Advisor createAdvisor(AiClientAdvisorVO advisorVO, PgVectorStore vectorStore) {

            AiClientAdvisorVO.RagAnswer ragAnswer = advisorVO.getRagAnswer();
            return new RagAnswerAdvisor(vectorStore, SearchRequest.builder()
                    .topK(ragAnswer.getTopK())
                    .filterExpression(ragAnswer.getFilterExpression())
                    .build());
        }
    },

    CHAT_MEMORY("上下文记忆（内存模式", "ChatMemory") {
        @Override
        public Advisor createAdvisor(AiClientAdvisorVO advisorVO, PgVectorStore vectorStore) {

            return PromptChatMemoryAdvisor.builder(
                    MessageWindowChatMemory.builder()
                            .maxMessages(100)
                            .build())
                    .build();
        }
    },
    ;


    private String name;

    private String code;

    private static final Map<String, AiClientAdvisorTypeEnumVO> CODE_MAP = new HashMap<>();

    // 静态初始化块，在类加载时初始化Map
    static {
        for (AiClientAdvisorTypeEnumVO enumVO : values()) {
            CODE_MAP.put(enumVO.getCode(), enumVO);
        }
    }


    public abstract Advisor createAdvisor(AiClientAdvisorVO advisorVO, PgVectorStore vectorStore);


    public static AiClientAdvisorTypeEnumVO getByCode(String code) {
        AiClientAdvisorTypeEnumVO advisorTypeEnumVO = CODE_MAP.get(code);
        if (advisorTypeEnumVO == null) {
            throw new RuntimeException("err! 未知的顾问类型 code:" + code);
        }
        return advisorTypeEnumVO;
    }

}
