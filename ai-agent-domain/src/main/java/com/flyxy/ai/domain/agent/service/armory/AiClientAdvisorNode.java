package com.flyxy.ai.domain.agent.service.armory;

import cn.bugstack.wrench.design.framework.tree.StrategyHandler;
import com.flyxy.ai.domain.agent.model.entity.ArmoryCommandEntity;
import com.flyxy.ai.domain.agent.model.valobj.enums.AiAgentEnumVO;
import com.flyxy.ai.domain.agent.model.valobj.enums.AiClientAdvisorTypeEnumVO;
import com.flyxy.ai.domain.agent.model.valobj.AiClientAdvisorVO;
import com.flyxy.ai.domain.agent.service.armory.factory.DefaultArmoryStrategyFactory;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.advisor.api.Advisor;
import org.springframework.ai.vectorstore.pgvector.PgVectorStore;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description:
 * @Author: flyxy
 * @Date: 2025/8/30
 */
@Slf4j
@Service
public class AiClientAdvisorNode extends AbstractArmorySupport{

    @Resource
    private PgVectorStore vectorStore;

    @Resource
    private AiClientNode aiClientNode;


    @Override
    protected String doApply(ArmoryCommandEntity requestParameter, DefaultArmoryStrategyFactory.DynamicContext dynamicContext) throws Exception {
        log.info("AIClient装配链---AiClientAdvisorNode---开始装配顾问");
        List<AiClientAdvisorVO> advisorVOS = dynamicContext.getValue(dataName());

        if (advisorVOS == null || advisorVOS.isEmpty()){
            log.info("没有顾问配置");
            return router(requestParameter, dynamicContext);
        }

        for (AiClientAdvisorVO advisorVO : advisorVOS) {
            AiClientAdvisorTypeEnumVO advisorTypeEnumVO = AiClientAdvisorTypeEnumVO.getByCode(advisorVO.getAdvisorType());
            Advisor advisor = null;
            advisor = advisorTypeEnumVO.createAdvisor(advisorVO, vectorStore);
            registerBean(beanName(advisorVO.getAdvisorId()), Advisor.class, advisor);
            log.info("注册顾问---advisorId:{}, advisorName:{}---成功", advisorVO.getAdvisorId(), advisorVO.getAdvisorName());
        }

        return router(requestParameter, dynamicContext);
    }

    @Override
    public StrategyHandler<ArmoryCommandEntity, DefaultArmoryStrategyFactory.DynamicContext, String> get(ArmoryCommandEntity requestParameter, DefaultArmoryStrategyFactory.DynamicContext dynamicContext) throws Exception {
        return aiClientNode;
    }

    @Override
    protected String beanName(String advisorId) {
        return AiAgentEnumVO.AI_CLIENT_ADVISOR.getBeanName(advisorId);
    }

    @Override
    protected String dataName() {
        return AiAgentEnumVO.AI_CLIENT_ADVISOR.getDataName();
    }
}
