package com.flyxy.ai.domain.agent.service.armory;

import cn.bugstack.wrench.design.framework.tree.StrategyHandler;
import com.flyxy.ai.domain.agent.model.entity.ArmoryCommandEntity;
import com.flyxy.ai.domain.agent.model.valobj.enums.AiAgentEnumVO;
import com.flyxy.ai.domain.agent.model.valobj.AiClientApiVO;
import com.flyxy.ai.domain.agent.service.armory.factory.DefaultArmoryStrategyFactory;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.openai.api.OpenAiApi;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description:
 * @Author: flyxy
 * @Date: 2025/8/28
 */
@Slf4j
@Service
public class AiClientApiNode extends AbstractArmorySupport {

    @Resource
    private AiClientMcpNode aiClientMcpNode;


    @Override
    protected String doApply(ArmoryCommandEntity requestParameter, DefaultArmoryStrategyFactory.DynamicContext dynamicContext) throws Exception {
        log.info("AIClient装配链---AiClientApiNode---开始装配API");

        List<AiClientApiVO> apiVOS = dynamicContext.getValue(AiAgentEnumVO.AI_CLIENT_API.getDataName());
        // 如果没有api需要配置，直接进入下个节点
        if (apiVOS == null || apiVOS.isEmpty()) {
            return router(requestParameter, dynamicContext);
        }
        for (AiClientApiVO apiVO : apiVOS) {
            OpenAiApi openAiApi = OpenAiApi.builder()
                    .apiKey(apiVO.getApiKey())
                    .baseUrl(apiVO.getBaseUrl())
                    //.completionsPath(apiVO.getCompletionsPath())
                    //.embeddingsPath(apiVO.getEmbeddingsPath())
                    .build();
            registerBean(AiAgentEnumVO.AI_CLIENT_API.getBeanName(apiVO.getApiId()), OpenAiApi.class, openAiApi);
        }

        return router(requestParameter, dynamicContext);
    }

    @Override
    public StrategyHandler<ArmoryCommandEntity, DefaultArmoryStrategyFactory.DynamicContext, String> get(ArmoryCommandEntity requestParameter, DefaultArmoryStrategyFactory.DynamicContext dynamicContext) throws Exception {
        return aiClientMcpNode;
    }

    @Override
    protected String beanName(String apiId) {
        return AiAgentEnumVO.AI_CLIENT_API.getBeanName(apiId);
    }

    @Override
    protected String dataName() {
        return AiAgentEnumVO.AI_CLIENT_API.getDataName();
    }
}
