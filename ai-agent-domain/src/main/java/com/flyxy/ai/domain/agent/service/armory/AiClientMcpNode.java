package com.flyxy.ai.domain.agent.service.armory;

import cn.bugstack.wrench.design.framework.tree.StrategyHandler;
import com.flyxy.ai.domain.agent.model.entity.ArmoryCommandEntity;
import com.flyxy.ai.domain.agent.model.valobj.enums.AiAgentEnumVO;
import com.flyxy.ai.domain.agent.model.valobj.AiClientToolMcpVO;
import com.flyxy.ai.domain.agent.service.armory.factory.DefaultArmoryStrategyFactory;
import io.modelcontextprotocol.client.McpClient;
import io.modelcontextprotocol.client.McpSyncClient;
import io.modelcontextprotocol.client.transport.HttpClientSseClientTransport;
import io.modelcontextprotocol.client.transport.ServerParameters;
import io.modelcontextprotocol.client.transport.StdioClientTransport;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.List;

/**
 * @Description: 装配mcp
 * @Author: flyxy
 * @Date: 2025/8/29
 */
@Slf4j
@Service
public class AiClientMcpNode extends AbstractArmorySupport {

    @Resource
    private AiClientModelNode aiClientModelNode;

    @Override
    protected String doApply(ArmoryCommandEntity requestParameter, DefaultArmoryStrategyFactory.DynamicContext dynamicContext) throws Exception {
        log.info("AIClient装配链---AiClientMcpNode---开始装配MCP");

        List<AiClientToolMcpVO> mcpVOList = dynamicContext.getValue(dataName());

        for (AiClientToolMcpVO mcpVO : mcpVOList) {
            createMcpSyncClient(mcpVO);
        }

        return router(requestParameter, dynamicContext);
    }

    @Override
    public StrategyHandler<ArmoryCommandEntity, DefaultArmoryStrategyFactory.DynamicContext, String> get(ArmoryCommandEntity requestParameter, DefaultArmoryStrategyFactory.DynamicContext dynamicContext) throws Exception {
        return aiClientModelNode;
    }

    @Override
    protected String beanName(String mcpId) {
        return AiAgentEnumVO.AI_CLIENT_TOOL_MCP.getBeanName(mcpId);
    }

    @Override
    protected String dataName() {
        return AiAgentEnumVO.AI_CLIENT_TOOL_MCP.getDataName();
    }


    public void createMcpSyncClient(AiClientToolMcpVO mcpVO){
        String type = mcpVO.getTransportType();

        switch (type){
            case "sse":
                AiClientToolMcpVO.TransportConfigSse configSse = mcpVO.getTransportConfigSse();
                HttpClientSseClientTransport sseClientTransport = HttpClientSseClientTransport.builder(configSse.getBaseUri())
                        .sseEndpoint(configSse.getSseEndpoint())
                        .build();

                McpSyncClient mcpSyncClient = McpClient.sync(sseClientTransport)
                        .requestTimeout(Duration.ofMinutes(mcpVO.getRequestTimeout()))
                        .build();

                var mcpSseInit = mcpSyncClient.initialize();
                System.out.println("SSE MCP Initialized: " + mcpSseInit);
                // 注册MCPBean
                registerBean(beanName(mcpVO.getMcpId()), McpSyncClient.class, mcpSyncClient);

                log.info("sse方式注册mcp: {} 成功", mcpVO.getMcpName());
                break;

            case "stdio":
                AiClientToolMcpVO.TransportConfigStdio.Stdio configStdio = mcpVO.getTransportConfigStdio()
                        .getStdio()
                        .get(mcpVO.getMcpName());

                var stdioParams = ServerParameters.builder(configStdio.getCommand())
                        .args(configStdio.getArgs())
                        .build();

                var mcpClient = McpClient.sync(new StdioClientTransport(stdioParams))
                        .requestTimeout(Duration.ofMinutes(mcpVO.getRequestTimeout()))
                        .build();

                var mcpStdioInit = mcpClient.initialize();
                registerBean(beanName(mcpVO.getMcpId()), McpSyncClient.class, mcpClient);

                log.info("stdio方式注册mcp: {} 成功", mcpVO.getMcpName());
                break;

            default:
                throw new RuntimeException("不支持的MCP类型: " + type);
        }
    }
}
