package com.flyxy.ai.domain.agent.service.armory;

import cn.bugstack.wrench.design.framework.tree.StrategyHandler;
import com.flyxy.ai.domain.agent.model.entity.ArmoryCommandEntity;
import com.flyxy.ai.domain.agent.model.valobj.enums.AiAgentEnumVO;
import com.flyxy.ai.domain.agent.model.valobj.AiClientModelVO;
import com.flyxy.ai.domain.agent.service.armory.factory.DefaultArmoryStrategyFactory;
import io.modelcontextprotocol.client.McpSyncClient;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.mcp.SyncMcpToolCallbackProvider;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.ai.openai.api.OpenAiApi;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description: 装配模型
 * @Author: flyxy
 * @Date: 2025/8/29
 */
@Slf4j
@Service
public class AiClientModelNode extends AbstractArmorySupport{

    @Resource
    private AiClientAdvisorNode aiClientAdvisorNode;

    @Override
    protected String doApply(ArmoryCommandEntity requestParameter, DefaultArmoryStrategyFactory.DynamicContext dynamicContext) throws Exception {
        log.info("AIClient装配链---AiClientModelNode---开始装配Model");

        List<AiClientModelVO> modelVOS = dynamicContext.getValue(AiAgentEnumVO.AI_CLIENT_MODEL.getDataName());

        ConfigurableListableBeanFactory beanFactory = context.getBeanFactory();

        for (AiClientModelVO modelVO : modelVOS) {
            ArrayList<McpSyncClient> mcpSyncClients = new ArrayList<>();
            // 获取model对应的api对象
            OpenAiApi api = beanFactory.getBean(AiAgentEnumVO.AI_CLIENT_API.getBeanName(modelVO.getApiId()), OpenAiApi.class);
            // 获取model对应的mcp对象
            for (String mcpId : modelVO.getToolMcpIds()) {
                McpSyncClient mcpSyncClient = beanFactory.getBean(AiAgentEnumVO.AI_CLIENT_TOOL_MCP.getBeanName(mcpId), McpSyncClient.class);
                mcpSyncClients.add(mcpSyncClient);
            }
            // 构建model模型
            OpenAiChatModel chatModel = OpenAiChatModel.builder()
                    .openAiApi(api)
                    .defaultOptions(OpenAiChatOptions.builder()
                            .model(modelVO.getModelName())
                            .toolCallbacks(new SyncMcpToolCallbackProvider(mcpSyncClients).getToolCallbacks())
                            .build())
                    .build();

            // 注册模型
            registerBean(beanName(modelVO.getModelId()), OpenAiChatModel.class, chatModel);
            log.info("注册模型-modelId:{}, modelName:{}-成功", modelVO.getModelId(), modelVO.getModelName());
        }

        return router(requestParameter, dynamicContext);
    }

    @Override
    public StrategyHandler<ArmoryCommandEntity, DefaultArmoryStrategyFactory.DynamicContext, String> get(ArmoryCommandEntity requestParameter, DefaultArmoryStrategyFactory.DynamicContext dynamicContext) throws Exception {
        return aiClientAdvisorNode;
    }

    @Override
    protected String beanName(String modelId) {
        return AiAgentEnumVO.AI_CLIENT_MODEL.getBeanName(modelId);
    }

    @Override
    protected String dataName() {
        return AiAgentEnumVO.AI_CLIENT_MODEL.getDataName();
    }
}
