package com.flyxy.ai.domain.agent.service.armory;

import cn.bugstack.wrench.design.framework.tree.StrategyHandler;
import com.flyxy.ai.domain.agent.model.entity.ArmoryCommandEntity;
import com.flyxy.ai.domain.agent.model.valobj.enums.AiAgentEnumVO;
import com.flyxy.ai.domain.agent.model.valobj.AiClientSystemPromptVO;
import com.flyxy.ai.domain.agent.model.valobj.AiClientVO;
import com.flyxy.ai.domain.agent.service.armory.factory.DefaultArmoryStrategyFactory;
import io.modelcontextprotocol.client.McpSyncClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.client.advisor.api.Advisor;
import org.springframework.ai.mcp.SyncMcpToolCallbackProvider;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @Description:
 * @Author: flyxy
 * @Date: 2025/8/30
 */
@Slf4j
@Service
public class AiClientNode extends AbstractArmorySupport{
    @Override
    protected String doApply(ArmoryCommandEntity requestParameter, DefaultArmoryStrategyFactory.DynamicContext dynamicContext) throws Exception {
        log.info("AIClient装配链---AiClientNode---开始装配Client");
        // bean工厂
        ConfigurableListableBeanFactory beanFactory = context.getBeanFactory();
        // 获取clientVO和promptVoMap
        List<AiClientVO> clientVOS = dynamicContext.getValue(AiAgentEnumVO.AI_CLIENT.getDataName());
        Map<String, AiClientSystemPromptVO> promptVOMap = dynamicContext.getValue(AiAgentEnumVO.AI_CLIENT_SYSTEM_PROMPT.getDataName());

        for (AiClientVO clientVO : clientVOS) {
            // 1. 提示词
            StringBuilder defaultSystem = new StringBuilder("Ai 智能体 \r\n");
            List<String> promptIdList = clientVO.getPromptIdList();
            for (String promptId : promptIdList) {
                AiClientSystemPromptVO aiClientSystemPromptVO = promptVOMap.get(promptId);
                defaultSystem.append(aiClientSystemPromptVO.getPromptContent());
            }
            // 2. MCP
            List<String> mcpIdList = clientVO.getMcpIdList();
            List<McpSyncClient> mcpSyncClientList = new ArrayList<>();
            for (String mcpId : mcpIdList) {
                McpSyncClient mcpSyncClient = getBean(AiAgentEnumVO.AI_CLIENT_TOOL_MCP.getBeanName(mcpId), McpSyncClient.class);
                mcpSyncClientList.add(mcpSyncClient);
            }
            // 3. Model
            OpenAiChatModel model = beanFactory.getBean(AiAgentEnumVO.AI_CLIENT_MODEL.getBeanName(clientVO.getModelId()), OpenAiChatModel.class);

            // 4. Advisor
            List<String> advisorIdList = clientVO.getAdvisorIdList();
            List<Advisor> AdvisorList = new ArrayList<>();
            for (String advisorId : advisorIdList) {
                Advisor advisor = getBean(AiAgentEnumVO.AI_CLIENT_ADVISOR.getBeanName(advisorId), Advisor.class);
                AdvisorList.add(advisor);
            }

            // 5. 构建ChatClient
            ChatClient chatClient = ChatClient.builder(model)
                    .defaultSystem(defaultSystem.toString())
                    .defaultToolCallbacks(new SyncMcpToolCallbackProvider(mcpSyncClientList).getToolCallbacks())
                    .defaultAdvisors(AdvisorList)
                    .build();

            registerBean(AiAgentEnumVO.AI_CLIENT.getBeanName(clientVO.getClientId()), ChatClient.class, chatClient);
            log.info("注册Client---clientId:{}, clientName:{}---成功", clientVO.getClientId(), clientVO.getClientName());
        }

        return router(requestParameter, dynamicContext);
    }

    @Override
    public StrategyHandler<ArmoryCommandEntity, DefaultArmoryStrategyFactory.DynamicContext, String> get(ArmoryCommandEntity requestParameter, DefaultArmoryStrategyFactory.DynamicContext dynamicContext) throws Exception {
        return defaultStrategyHandler;
    }

    @Override
    protected String beanName(String clientId) {
        return AiAgentEnumVO.AI_CLIENT.getBeanName(clientId);
    }

    @Override
    protected String dataName() {
        return AiAgentEnumVO.AI_CLIENT.getDataName();
    }
}
