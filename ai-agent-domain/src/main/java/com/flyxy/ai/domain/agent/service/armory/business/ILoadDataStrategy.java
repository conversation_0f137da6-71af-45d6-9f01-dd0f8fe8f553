package com.flyxy.ai.domain.agent.service.armory.business;

import com.flyxy.ai.domain.agent.model.entity.ArmoryCommandEntity;
import com.flyxy.ai.domain.agent.service.armory.factory.DefaultArmoryStrategyFactory;

/**
 * @Description:
 * @Author: flyxy
 * @Date: 2025/8/28
 */
public interface ILoadDataStrategy{

    void loadData(ArmoryCommandEntity armoryCommandEntity, DefaultArmoryStrategyFactory.DynamicContext dynamicContext);
}
