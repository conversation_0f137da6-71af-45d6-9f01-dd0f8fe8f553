package com.flyxy.ai.domain.agent.service.armory.factory;

import cn.bugstack.wrench.design.framework.tree.StrategyHandler;
import com.flyxy.ai.domain.agent.model.entity.ArmoryCommandEntity;
import com.flyxy.ai.domain.agent.service.armory.RootNode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * @Description:
 * @Author: flyxy
 * @Date: 2025/8/27
 */
@Service
public class DefaultArmoryStrategyFactory {

    private final RootNode armoryRootNode;

    public DefaultArmoryStrategyFactory(RootNode armoryRootNode) {
        this.armoryRootNode = armoryRootNode;
    }

    public StrategyHandler<ArmoryCommandEntity, DefaultArmoryStrategyFactory.DynamicContext, String> armoryStrategyHandler(){
        return armoryRootNode;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class DynamicContext{

        private Map<String, Object> dataObjects = new HashMap<>();


        public <T> void setValue(String key, T value){
            dataObjects.put(key, value);
        }

        public <T> T getValue(String key){
            return (T) dataObjects.get(key);
        }

    }

}
