package com.flyxy.ai.domain.agent.service.exec;

import com.flyxy.ai.domain.agent.model.entity.ExecuteCommandEntity;
import reactor.core.publisher.Flux;

/**
 * @Description:
 * @Author: flyxy
 * @Date: 2025/9/1
 */
public interface IExecuteStrategy {

    void execute(ExecuteCommandEntity requestParameter) throws Exception;

    void execute(ExecuteCommandEntity requestParameter, Flux<String> flux) throws Exception;

}
