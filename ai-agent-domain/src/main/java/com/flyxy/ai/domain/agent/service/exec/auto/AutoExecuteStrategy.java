package com.flyxy.ai.domain.agent.service.exec.auto;

import com.flyxy.ai.domain.agent.model.entity.ExecuteCommandEntity;
import com.flyxy.ai.domain.agent.service.exec.IExecuteStrategy;
import com.flyxy.ai.domain.agent.service.exec.auto.step.factory.DefaultAutoAgentExecuteStrategyFactory;
import jakarta.annotation.Resource;
import reactor.core.publisher.Flux;

/**
 * @Description:
 * @Author: flyxy
 * @Date: 2025/9/1
 */
public class AutoExecuteStrategy implements IExecuteStrategy {

    @Resource
    private DefaultAutoAgentExecuteStrategyFactory defaultAutoAgentExecuteStrategyFactory;

    @Override
    public void execute(ExecuteCommandEntity requestParameter) throws Exception {

    }

    @Override
    public void execute(ExecuteCommandEntity requestParameter, Flux<String> flux) throws Exception {
        DefaultAutoAgentExecuteStrategyFactory.DynamicContext dynamicContext = new DefaultAutoAgentExecuteStrategyFactory.DynamicContext();
        dynamicContext.setFlux(flux);
        String result = defaultAutoAgentExecuteStrategyFactory.armoryStrategyHandler()
                .apply(requestParameter, dynamicContext);
    }
}
