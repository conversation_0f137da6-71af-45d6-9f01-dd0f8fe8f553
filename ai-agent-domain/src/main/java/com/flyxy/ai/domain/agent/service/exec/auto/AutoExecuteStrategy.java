package com.flyxy.ai.domain.agent.service.exec.auto;

import com.flyxy.ai.domain.agent.model.entity.ExecuteCommandEntity;
import com.flyxy.ai.domain.agent.service.exec.IExecuteStrategy;
import com.flyxy.ai.domain.agent.service.exec.auto.step.StreamContextManager;
import com.flyxy.ai.domain.agent.service.exec.auto.step.factory.DefaultAutoAgentExecuteStrategyFactory;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.FluxSink;

/**
 * @Description:
 * @Author: flyxy
 * @Date: 2025/9/1
 */
@Slf4j
public class AutoExecuteStrategy implements IExecuteStrategy {

    @Resource
    private DefaultAutoAgentExecuteStrategyFactory defaultAutoAgentExecuteStrategyFactory;

    @Override
    public void execute(ExecuteCommandEntity requestParameter) throws Exception {

    }

    @Override
    public void execute(ExecuteCommandEntity requestParameter, Flux<String> flux) throws Exception {
        DefaultAutoAgentExecuteStrategyFactory.DynamicContext dynamicContext = new DefaultAutoAgentExecuteStrategyFactory.DynamicContext();
        dynamicContext.setFlux(flux);
        String result = defaultAutoAgentExecuteStrategyFactory.armoryStrategyHandler()
                .apply(requestParameter, dynamicContext);
    }

    @Override
    public void execute(ExecuteCommandEntity requestParameter, FluxSink<String> sink) throws Exception {
        log.info("开始执行AutoAgent流式任务，会话ID: {}", requestParameter.getSessionId());

        try {
            // 创建流式上下文管理器
            StreamContextManager streamManager = new StreamContextManager(sink);

            // 创建动态上下文
            DefaultAutoAgentExecuteStrategyFactory.DynamicContext dynamicContext =
                new DefaultAutoAgentExecuteStrategyFactory.DynamicContext();
            dynamicContext.setStreamManager(streamManager);

            // 添加开始标识
            streamManager.appendContent("🚀 === AutoAgent执行开始 ===\n", "system");

            // 执行规则树
            String result = defaultAutoAgentExecuteStrategyFactory.armoryStrategyHandler()
                    .apply(requestParameter, dynamicContext);

            // 添加完成标识
            streamManager.appendContent("\n🏁 === AutoAgent执行完成 ===\n", "system");

            // 完成流
            streamManager.complete();

            log.info("AutoAgent流式任务执行完成，会话ID: {}", requestParameter.getSessionId());

        } catch (Exception e) {
            log.error("AutoAgent流式任务执行异常，会话ID: {}", requestParameter.getSessionId(), e);
            sink.error(e);
        }
    }
}
