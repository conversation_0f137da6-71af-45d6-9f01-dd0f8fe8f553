package com.flyxy.ai.domain.agent.service.exec.auto.step;

import cn.bugstack.wrench.design.framework.tree.AbstractMultiThreadStrategyRouter;
import cn.bugstack.wrench.design.framework.tree.StrategyHandler;
import com.flyxy.ai.domain.agent.adapter.repository.IAgentRepository;
import com.flyxy.ai.domain.agent.model.entity.ExecuteCommandEntity;
import com.flyxy.ai.domain.agent.service.exec.auto.step.factory.DefaultAutoAgentExecuteStrategyFactory;
import jakarta.annotation.Resource;
import org.springframework.context.ConfigurableApplicationContext;

import java.util.concurrent.ExecutionException;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeoutException;

/**
 * @Description:
 * @Author: flyxy
 * @Date: 2025/9/1
 */
public abstract class AbstractExecuteSupport extends AbstractMultiThreadStrategyRouter<ExecuteCommandEntity, DefaultAutoAgentExecuteStrategyFactory.DynamicContext, String> {

    @Resource
    protected ConfigurableApplicationContext context;

    @Resource
    protected IAgentRepository repository;

    @Resource
    protected ThreadPoolExecutor threadPoolExecutor;

    @Override
    protected void multiThread(ExecuteCommandEntity requestParameter, DefaultAutoAgentExecuteStrategyFactory.DynamicContext dynamicContext) throws ExecutionException, InterruptedException, TimeoutException {
        //
    }

    protected <T> T getBean(String beanName, Class<T> clazz){
        return context.getBean(beanName, clazz);
    }


}
