package com.flyxy.ai.domain.agent.service.exec.auto.step;

import cn.bugstack.wrench.design.framework.tree.StrategyHandler;
import com.flyxy.ai.domain.agent.model.entity.ExecuteCommandEntity;
import com.flyxy.ai.domain.agent.model.valobj.AiAgentClientFlowConfigVO;
import com.flyxy.ai.domain.agent.service.exec.auto.step.factory.DefaultAutoAgentExecuteStrategyFactory;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeoutException;

/**
 * @Description:
 * @Author: flyxy
 * @Date: 2025/9/1
 */
@Slf4j
@Service("executeRootNode")
public class RootNode extends AbstractExecuteSupport{


    @Resource
    private Step1TaskAnalyzerClient step1TaskAnalyzerClient;

    @Override
    protected void multiThread(ExecuteCommandEntity requestParameter, DefaultAutoAgentExecuteStrategyFactory.DynamicContext dynamicContext) throws ExecutionException, InterruptedException, TimeoutException {
        // 获取任务执行流程
        Map<String, AiAgentClientFlowConfigVO> stringAiAgentClientFlowConfigVOMap =
                repository.queryAiAgentClientFlowConfig(requestParameter.getAiAgentId());
        dynamicContext.setAiAgentClientFlowConfigVOMap(stringAiAgentClientFlowConfigVOMap);
    }
    @Override
    protected String doApply(ExecuteCommandEntity requestParameter, DefaultAutoAgentExecuteStrategyFactory.DynamicContext dynamicContext) throws Exception {
        log.info("=== 动态多轮执行测试开始 ====");
        log.info("用户输入: {}", requestParameter.getMessage());
        log.info("最大执行步数: {}", requestParameter.getMaxStep());
        log.info("会话ID: {}", requestParameter.getSessionId());

        // 获取流式管理器并输出开始信息
        StreamContextManager streamManager = dynamicContext.getStreamManager();
        if (streamManager != null) {
            String startInfo = String.format("""
                    📋 === 动态多轮执行开始 ===
                    👤 用户输入: %s
                    🔢 最大执行步数: %d
                    🆔 会话ID: %s

                    """, requestParameter.getMessage(), requestParameter.getMaxStep(), requestParameter.getSessionId());
            streamManager.appendContent(startInfo, "RootNode-开始");
        }

        // 上下文信息
        dynamicContext.setExecutionHistory(new StringBuilder());
        // 当前任务信息
        dynamicContext.setCurrentTask(requestParameter.getMessage());
        // 最大任务步骤
        dynamicContext.setMaxStep(requestParameter.getMaxStep());

        return router(requestParameter, dynamicContext);
    }

    @Override
    public StrategyHandler<ExecuteCommandEntity, DefaultAutoAgentExecuteStrategyFactory.DynamicContext, String> get(ExecuteCommandEntity requestParameter, DefaultAutoAgentExecuteStrategyFactory.DynamicContext dynamicContext) throws Exception {
        return step1TaskAnalyzerClient;
    }

}
