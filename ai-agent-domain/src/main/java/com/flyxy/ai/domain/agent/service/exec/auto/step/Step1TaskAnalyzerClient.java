package com.flyxy.ai.domain.agent.service.exec.auto.step;

import cn.bugstack.wrench.design.framework.tree.StrategyHandler;
import com.flyxy.ai.domain.agent.model.entity.ExecuteCommandEntity;
import com.flyxy.ai.domain.agent.model.valobj.AiAgentClientFlowConfigVO;
import com.flyxy.ai.domain.agent.model.valobj.enums.AiAgentEnumVO;
import com.flyxy.ai.domain.agent.model.valobj.enums.AiClientTypeEnumVO;
import com.flyxy.ai.domain.agent.service.exec.auto.step.factory.DefaultAutoAgentExecuteStrategyFactory;
import com.flyxy.ai.domain.agent.utils.FluxLineSplitter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

import java.time.LocalDate;

import static com.flyxy.ai.domain.agent.service.armory.AbstractArmorySupport.CHAT_MEMORY_CONVERSATION_ID_KEY;
import static com.flyxy.ai.domain.agent.service.armory.AbstractArmorySupport.CHAT_MEMORY_RETRIEVE_SIZE_KEY;

/**
 * @Description: 步骤1：任务分析
 * @Author: flyxy
 * @Date: 2025/9/1
 */
@Slf4j
@Service
public class Step1TaskAnalyzerClient extends AbstractExecuteSupport {
    @Override
    protected String doApply(ExecuteCommandEntity requestParameter, DefaultAutoAgentExecuteStrategyFactory.DynamicContext dynamicContext) throws Exception {
        log.info("=== 动态多轮执行第{}步 Step1 ====", dynamicContext.getStep());

        // 获取流式管理器
        StreamContextManager streamManager = dynamicContext.getStreamManager();
        if (streamManager != null) {
            streamManager.appendStepStart("Step1-任务分析", dynamicContext.getStep());
        }

        // 第一阶段：任务分析
        log.info("\n📊 阶段1: 任务状态分析");
        // 组装prompt
        String analysisPrompt = String.format("""
                **原始用户需求:** %s

                **当前执行步骤:** 第 %d 步 (最大 %d 步)

                **历史执行记录:**
                %s

                **当前任务:** %s

                请分析当前任务状态，评估执行进度，并制定下一步策略。
                """, requestParameter.getMessage(), dynamicContext.getStep(), dynamicContext.getMaxStep(), dynamicContext.getExecutionHistory()
                .isEmpty() ? "[首次执行]" : dynamicContext.getExecutionHistory()
                .toString(), dynamicContext.getCurrentTask());

        // 获取Client实例
        AiAgentClientFlowConfigVO aiAgentClientFlowConfigVO = dynamicContext.getAiAgentClientFlowConfigVOMap()
                .get(AiClientTypeEnumVO.TASK_ANALYZER_CLIENT.getCode());
        String clientId = aiAgentClientFlowConfigVO.getClientId();
        ChatClient chatClient = getBean(AiAgentEnumVO.AI_CLIENT.getBeanName(clientId), ChatClient.class);

        StringBuilder sb = new StringBuilder();

        // 使用同步调用避免StreamAdvisor问题，然后模拟流式输出
        String analysisResult = chatClient.prompt(analysisPrompt)
                .system(s -> s.param("current_date", LocalDate.now().toString()))
                .advisors(a -> a.param(CHAT_MEMORY_CONVERSATION_ID_KEY, "workflow-execution-001")
                        .param(CHAT_MEMORY_RETRIEVE_SIZE_KEY, 100))
                .call()
                .content();

        assert analysisResult != null;

        // 解析结果并添加到流
        String parsedResult = parseAnalysisResult(dynamicContext, analysisResult);
        sb.append(parsedResult);

        // 将结果添加到流式管理器
        if (streamManager != null) {
            streamManager.appendContent(parsedResult, "Step1-分析结果");
        }

        // 将执行结果存入上下文中
        dynamicContext.setValue("analysisResult", sb.toString());

        if (streamManager != null) {
            streamManager.appendStepComplete("Step1-任务分析", dynamicContext.getStep());
        }

        return router(requestParameter, dynamicContext);
    }

    @Override
    public StrategyHandler<ExecuteCommandEntity, DefaultAutoAgentExecuteStrategyFactory.DynamicContext, String> get(ExecuteCommandEntity requestParameter, DefaultAutoAgentExecuteStrategyFactory.DynamicContext dynamicContext) throws Exception {
        if (dynamicContext.isCompleted() || dynamicContext.getStep() > dynamicContext.getMaxStep()) {
            // 任务完成或超出最大步数，进入总结阶段
            return getBean("step4QualitySupervisorClient", StrategyHandler.class);
        }
        // 任务未完成，按照流程继续
        return getBean("step2PrecisionExecutorClient", StrategyHandler.class);
    }

    private String parseAnalysisResult(DefaultAutoAgentExecuteStrategyFactory.DynamicContext dynamicContext, String analysisResult) {
        StringBuilder processedResult = new StringBuilder();
        processedResult.append("\n📊 === 第 ")
                .append(dynamicContext.getStep())
                .append(" 步分析结果 ===\n");

        String[] lines = analysisResult.split("\n");
        String currentSection = "";

        for (String line : lines) {

            line = line.trim();
            if (line.isEmpty()) continue;

            if (analysisResult.contains("任务状态: COMPLETED") || analysisResult.contains("完成度评估: 100%")) {
                dynamicContext.setCompleted(true);
                log.info("✅ 任务分析显示已完成！");
            }
            if (line.contains("任务状态分析:")) {
                currentSection = "status";
                processedResult.append("\n🎯 任务状态分析:\n");
                continue;
            } else if (line.contains("执行历史评估:")) {
                currentSection = "history";
                processedResult.append("\n📈 执行历史评估:\n");
                continue;
            } else if (line.contains("下一步策略:")) {
                currentSection = "strategy";
                processedResult.append("\n🚀 下一步策略:\n");
                continue;
            } else if (line.contains("完成度评估:")) {
                currentSection = "progress";
                String progress = line.substring(line.indexOf(":") + 1)
                        .trim();
                processedResult.append("\n📊 完成度评估: ")
                        .append(progress)
                        .append("\n");
                continue;
            } else if (line.contains("任务状态:")) {
                currentSection = "task_status";
                String status = line.substring(line.indexOf(":") + 1)
                        .trim();
                if (status.equals("COMPLETED")) {
                    processedResult.append("\n✅ 任务状态: 已完成\n");
                } else {
                    processedResult.append("\n🔄 任务状态: 继续执行\n");
                }
                continue;
            }

            switch (currentSection) {
                case "status":
                    processedResult.append("   📋 ")
                            .append(line)
                            .append("\n");
                    break;
                case "history":
                    processedResult.append("   📊 ")
                            .append(line)
                            .append("\n");
                    break;
                case "strategy":
                    processedResult.append("   🎯 ")
                            .append(line)
                            .append("\n");
                    break;
                default:
                    processedResult.append("   📝 ")
                            .append(line)
                            .append("\n");
                    break;
            }
        }

        return processedResult.toString();
    }
}
