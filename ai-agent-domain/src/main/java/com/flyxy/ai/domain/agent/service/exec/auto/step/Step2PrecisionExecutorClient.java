package com.flyxy.ai.domain.agent.service.exec.auto.step;

import cn.bugstack.wrench.design.framework.tree.StrategyHandler;
import com.flyxy.ai.domain.agent.model.entity.ExecuteCommandEntity;
import com.flyxy.ai.domain.agent.model.valobj.AiAgentClientFlowConfigVO;
import com.flyxy.ai.domain.agent.model.valobj.enums.AiAgentEnumVO;
import com.flyxy.ai.domain.agent.model.valobj.enums.AiClientTypeEnumVO;
import com.flyxy.ai.domain.agent.service.exec.auto.step.factory.DefaultAutoAgentExecuteStrategyFactory;
import com.flyxy.ai.domain.agent.utils.FluxLineSplitter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

import java.time.LocalDate;

import static com.flyxy.ai.domain.agent.service.armory.AbstractArmorySupport.CHAT_MEMORY_CONVERSATION_ID_KEY;
import static com.flyxy.ai.domain.agent.service.armory.AbstractArmorySupport.CHAT_MEMORY_RETRIEVE_SIZE_KEY;

/**
 * @Description:
 * @Author: flyxy
 * @Date: 2025/9/1
 */
@Slf4j
@Service
public class Step2PrecisionExecutorClient extends AbstractExecuteSupport {
    @Override
    protected String doApply(ExecuteCommandEntity requestParameter, DefaultAutoAgentExecuteStrategyFactory.DynamicContext dynamicContext) throws Exception {
        log.info("=== 动态多轮执行第{}步 Step2 ====", dynamicContext.getStep());

        // 获取流式管理器
        StreamContextManager streamManager = dynamicContext.getStreamManager();
        if (streamManager != null) {
            streamManager.appendStepStart("Step2-精准执行", dynamicContext.getStep());
        }

        log.info("\n📊 阶段2: 精准任务执行");

        AiAgentClientFlowConfigVO aiAgentClientFlowConfigVO = dynamicContext.getAiAgentClientFlowConfigVOMap()
                .get(AiClientTypeEnumVO.PRECISION_EXECUTOR_CLIENT.getCode());
        String clientId = aiAgentClientFlowConfigVO.getClientId();
        ChatClient chatClient = getBean(AiAgentEnumVO.AI_CLIENT.getBeanName(clientId), ChatClient.class);

        // 从动态上下文中获取分析结果
        String analysisResult = dynamicContext.getValue("analysisResult");
        if (analysisResult == null || analysisResult.trim().isEmpty()) {
            log.warn("⚠️ 分析结果为空，使用默认执行策略");
            analysisResult = "执行当前任务步骤";
        }

        String executionPrompt = String.format("""
                **分析师策略:** %s

                **执行指令:** 根据上述分析师的策略，执行具体的任务步骤。

                **执行要求:**
                1. 严格按照策略执行
                2. 使用必要的工具
                3. 确保执行质量
                4. 详细记录过程

                **输出格式:**
                执行目标: [明确的执行目标]
                执行过程: [详细的执行步骤]
                执行结果: [具体的执行成果]
                质量检查: [自我质量评估]
                """, analysisResult);

        StringBuilder sb = new StringBuilder();

        // 执行流式任务
        Flux<String> executionFlux = FluxLineSplitter.splitByNewline(chatClient
                .prompt(executionPrompt)
                .system(s -> s.param("current_date", LocalDate.now()
                        .toString()))
                /*.advisors(a -> a
                        .param(CHAT_MEMORY_CONVERSATION_ID_KEY, "workflow-execution-001")
                        .param(CHAT_MEMORY_RETRIEVE_SIZE_KEY, 100))*/
                .stream()
                .content())
                .map(contentLine -> {
                    String parsedExecutionResult = parseExecutionResultStream(dynamicContext.getStep(), contentLine);
                    sb.append(parsedExecutionResult);
                    return parsedExecutionResult;
                });

        // 将流添加到流式管理器
        if (streamManager != null) {
            streamManager.appendStream(executionFlux, "Step2-执行流");
        }

        // 等待流完成并获取结果
        String executionResult = executionFlux.collectList().block().stream()
                .reduce("", (a, b) -> a + b);

        // 将执行结果保存到动态上下文中，供下一步使用
        dynamicContext.setValue("executionResult", sb.toString());

        // 更新执行历史
        String stepSummary = String.format("""
                === 第 %d 步执行记录 ===
                【分析阶段】%s
                【执行阶段】%s
                """, dynamicContext.getStep(), analysisResult, sb.toString());

        dynamicContext.getExecutionHistory().append(stepSummary);

        if (streamManager != null) {
            streamManager.appendStepComplete("Step2-精准执行", dynamicContext.getStep());
        }

        return router(requestParameter, dynamicContext);

    }

    @Override
    public StrategyHandler<ExecuteCommandEntity, DefaultAutoAgentExecuteStrategyFactory.DynamicContext, String> get(ExecuteCommandEntity requestParameter, DefaultAutoAgentExecuteStrategyFactory.DynamicContext dynamicContext) throws Exception {
        return getBean("step3PrecisionExecutorClient", StrategyHandler.class);
    }

    /**
     * 解析执行结果流式输出
     */
    private String parseExecutionResultStream(int step, String contentLine) {
        StringBuilder processedResult = new StringBuilder();
        processedResult.append("\n⚡ === 第 ")
                .append(step)
                .append(" 步执行结果 ===\n");

        String[] lines = contentLine.split("\n");
        String currentSection = "";

        for (String line : lines) {
            line = line.trim();
            if (line.isEmpty()) continue;

            if (line.contains("执行目标:")) {
                currentSection = "target";
                processedResult.append("\n🎯 执行目标:\n");
                continue;
            } else if (line.contains("执行过程:")) {
                currentSection = "process";
                processedResult.append("\n🔧 执行过程:\n");
                continue;
            } else if (line.contains("执行结果:")) {
                currentSection = "result";
                processedResult.append("\n📈 执行结果:\n");
                continue;
            } else if (line.contains("质量检查:")) {
                currentSection = "quality";
                processedResult.append("\n🔍 质量检查:\n");
                continue;
            }

            switch (currentSection) {
                case "target":
                    processedResult.append("   🎯 ")
                            .append(line)
                            .append("\n");
                    break;
                case "process":
                    processedResult.append("   ⚙️ ")
                            .append(line)
                            .append("\n");
                    break;
                case "result":
                    processedResult.append("   📊 ")
                            .append(line)
                            .append("\n");
                    break;
                case "quality":
                    processedResult.append("   ✅ ")
                            .append(line)
                            .append("\n");
                    break;
                default:
                    processedResult.append("   📝 ")
                            .append(line)
                            .append("\n");
                    break;
            }
        }

        return processedResult.toString();
    }

    /**
     * 解析执行结果（保留原有方法用于兼容）
     */
    private void parseExecutionResult(int step, String executionResult) {
        log.info("\n⚡ === 第 {} 步执行结果 ===", step);

        String[] lines = executionResult.split("\n");
        String currentSection = "";

        for (String line : lines) {
            line = line.trim();
            if (line.isEmpty()) continue;

            if (line.contains("执行目标:")) {
                currentSection = "target";
                log.info("\n🎯 执行目标:");
                continue;
            } else if (line.contains("执行过程:")) {
                currentSection = "process";
                log.info("\n🔧 执行过程:");
                continue;
            } else if (line.contains("执行结果:")) {
                currentSection = "result";
                log.info("\n📈 执行结果:");
                continue;
            } else if (line.contains("质量检查:")) {
                currentSection = "quality";
                log.info("\n🔍 质量检查:");
                continue;
            }

            switch (currentSection) {
                case "target":
                    log.info("   🎯 {}", line);
                    break;
                case "process":
                    log.info("   ⚙️ {}", line);
                    break;
                case "result":
                    log.info("   📊 {}", line);
                    break;
                case "quality":
                    log.info("   ✅ {}", line);
                    break;
                default:
                    log.info("   📝 {}", line);
                    break;
            }
        }
    }
}
