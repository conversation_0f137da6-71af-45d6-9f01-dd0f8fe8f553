package com.flyxy.ai.domain.agent.service.exec.auto.step;

import cn.bugstack.wrench.design.framework.tree.StrategyHandler;
import com.flyxy.ai.domain.agent.model.entity.ExecuteCommandEntity;
import com.flyxy.ai.domain.agent.model.valobj.AiAgentClientFlowConfigVO;
import com.flyxy.ai.domain.agent.model.valobj.enums.AiAgentEnumVO;
import com.flyxy.ai.domain.agent.model.valobj.enums.AiClientTypeEnumVO;
import com.flyxy.ai.domain.agent.service.exec.auto.step.factory.DefaultAutoAgentExecuteStrategyFactory;
import com.flyxy.ai.domain.agent.utils.FluxLineSplitter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

import java.time.LocalDate;

import static com.flyxy.ai.domain.agent.service.armory.AbstractArmorySupport.CHAT_MEMORY_CONVERSATION_ID_KEY;
import static com.flyxy.ai.domain.agent.service.armory.AbstractArmorySupport.CHAT_MEMORY_RETRIEVE_SIZE_KEY;

/**
 * @Description:
 * @Author: flyxy
 * @Date: 2025/9/1
 */
@Slf4j
@Service
public class Step3PrecisionExecutorClient extends AbstractExecuteSupport{
    @Override
    protected String doApply(ExecuteCommandEntity requestParameter, DefaultAutoAgentExecuteStrategyFactory.DynamicContext dynamicContext) throws Exception {
        log.info("=== 动态多轮执行第{}步 Step3 ====", dynamicContext.getStep());

        // 获取流式管理器
        StreamContextManager streamManager = dynamicContext.getStreamManager();
        if (streamManager != null) {
            streamManager.appendStepStart("Step3-质量监督", dynamicContext.getStep());
        }

        log.info("\n🔍 阶段3: 质量监督检查");

        // 从上下文获取任务执行数据
        String executionResult = dynamicContext.getValue("executionResult");
        if (executionResult == null || executionResult.isEmpty()){
            log.warn("⚠️ 执行结果为空，跳过质量监督");
            if (streamManager != null) {
                streamManager.appendContent("⚠️ 执行结果为空，跳过质量监督\n", "Step3-警告");
                streamManager.appendStepComplete("Step3-质量监督", dynamicContext.getStep());
            }
            return "质量监督跳过";
        }

        // 组装prompt
        String supervisionPrompt = String.format("""
                **用户原始需求:** %s

                **执行结果:** %s

                **监督要求:** 请评估执行结果的质量，识别问题，并提供改进建议。

                **输出格式:**
                质量评估: [对执行结果的整体评估]
                问题识别: [发现的问题和不足]
                改进建议: [具体的改进建议]
                质量评分: [1-10分的质量评分]
                是否通过: [PASS/FAIL/OPTIMIZE]
                """, requestParameter.getMessage(), executionResult);

        // 获取对话客户端
        AiAgentClientFlowConfigVO aiAgentClientFlowConfigVO = dynamicContext.getAiAgentClientFlowConfigVOMap()
                .get(AiClientTypeEnumVO.QUALITY_SUPERVISOR_CLIENT.getCode());
        String clientId = aiAgentClientFlowConfigVO.getClientId();
        ChatClient chatClient = getBean(AiAgentEnumVO.AI_CLIENT.getBeanName(clientId), ChatClient.class);

        StringBuilder sb = new StringBuilder();

        // 使用同步调用避免StreamAdvisor问题，然后模拟流式输出
        String supervisionResult = chatClient
                .prompt(supervisionPrompt)
                .system(s -> s.param("current_date", LocalDate.now().toString()))
                .advisors(a -> a
                        .param(CHAT_MEMORY_CONVERSATION_ID_KEY, "workflow-summary-001")
                        .param(CHAT_MEMORY_RETRIEVE_SIZE_KEY, 50))
                .call()
                .content();

        // 模拟流式输出：将结果按行分割并逐步推送
        if (streamManager != null && supervisionResult != null) {
            String[] lines = supervisionResult.split("\n");
            for (String line : lines) {
                if (!line.trim().isEmpty()) {
                    String parsedLine = parseSupervisionResultStream(dynamicContext.getStep(), line);
                    sb.append(parsedLine);
                    streamManager.appendContent(parsedLine, "Step3-监督流");

                    // 添加小延迟模拟真实流式效果
                    try {
                        Thread.sleep(50);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
        } else {
            // 如果没有流管理器，直接处理整个结果
            String parsedResult = parseSupervisionResultStream(dynamicContext.getStep(), supervisionResult);
            sb.append(parsedResult);
        }

        // 将监督结果保存到动态上下文中
        dynamicContext.setValue("supervisionResult", sb.toString());

        // 根据监督结果决定是否需要重新执行
        if (sb.toString().contains("是否通过: FAIL")) {
            log.info("❌ 质量检查未通过，需要重新执行");
            dynamicContext.setCurrentTask("根据质量监督的建议重新执行任务");
            if (streamManager != null) {
                streamManager.appendContent("❌ 质量检查未通过，需要重新执行\n", "Step3-结果");
            }
        } else if (sb.toString().contains("是否通过: OPTIMIZE")) {
            log.info("🔧 质量检查建议优化，继续改进");
            dynamicContext.setCurrentTask("根据质量监督的建议优化执行结果");
            if (streamManager != null) {
                streamManager.appendContent("🔧 质量检查建议优化，继续改进\n", "Step3-结果");
            }
        } else {
            log.info("✅ 质量检查通过");
            dynamicContext.setCompleted(true);
            if (streamManager != null) {
                streamManager.appendContent("✅ 质量检查通过\n", "Step3-结果");
            }
        }

        // 更新执行历史
        String stepSummary = String.format("""
                === 第 %d 步完整记录 ===
                【分析阶段】%s
                【执行阶段】%s
                【监督阶段】%s
                """, dynamicContext.getStep(),
                dynamicContext.getValue("analysisResult"),
                executionResult,
                sb.toString());

        dynamicContext.getExecutionHistory().append(stepSummary);

        // 增加步骤计数
        dynamicContext.setStep(dynamicContext.getStep() + 1);

        if (streamManager != null) {
            streamManager.appendStepComplete("Step3-质量监督", dynamicContext.getStep() - 1);
        }

        return router(requestParameter, dynamicContext);
    }

    @Override
    public StrategyHandler<ExecuteCommandEntity, DefaultAutoAgentExecuteStrategyFactory.DynamicContext, String> get(ExecuteCommandEntity requestParameter, DefaultAutoAgentExecuteStrategyFactory.DynamicContext dynamicContext) throws Exception {
        // 如果任务完成或超出步数，进入总结阶段
        if (dynamicContext.isCompleted() || dynamicContext.getStep() > dynamicContext.getMaxStep()){
            return getBean("step4QualitySupervisorClient", StrategyHandler.class);
        }
        // 如果任务未完成，重新进入step1
        return getBean("step1TaskAnalyzerClient", StrategyHandler.class);
    }


    /**
     * 解析监督结果流式输出
     */
    private String parseSupervisionResultStream(int step, String contentLine) {
        StringBuilder processedResult = new StringBuilder();
        processedResult.append("\n🔍 === 第 ")
                .append(step)
                .append(" 步监督结果 ===\n");

        String[] lines = contentLine.split("\n");
        String currentSection = "";

        for (String line : lines) {
            line = line.trim();
            if (line.isEmpty()) continue;

            if (line.contains("质量评估:")) {
                currentSection = "assessment";
                processedResult.append("\n📊 质量评估:\n");
                continue;
            } else if (line.contains("问题识别:")) {
                currentSection = "issues";
                processedResult.append("\n⚠️ 问题识别:\n");
                continue;
            } else if (line.contains("改进建议:")) {
                currentSection = "suggestions";
                processedResult.append("\n💡 改进建议:\n");
                continue;
            } else if (line.contains("质量评分:")) {
                currentSection = "score";
                String score = line.substring(line.indexOf(":") + 1).trim();
                processedResult.append("\n📊 质量评分: ")
                        .append(score)
                        .append("\n");
                continue;
            } else if (line.contains("是否通过:")) {
                currentSection = "pass";
                String status = line.substring(line.indexOf(":") + 1).trim();
                if (status.equals("PASS")) {
                    processedResult.append("\n✅ 检查结果: 通过\n");
                } else if (status.equals("FAIL")) {
                    processedResult.append("\n❌ 检查结果: 未通过\n");
                } else {
                    processedResult.append("\n🔧 检查结果: 需要优化\n");
                }
                continue;
            }

            switch (currentSection) {
                case "assessment":
                    processedResult.append("   📋 ")
                            .append(line)
                            .append("\n");
                    break;
                case "issues":
                    processedResult.append("   ⚠️ ")
                            .append(line)
                            .append("\n");
                    break;
                case "suggestions":
                    processedResult.append("   💡 ")
                            .append(line)
                            .append("\n");
                    break;
                default:
                    processedResult.append("   📝 ")
                            .append(line)
                            .append("\n");
                    break;
            }
        }

        return processedResult.toString();
    }

    /**
     * 解析监督结果（保留原有方法用于兼容）
     */
    private void parseSupervisionResult(int step, String supervisionResult) {
        log.info("\n🔍 === 第 {} 步监督结果 ===", step);

        String[] lines = supervisionResult.split("\n");
        String currentSection = "";

        for (String line : lines) {
            line = line.trim();
            if (line.isEmpty()) continue;

            if (line.contains("质量评估:")) {
                currentSection = "assessment";
                log.info("\n📊 质量评估:");
                continue;
            } else if (line.contains("问题识别:")) {
                currentSection = "issues";
                log.info("\n⚠️ 问题识别:");
                continue;
            } else if (line.contains("改进建议:")) {
                currentSection = "suggestions";
                log.info("\n💡 改进建议:");
                continue;
            } else if (line.contains("质量评分:")) {
                currentSection = "score";
                String score = line.substring(line.indexOf(":") + 1).trim();
                log.info("\n📊 质量评分: {}", score);
                continue;
            } else if (line.contains("是否通过:")) {
                currentSection = "pass";
                String status = line.substring(line.indexOf(":") + 1).trim();
                if (status.equals("PASS")) {
                    log.info("\n✅ 检查结果: 通过");
                } else if (status.equals("FAIL")) {
                    log.info("\n❌ 检查结果: 未通过");
                } else {
                    log.info("\n🔧 检查结果: 需要优化");
                }
                continue;
            }

            switch (currentSection) {
                case "assessment":
                    log.info("   📋 {}", line);
                    break;
                case "issues":
                    log.info("   ⚠️ {}", line);
                    break;
                case "suggestions":
                    log.info("   💡 {}", line);
                    break;
                default:
                    log.info("   📝 {}", line);
                    break;
            }
        }
    }
}
