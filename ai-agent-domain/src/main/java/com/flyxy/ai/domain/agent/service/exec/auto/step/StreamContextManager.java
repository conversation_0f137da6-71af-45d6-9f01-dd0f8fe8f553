package com.flyxy.ai.domain.agent.service.exec.auto.step;

import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.FluxSink;

import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;

/**
 * @Description: 流式上下文管理器，用于管理整个执行过程中的流式输出
 * @Author: flyxy
 * @Date: 2025/9/7
 */
@Slf4j
public class StreamContextManager {
    
    private final FluxSink<String> sink;
    private final AtomicBoolean isCompleted = new AtomicBoolean(false);
    private final AtomicReference<Flux<String>> currentFlux = new AtomicReference<>();
    
    public StreamContextManager(FluxSink<String> sink) {
        this.sink = sink;
    }
    
    /**
     * 添加新的流片段到主流
     * @param newStream 新的流片段
     * @param stepName 步骤名称，用于日志
     */
    public void appendStream(Flux<String> newStream, String stepName) {
        if (isCompleted.get()) {
            log.warn("StreamContextManager已完成，无法添加新流: {}", stepName);
            return;
        }
        
        log.debug("开始添加流片段: {}", stepName);
        
        newStream
            .doOnNext(content -> {
                if (!isCompleted.get()) {
                    sink.next(content);
                }
            })
            .doOnError(error -> {
                log.error("流片段{}执行出错: {}", stepName, error.getMessage(), error);
                if (!isCompleted.get()) {
                    sink.error(error);
                }
            })
            .doOnComplete(() -> {
                log.debug("流片段{}完成", stepName);
            })
            .subscribe();
    }
    
    /**
     * 添加同步内容到流
     * @param content 内容
     * @param stepName 步骤名称
     */
    public void appendContent(String content, String stepName) {
        if (isCompleted.get()) {
            log.warn("StreamContextManager已完成，无法添加内容: {}", stepName);
            return;
        }
        
        if (content != null && !content.isEmpty()) {
            log.debug("添加同步内容到流: {} - 内容长度: {}", stepName, content.length());
            sink.next(content);
        }
    }
    
    /**
     * 添加步骤开始标识
     * @param stepName 步骤名称
     * @param stepNumber 步骤编号
     */
    public void appendStepStart(String stepName, int stepNumber) {
        String stepHeader = String.format("\n🚀 === 开始执行 %s (第%d步) ===\n", stepName, stepNumber);
        appendContent(stepHeader, stepName + "-header");
    }
    
    /**
     * 添加步骤完成标识
     * @param stepName 步骤名称
     * @param stepNumber 步骤编号
     */
    public void appendStepComplete(String stepName, int stepNumber) {
        String stepFooter = String.format("\n✅ === %s 完成 (第%d步) ===\n", stepName, stepNumber);
        appendContent(stepFooter, stepName + "-footer");
    }
    
    /**
     * 添加错误信息到流
     * @param error 错误信息
     * @param stepName 步骤名称
     */
    public void appendError(String error, String stepName) {
        String errorContent = String.format("\n❌ === %s 执行出错 ===\n错误信息: %s\n", stepName, error);
        appendContent(errorContent, stepName + "-error");
    }
    
    /**
     * 完成流输出
     */
    public void complete() {
        if (isCompleted.compareAndSet(false, true)) {
            log.info("StreamContextManager完成流输出");
            sink.complete();
        }
    }
    
    /**
     * 流是否已完成
     */
    public boolean isCompleted() {
        return isCompleted.get();
    }
    
    /**
     * 发生错误时完成流
     * @param error 错误
     */
    public void completeWithError(Throwable error) {
        if (isCompleted.compareAndSet(false, true)) {
            log.error("StreamContextManager因错误完成: {}", error.getMessage(), error);
            sink.error(error);
        }
    }
}
