package com.flyxy.ai.domain.agent.service.exec.auto.step.factory;

import cn.bugstack.wrench.design.framework.tree.StrategyHandler;
import com.flyxy.ai.domain.agent.model.entity.ExecuteCommandEntity;
import com.flyxy.ai.domain.agent.model.valobj.AiAgentClientFlowConfigVO;
import com.flyxy.ai.domain.agent.service.exec.auto.step.RootNode;
import com.flyxy.ai.domain.agent.service.exec.auto.step.StreamContextManager;
import jakarta.annotation.Resource;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

import java.util.HashMap;
import java.util.Map;

/**
 * @Description:
 * @Author: flyxy
 * @Date: 2025/9/1
 */
@Slf4j
@Service
public class DefaultAutoAgentExecuteStrategyFactory {


    private final RootNode executeRootNode;

    public DefaultAutoAgentExecuteStrategyFactory(RootNode executeRootNode) {
        this.executeRootNode = executeRootNode;
    }

    public StrategyHandler<ExecuteCommandEntity, DynamicContext, String> armoryStrategyHandler(){
        return executeRootNode;
    }


    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class DynamicContext {
        private Flux<String> flux;

        // 流式上下文管理器
        private StreamContextManager streamContextManager;

        // 任务执行步骤
        private int step = 1;

        // 最大任务步骤
        private int maxStep = 1;

        private StringBuilder executionHistory;

        private String currentTask;

        boolean isCompleted = false;

        private Map<String, AiAgentClientFlowConfigVO> aiAgentClientFlowConfigVOMap;

        private Map<String, Object> dataObjects = new HashMap<>();

        public <T> void setValue(String key, T value) {
            dataObjects.put(key, value);
        }

        public <T> T getValue(String key) {
            return (T) dataObjects.get(key);
        }

        /**
         * 获取流式上下文管理器
         */
        public StreamContextManager getStreamManager() {
            return streamContextManager;
        }

        /**
         * 设置流式上下文管理器
         */
        public void setStreamManager(StreamContextManager streamContextManager) {
            this.streamContextManager = streamContextManager;
        }
    }
}
