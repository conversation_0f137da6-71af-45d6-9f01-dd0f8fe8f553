package com.flyxy.ai.domain.agent.service.exec.flow;

import com.flyxy.ai.domain.agent.model.entity.ExecuteCommandEntity;
import com.flyxy.ai.domain.agent.service.exec.IExecuteStrategy;
import reactor.core.publisher.Flux;
import reactor.core.publisher.FluxSink;

/**
 * @Description:
 * @Author: flyxy
 * @Date: 2025/9/1
 */
public class FlowExecuteStrategy implements IExecuteStrategy {
    @Override
    public void execute(ExecuteCommandEntity requestParameter) throws Exception {

    }

    @Override
    public void execute(ExecuteCommandEntity requestParameter, Flux<String> flux) throws Exception {

    }

    @Override
    public void execute(ExecuteCommandEntity requestParameter, FluxSink<String> sink) throws Exception {
        // 流式执行策略的实现
    }
}
