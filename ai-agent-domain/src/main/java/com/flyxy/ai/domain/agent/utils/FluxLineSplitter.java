package com.flyxy.ai.domain.agent.utils;

import reactor.core.publisher.Flux;

import java.util.ArrayList;
import java.util.List;

public class FluxLineSplitter {

    /**
     * 将 Flux<String> 按行切分输出（保留换行符）。
     *
     * @param flux 原始字符串流（可能是碎片化的 token）
     * @return 按行切分后的 Flux<String>
     */
    public static Flux<String> splitByNewline(Flux<String> flux) {
        return flux
            // 用 StringBuilder 做累积，处理碎片化 chunk
            .scanWith(StringBuilder::new, (sb, chunk) -> {
                sb.append(chunk);
                return sb;
            })
            // 把 StringBuilder 转换成 “已完成的行”
            .flatMapIterable(sb -> {
                List<String> lines = new ArrayList<>();
                int start = 0;
                for (int i = 0; i < sb.length(); i++) {
                    if (sb.charAt(i) == '\n') {
                        lines.add(sb.substring(start, i + 1));
                        start = i + 1;
                    }
                }
                if (start > 0) {
                    // 删除已输出的部分，保留未完成的行
                    sb.delete(0, start);
                }
                return lines;
            })
            // 过滤掉空字符串，避免多余事件
            .filter(line -> !line.isEmpty());
    }
}
