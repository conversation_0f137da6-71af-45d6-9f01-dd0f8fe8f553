package com.flyxy.ai.infrastructure.dao;

import com.flyxy.ai.infrastructure.dao.po.AiAgent;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * AI智能体配置表 DAO接口
 */
@Mapper
public interface IAiAgentDao {

    /**
     * 插入AI智能体配置
     * @param aiAgent AI智能体配置
     * @return 影响行数
     */
    int insert(AiAgent aiAgent);

    /**
     * 根据ID删除AI智能体配置
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteById(Long id);

    /**
     * 根据智能体ID删除AI智能体配置
     * @param agentId 智能体ID
     * @return 影响行数
     */
    int deleteByAgentId(String agentId);

    /**
     * 更新AI智能体配置
     * @param aiAgent AI智能体配置
     * @return 影响行数
     */
    int update(AiAgent aiAgent);

    /**
     * 根据ID查询AI智能体配置
     * @param id 主键ID
     * @return AI智能体配置
     */
    AiAgent queryById(Long id);

    /**
     * 根据智能体ID查询AI智能体配置
     * @param agentId 智能体ID
     * @return AI智能体配置
     */
    AiAgent queryByAgentId(@Param("agentId") String agentId);

    /**
     * 查询所有AI智能体配置
     * @return AI智能体配置列表
     */
    List<AiAgent> queryAll();

    /**
     * 根据状态查询AI智能体配置
     * @param status 状态
     * @return AI智能体配置列表
     */
    List<AiAgent> queryByStatus(Integer status);

}
