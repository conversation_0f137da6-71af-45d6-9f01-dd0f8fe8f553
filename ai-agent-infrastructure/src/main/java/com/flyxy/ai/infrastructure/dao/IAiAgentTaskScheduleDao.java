package com.flyxy.ai.infrastructure.dao;

import com.flyxy.ai.infrastructure.dao.po.AiAgentTaskSchedule;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 智能体任务调度配置表 DAO接口
 */
@Mapper
public interface IAiAgentTaskScheduleDao {

    /**
     * 插入智能体任务调度配置
     * @param aiAgentTaskSchedule 智能体任务调度配置
     * @return 影响行数
     */
    int insert(AiAgentTaskSchedule aiAgentTaskSchedule);

    /**
     * 根据ID删除智能体任务调度配置
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteById(Long id);

    /**
     * 根据智能体ID删除智能体任务调度配置
     * @param agentId 智能体ID
     * @return 影响行数
     */
    int deleteByAgentId(Long agentId);

    /**
     * 更新智能体任务调度配置
     * @param aiAgentTaskSchedule 智能体任务调度配置
     * @return 影响行数
     */
    int update(AiAgentTaskSchedule aiAgentTaskSchedule);

    /**
     * 根据ID查询智能体任务调度配置
     * @param id 主键ID
     * @return 智能体任务调度配置
     */
    AiAgentTaskSchedule queryById(Long id);

    /**
     * 根据智能体ID查询智能体任务调度配置
     * @param agentId 智能体ID
     * @return 智能体任务调度配置列表
     */
    List<AiAgentTaskSchedule> queryByAgentId(Long agentId);

    /**
     * 根据状态查询智能体任务调度配置
     * @param status 状态
     * @return 智能体任务调度配置列表
     */
    List<AiAgentTaskSchedule> queryByStatus(Integer status);

    /**
     * 查询所有智能体任务调度配置
     * @return 智能体任务调度配置列表
     */
    List<AiAgentTaskSchedule> queryAll();

}
