package com.flyxy.ai.infrastructure.dao;

import com.flyxy.ai.infrastructure.dao.po.AiClientAdvisor;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 顾问配置表 DAO接口
 */
@Mapper
public interface IAiClientAdvisorDao {

    /**
     * 插入顾问配置
     * @param aiClientAdvisor 顾问配置
     * @return 影响行数
     */
    int insert(AiClientAdvisor aiClientAdvisor);

    /**
     * 根据ID删除顾问配置
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteById(Long id);

    /**
     * 根据顾问ID删除顾问配置
     * @param advisorId 顾问ID
     * @return 影响行数
     */
    int deleteByAdvisorId(String advisorId);

    /**
     * 更新顾问配置
     * @param aiClientAdvisor 顾问配置
     * @return 影响行数
     */
    int update(AiClientAdvisor aiClientAdvisor);

    /**
     * 根据ID查询顾问配置
     * @param id 主键ID
     * @return 顾问配置
     */
    AiClientAdvisor queryById(Long id);

    /**
     * 根据顾问ID查询顾问配置
     * @param advisorId 顾问ID
     * @return 顾问配置
     */
    AiClientAdvisor queryByAdvisorId(String advisorId);

    /**
     * 根据顾问类型查询顾问配置
     * @param advisorType 顾问类型
     * @return 顾问配置列表
     */
    List<AiClientAdvisor> queryByAdvisorType(String advisorType);

    /**
     * 根据状态查询顾问配置
     * @param status 状态
     * @return 顾问配置列表
     */
    List<AiClientAdvisor> queryByStatus(Integer status);

    /**
     * 查询所有顾问配置
     * @return 顾问配置列表
     */
    List<AiClientAdvisor> queryAll();

}
