package com.flyxy.ai.infrastructure.dao;

import com.flyxy.ai.infrastructure.dao.po.AiClientApi;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * OpenAI API配置表 DAO接口
 */
@Mapper
public interface IAiClientApiDao {

    /**
     * 插入OpenAI API配置
     * @param aiClientApi OpenAI API配置
     * @return 影响行数
     */
    int insert(AiClientApi aiClientApi);

    /**
     * 根据ID删除OpenAI API配置
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteById(Long id);

    /**
     * 根据API ID删除OpenAI API配置
     * @param apiId API ID
     * @return 影响行数
     */
    int deleteByApiId(String apiId);

    /**
     * 更新OpenAI API配置
     * @param aiClientApi OpenAI API配置
     * @return 影响行数
     */
    int update(AiClientApi aiClientApi);

    /**
     * 根据ID查询OpenAI API配置
     * @param id 主键ID
     * @return OpenAI API配置
     */
    AiClientApi queryById(Long id);

    /**
     * 根据API ID查询OpenAI API配置
     * @param apiId API ID
     * @return OpenAI API配置
     */
    AiClientApi queryByApiId(String apiId);

    /**
     * 根据状态查询OpenAI API配置
     * @param status 状态
     * @return OpenAI API配置列表
     */
    List<AiClientApi> queryByStatus(Integer status);

    /**
     * 查询所有OpenAI API配置
     * @return OpenAI API配置列表
     */
    List<AiClientApi> queryAll();

}
