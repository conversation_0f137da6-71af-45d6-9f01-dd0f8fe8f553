package com.flyxy.ai.infrastructure.dao;

import com.flyxy.ai.infrastructure.dao.po.AiClientConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * AI客户端统一关联配置表 DAO接口
 */
@Mapper
public interface IAiClientConfigDao {

    /**
     * 插入AI客户端统一关联配置
     * @param aiClientConfig AI客户端统一关联配置
     * @return 影响行数
     */
    int insert(AiClientConfig aiClientConfig);

    /**
     * 根据ID删除AI客户端统一关联配置
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteById(Long id);

    /**
     * 根据源ID删除AI客户端统一关联配置
     * @param sourceId 源ID
     * @return 影响行数
     */
    int deleteBySourceId(String sourceId);

    /**
     * 更新AI客户端统一关联配置
     * @param aiClientConfig AI客户端统一关联配置
     * @return 影响行数
     */
    int update(AiClientConfig aiClientConfig);

    /**
     * 根据ID查询AI客户端统一关联配置
     * @param id 主键ID
     * @return AI客户端统一关联配置
     */
    AiClientConfig queryById(Long id);

    /**
     * 根据源ID查询AI客户端统一关联配置
     * @param sourceId 源ID
     * @return AI客户端统一关联配置列表
     */
    List<AiClientConfig> queryBySourceId(String sourceId);

    /**
     * 根据目标ID查询AI客户端统一关联配置
     * @param targetId 目标ID
     * @return AI客户端统一关联配置列表
     */
    List<AiClientConfig> queryByTargetId(String targetId);

    /**
     * 根据源类型和源ID查询AI客户端统一关联配置
     * @param sourceType 源类型
     * @param sourceId 源ID
     * @return AI客户端统一关联配置列表
     */
    List<AiClientConfig> queryBySourceTypeAndId(@Param("sourceType") String sourceType, @Param("sourceId") String sourceId);

    /**
     * 根据状态查询AI客户端统一关联配置
     * @param status 状态
     * @return AI客户端统一关联配置列表
     */
    List<AiClientConfig> queryByStatus(Integer status);

    /**
     * 查询所有AI客户端统一关联配置
     * @return AI客户端统一关联配置列表
     */
    List<AiClientConfig> queryAll();

}
