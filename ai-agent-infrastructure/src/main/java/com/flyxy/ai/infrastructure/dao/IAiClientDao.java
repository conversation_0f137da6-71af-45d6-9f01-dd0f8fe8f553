package com.flyxy.ai.infrastructure.dao;

import com.flyxy.ai.infrastructure.dao.po.AiClient;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * AI客户端配置表 DAO接口
 */
@Mapper
public interface IAiClientDao {

    /**
     * 插入AI客户端配置
     * @param aiClient AI客户端配置
     * @return 影响行数
     */
    int insert(AiClient aiClient);

    /**
     * 根据ID删除AI客户端配置
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteById(Long id);

    /**
     * 根据客户端ID删除AI客户端配置
     * @param clientId 客户端ID
     * @return 影响行数
     */
    int deleteByClientId(String clientId);

    /**
     * 更新AI客户端配置
     * @param aiClient AI客户端配置
     * @return 影响行数
     */
    int update(AiClient aiClient);

    /**
     * 根据ID查询AI客户端配置
     * @param id 主键ID
     * @return AI客户端配置
     */
    AiClient queryById(Long id);

    /**
     * 根据客户端ID查询AI客户端配置
     * @param clientId 客户端ID
     * @return AI客户端配置
     */
    AiClient queryByClientId(String clientId);

    /**
     * 查询所有AI客户端配置
     * @return AI客户端配置列表
     */
    List<AiClient> queryAll();

    /**
     * 根据状态查询AI客户端配置
     * @param status 状态
     * @return AI客户端配置列表
     */
    List<AiClient> queryByStatus(Integer status);

}
