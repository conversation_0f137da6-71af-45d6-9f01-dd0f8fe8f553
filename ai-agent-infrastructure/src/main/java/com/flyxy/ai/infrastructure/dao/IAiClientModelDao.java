package com.flyxy.ai.infrastructure.dao;

import com.flyxy.ai.infrastructure.dao.po.AiClientModel;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 聊天模型配置表 DAO接口
 */
@Mapper
public interface IAiClientModelDao {

    /**
     * 插入聊天模型配置
     * @param aiClientModel 聊天模型配置
     * @return 影响行数
     */
    int insert(AiClientModel aiClientModel);

    /**
     * 根据ID删除聊天模型配置
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteById(Long id);

    /**
     * 根据模型ID删除聊天模型配置
     * @param modelId 模型ID
     * @return 影响行数
     */
    int deleteByModelId(String modelId);

    /**
     * 更新聊天模型配置
     * @param aiClientModel 聊天模型配置
     * @return 影响行数
     */
    int update(AiClientModel aiClientModel);

    /**
     * 根据ID查询聊天模型配置
     * @param id 主键ID
     * @return 聊天模型配置
     */
    AiClientModel queryById(Long id);

    /**
     * 根据模型ID查询聊天模型配置
     * @param modelId 模型ID
     * @return 聊天模型配置
     */
    AiClientModel queryByModelId(String modelId);

    /**
     * 根据API ID查询聊天模型配置
     * @param apiId API ID
     * @return 聊天模型配置列表
     */
    List<AiClientModel> queryByApiId(String apiId);

    /**
     * 根据模型类型查询聊天模型配置
     * @param modelType 模型类型
     * @return 聊天模型配置列表
     */
    List<AiClientModel> queryByModelType(String modelType);

    /**
     * 根据状态查询聊天模型配置
     * @param status 状态
     * @return 聊天模型配置列表
     */
    List<AiClientModel> queryByStatus(Integer status);

    /**
     * 查询所有聊天模型配置
     * @return 聊天模型配置列表
     */
    List<AiClientModel> queryAll();

}
