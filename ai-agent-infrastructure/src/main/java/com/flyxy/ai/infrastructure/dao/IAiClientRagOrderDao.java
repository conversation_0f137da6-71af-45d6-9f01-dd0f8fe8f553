package com.flyxy.ai.infrastructure.dao;

import com.flyxy.ai.infrastructure.dao.po.AiClientRagOrder;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 知识库配置表 DAO接口
 */
@Mapper
public interface IAiClientRagOrderDao {

    /**
     * 插入知识库配置
     * @param aiClientRagOrder 知识库配置
     * @return 影响行数
     */
    int insert(AiClientRagOrder aiClientRagOrder);

    /**
     * 根据ID删除知识库配置
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteById(Long id);

    /**
     * 根据知识库ID删除知识库配置
     * @param ragId 知识库ID
     * @return 影响行数
     */
    int deleteByRagId(String ragId);

    /**
     * 更新知识库配置
     * @param aiClientRagOrder 知识库配置
     * @return 影响行数
     */
    int update(AiClientRagOrder aiClientRagOrder);

    /**
     * 根据ID查询知识库配置
     * @param id 主键ID
     * @return 知识库配置
     */
    AiClientRagOrder queryById(Long id);

    /**
     * 根据知识库ID查询知识库配置
     * @param ragId 知识库ID
     * @return 知识库配置
     */
    AiClientRagOrder queryByRagId(String ragId);

    /**
     * 根据知识标签查询知识库配置
     * @param knowledgeTag 知识标签
     * @return 知识库配置列表
     */
    List<AiClientRagOrder> queryByKnowledgeTag(String knowledgeTag);

    /**
     * 根据状态查询知识库配置
     * @param status 状态
     * @return 知识库配置列表
     */
    List<AiClientRagOrder> queryByStatus(Integer status);

    /**
     * 查询所有知识库配置
     * @return 知识库配置列表
     */
    List<AiClientRagOrder> queryAll();

}
