<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.flyxy.ai</groupId>
        <artifactId>ai-agent</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>

    <artifactId>ai-agent-trigger</artifactId>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-tx</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>

        <!-- 系统模块 -->
        <dependency>
            <groupId>com.flyxy.ai</groupId>
            <artifactId>ai-agent-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.flyxy.ai</groupId>
            <artifactId>ai-agent-types</artifactId>
        </dependency>
        <dependency>
            <groupId>com.flyxy.ai</groupId>
            <artifactId>ai-agent-domain</artifactId>
        </dependency>
    </dependencies>

    <build>
        <finalName>ai-agent-trigger</finalName>
    </build>

</project>
