package com.flyxy.ai.trigger.http;

import com.alibaba.fastjson.JSON;
import com.flyxy.ai.api.IAiAgentService;
import com.flyxy.ai.api.dto.AutoAgentRequestDTO;
import com.flyxy.ai.domain.agent.model.entity.ExecuteCommandEntity;
import com.flyxy.ai.domain.agent.service.exec.IExecuteStrategy;
import com.flyxy.ai.domain.agent.service.exec.auto.step.factory.DefaultAutoAgentExecuteStrategyFactory;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * @Description:
 * @Author: flyxy
 * @Date: 2025/9/2
 */
@Slf4j
@Controller
@RequestMapping("/api/v1/agent")
@CrossOrigin(origins = "*", allowedHeaders = "*", methods = {RequestMethod.GET, RequestMethod.POST, RequestMethod.OPTIONS})
public class AiAgentController implements IAiAgentService {

    @Resource
    private ThreadPoolExecutor threadPoolExecutor;
    @Resource
    private IExecuteStrategy autoExecuteStrategy;

    @Override
    @PostMapping("auto_agent")
    public Flux<String> autoAgent(@RequestBody AutoAgentRequestDTO request, HttpServletResponse response) {
        log.info("AutoAgent流式执行请求开始，请求信息：{}", JSON.toJSONString(request));

        try {
            // 设置响应格式
            response.setContentType("text/event-stream");
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Cache-Control", "no-cache");
            response.setHeader("Connection", "keep-alive");
            Flux<String> flux = Flux.empty();

            // 异步执行agent对话
            autoExecuteStrategy.execute(ExecuteCommandEntity.builder()
                    .aiAgentId(request.getAiAgentId())
                    .message(request.getMessage())
                    .sessionId(request.getSessionId())
                    .maxStep(request.getMaxStep())
                    .build(), flux);

            return flux;


        } catch (Exception e) {
            log.error("AutoAgent流式执行异常", e);
            throw new RuntimeException(e);
        }

        return null;
    }
}
