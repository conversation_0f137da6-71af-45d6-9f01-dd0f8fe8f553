# /usr/local/bin/docker-compose -f /docs/dev-ops/environment/environment-docker-compose-2.4.yml up -d
version: '3.8'
# docker-compose -f docker-compose-app.yml up -d
# 你需要修改system为你自身系统的仓库名
services:
  # MCP 服务，CSDN 发帖，注意 CSDN_API_COOKIE 修改为你的。
  mcp-server-csdn-app:
    # 课程代码：https://gitcode.net/KnowledgePlanet/mcp-server-csdn
    #    image: fuzhengwei/mcp-server-csdn-app:1.1
    image: registry.cn-hangzhou.aliyuncs.com/fuzhengwei/mcp-server-csdn-app:1.1
    container_name: mcp-server-csdn-app
    restart: always
    ports:
      - "8101:8101"
    volumes:
      - ./log:/data/log
    environment:
      - TZ=PRC
      - SERVER_PORT=8101
      - CSDN_API_CATEGORIES=Java场景面试宝典
      - CSDN_API_COOKIE=uuid_tt_dd=10_10071826050-1734915373749-615754
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    networks:
      - ai-agent

  # 课程代码：https://gitcode.net/KnowledgePlanet/mcp-server-weixin
  # MCP 服务，微信公众号通知，注意 environment 下的微信配置修改为你的 https://mp.weixin.qq.com/debug/cgi-bin/sandboxinfo?action=showinfo&t=sandbox/index
  mcp-server-weixin-app:
    #    image: fuzhengwei/mcp-server-weixin-app:1.1
    image: registry.cn-hangzhou.aliyuncs.com/fuzhengwei/mcp-server-weixin-app:1.1
    container_name: mcp-server-weixin-app
    restart: always
    ports:
      - "8102:8102"
    volumes:
      - ./log:/data/log
    environment:
      - TZ=PRC
      - SERVER_PORT=8102
      - WEIXIN_API_ORIGINAL_ID=gh_637303628105
      - WEIXIN_API_APP_ID=wx0eff35a3acff50d1
      - WEIXIN_API_APP_SECRET=a9565a722c5ed1c79020d441b340d81b
      - WEIXIN_API_TEMPLATE_ID=jDI_HBSZmsgsGi0azoyPZNR350wB58gMujafyM5n8vs
      - WEIXIN_API_TOUSER=ooVSovgyBNmA9xzwzVW3cg4Y35dU
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    networks:
      - ai-agent

networks:
  ai-agent:
    driver: bridge
